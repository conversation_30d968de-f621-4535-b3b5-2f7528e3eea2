package com.timevale.faceauth.service.domain.repository;

import com.timevale.faceauth.dal.pfs.face.support.UserPhotoDAO;
import com.timevale.faceauth.dal.pfs.face.support.UserPhotoDO;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.mandarin.base.util.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/31 17
 */
@Deprecated
@Repository
public class UserPhotoRepository {

  private final UserPhotoDAO userPhotoDAO;

  @Autowired
  public UserPhotoRepository(UserPhotoDAO userPhotoDAO) {
    this.userPhotoDAO = userPhotoDAO;
  }

  public void saveUserPhoto(FaceLocalLibraryPhoto photoEntity) throws FaceException {

    UserPhoto photo =
            UserPhoto.createBuilder()
                    .setPhoto(photoEntity.getPhoto())
                    .setPhotoType(photoEntity.getPhotoType())
                    .setIdNo(photoEntity.getIdNo())
                    .setName(photoEntity.getName())
                    .build();
    UserPhotoDO entity = photo.toEntity();
    int rows;
    try {
      rows = userPhotoDAO.insert(entity);
    } catch (Exception cause) {
      String msg;
      try {
        msg = JsonUtils.obj2json(entity);
      } catch (Exception ignore) {
        msg = null;
      }
      if (null == msg) {
        throw FaceException.valueOf(
            FaceStatusCode.RESOURCE_FAILED_DATASOURCE, "Fail persistence to db .", cause);
      } else {
        throw FaceException.valueOf(
            FaceStatusCode.RESOURCE_FAILED_DATASOURCE, "Fail persistence on data[" + msg + "] .", cause);
      }
    }
    if (0 >= rows) {
      String msg;
      try {
        msg = JsonUtils.obj2json(entity);
      } catch (Exception ignore) {
        msg = null;
      }
      if (null == msg) {
        throw FaceException.valueOf(FaceStatusCode.RESOURCE_FAILED_DATASOURCE, "Fail persistence to db .");
      } else {
        throw FaceException.valueOf(
            FaceStatusCode.RESOURCE_FAILED_DATASOURCE, "Fail persistence on data[" + msg + "] .");
      }
    }
  }



  public UserPhoto getPhotoByFactor2(String hashing) throws FaceException {

    UserPhotoDO entity;
    try {
      entity = userPhotoDAO.getLatestPhotoByFactor2(hashing);
    } catch (Exception cause) {
      throw FaceException.valueOf(
          FaceStatusCode.RESOURCE_FAILED_DATASOURCE,
          "Fail get photo on factor2[" + hashing+ "] .",
          cause);
    }
    return (null == entity ? null : UserPhoto.valueOf(entity));
  }


  public boolean clearUserPhoto(String idNo) throws FaceException {
    int count;
    try {
      count = userPhotoDAO.delUserPhoto(idNo);
    } catch (Exception cause) {
      throw FaceException.valueOf(
              FaceStatusCode.RESOURCE_FAILED_DATASOURCE, "Fail del photo on idNo[" + idNo + "] .", cause);
    }
    return count > 0;
  }
}
