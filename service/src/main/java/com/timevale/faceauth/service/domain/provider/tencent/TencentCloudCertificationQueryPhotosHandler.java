package com.timevale.faceauth.service.domain.provider.tencent;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.provider.support.RestTemplateRequestResolver;
import com.timevale.faceauth.service.utils.UrlUtil;
import com.timevale.mandarin.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.net.URI;

/**
 * 查询腾讯云多张照片
 * <AUTHOR>
 * @copyright 2022
 * @date 2022/3/25
 */
@Slf4j
@Component
public class TencentCloudCertificationQueryPhotosHandler
    extends TencentCloudFaceInvocationErrorHandler
    implements TencentCloudCertificationSignatureResolver<TencentCloudCertificationQueryPhotosRequest> {


  private final ConfigurableProperties properties;
  private final RestTemplateRequestResolver requestResolver;


  @Autowired
  public TencentCloudCertificationQueryPhotosHandler(
      ConfigurableProperties properties,
      RestTemplateRequestResolver requestResolver) {
    this.properties = properties;
    this.requestResolver = requestResolver;
  }

  public TencentCloudCertificationQueryPhotosResponse invoke(
          String faceId, String orderNo, TencentWebAppIdVersion appIdVersion)
      throws FaceException {
    TencentCloudCertificationQueryPhotosRequest request = prepareRequestQueryString(faceId, orderNo, appIdVersion);
    String uriStr = properties.getTencentCloudCertificationQueryPhotos();
    uriStr = UrlUtil.appendParam(uriStr, "orderNo", request.getOrderNo());

    URI uri = requestResolver.resolveURI(uriStr);
    RequestEntity<TencentCloudCertificationQueryPhotosRequest> requestEntity =
            (new RequestEntity<>(request, HttpMethod.POST, uri));

    log.info("tencent cloud query photo request param {},faceId={}", uriStr, faceId);

    ResponseEntity<String> responseEntity =
        requestResolver.resolveResponse(
                faceId,
                appIdVersion.getProviderName(),
            requestEntity,
            String.class);
    return detectAuthorizationResult(responseEntity, requestEntity);
  }

  private TencentCloudCertificationQueryPhotosRequest prepareRequestQueryString(String faceId, String orderNo, TencentWebAppIdVersion appIdVersion) throws FaceException {
    TencentCloudCertificationQueryPhotosRequest request =
            TencentCloudCertificationQueryPhotosRequest.createBuilder()
            .setAppId(appIdVersion.getAccessHolder().getWebAppId())
            .setOrderNo(orderNo)
            .setSignatureResolver(this)
            .setTicket(TencentCloudUtil.getTicketCache(faceId,  appIdVersion))
            .build();
    request.signatureRequest();
    return request;
  }



  @Override
  public String resolveSignature(TencentCloudCertificationQueryPhotosRequest request)
      throws FaceException {
    return TencentCloudUtil.signature(
        request.getWebankAppId(),
        request.getNonce(),
        request.getOrderNo(),
        request.getVersion(),
        request.getTicket());
  }

  private TencentCloudCertificationQueryPhotosResponse detectAuthorizationResult(
      ResponseEntity<String> responseEntity,
      RequestEntity<TencentCloudCertificationQueryPhotosRequest> requestEntity)
      throws FaceException {
    String content = responseEntity.getBody();

    TencentCloudCertificationQueryPhotosResponse response;
    try {
      response = JsonUtils.json2pojo(content, TencentCloudCertificationQueryPhotosResponse.class);
    } catch (Exception cause) {
      log.error(
          "Error response content["
              + content
              + "] on api["
              + requestEntity.getUrl().toString()
              + "]",
          cause);
      throw FaceException.valueOf(FaceStatusCode.PROVIDER_ERROR_API_RESULT_SYNTAX);
    }
   return response;
  }



}
