package com.timevale.faceauth.service.domain.provider.support;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.service.constant.SystemConfig;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.FaceAuthorizationResourceResolver;
import com.timevale.faceauth.service.domain.FaceVideoResource;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.domain.repository.FaceOSSResources;
import com.timevale.faceauth.service.domain.repository.conv.UserFacePhotoResourceConv;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 刷脸资源处理
 *
 * <AUTHOR>
 * @since 2020-08-31 00:34
 */
@Slf4j
@Component
public class FaceResourcesInvocationHandler {


  @Autowired
  private FaceAuthorizationResourceResolver resourceResolver;

  /**
   * 持久化刷脸的照片
   * @param faceResources
   * @param faceInfo
   */
  public FaceOSSResources persistentFaceResources(
          FaceBase64Resources faceResources, FaceInfo faceInfo) {
    long startTimeFile = System.currentTimeMillis();
    String faceId = faceInfo.getFaceId();
    FaceOSSResources ossResources = new FaceOSSResources();
    try {

      FacePhotoResource photoResource = detectUserFacePhotoResource(faceResources.getPhoto(), faceInfo);
      FaceVideoResource videoResource = detectFaceVideoResource(faceResources.getVideo(), faceInfo);
      FaceIdCardFrontResource idCardFrontResource = detectFaceIdCardFrontResource(faceResources.getIdCardFront(), faceInfo);
      FaceIdCardBackResource idCardBackResource = detectFaceIdCardBackResource(faceResources.getIdCardBack(), faceInfo);

      if(photoResource != null){
        ossResources.setPhoto(photoResource.getResourceContent());
        ossResources.setPhotoType(photoResource.getMimeType());
      }
      ossResources.setVideo(null != videoResource ? videoResource.getResourceContent() : null);
      ossResources.setIdCardFront(null != idCardFrontResource ? idCardFrontResource.getResourceContent() : null);
      ossResources.setIdCardBack(null != idCardBackResource ? idCardBackResource.getResourceContent() : null);
      resourceResolver.saveFaceResource(videoResource, photoResource, idCardFrontResource, idCardBackResource);

      return ossResources;
      // resourceResolver.saveUserPhoto(resourceWrap.getPhotoResource());
    } catch (Exception cause) {
      throw FaceException.valueOf(FaceStatusCode.RESOURCE_FAIL_PERSISTENCE, cause);
    } finally {
      log.info("FaceOSSResources persistent faceId = {},  cost: {} ms", faceId,
              (System.currentTimeMillis()-startTimeFile));
    }

  }


  /**
   * 保存到本地库照源
   *
   * @param faceInfo
   * @param resources
   */
  public void saveUserPhoto(FaceInfo faceInfo, FaceOSSResources resources){
    if(faceInfo == null
            ||  resources == null){
         return;
    }
    if(com.timevale.mandarin.base.util.StringUtils.isBlank(resources.getPhoto())){
      return;
    }
    resourceResolver.saveUserPhoto(UserFacePhotoResourceConv.conv(faceInfo, resources));
  }

  private FacePhotoResource detectUserFacePhotoResource(
          String photo, FaceInfo faceInfo) {
    if (StringUtils.isEmpty(photo)) {
      return null;
    }
    String faceId = faceInfo.getFaceId();
    String photoKey = resourceResolver.savePhoto(faceId, photo);
    if (StringUtils.isEmpty(photoKey)) {
      return null;
    }

    return  FacePhotoResource.createBuilder()
            .setProvider(faceInfo.getProvider())
            .setFaceId(faceId)
            .setResourceContent(photoKey)
            .build();
  }

  private FaceVideoResource detectFaceVideoResource(
          String video, FaceInfo faceInfo) {
    if (StringUtils.isEmpty(video)) {
      return null;
    }
    String faceId = faceInfo.getFaceId();
    String videoKey = resourceResolver.saveVideo(faceId, video);
    if (StringUtils.isEmpty(videoKey)) {
      return null;
    }

    return FaceVideoResource.createBuilder()
            .setProvider(faceInfo.getProvider())
            .setFaceId(faceId)
            .setResourceContent(videoKey)
            .build();
  }

  private FaceIdCardFrontResource detectFaceIdCardFrontResource(
      String idCardFront, FaceInfo faceInfo) {
    if (StringUtils.isEmpty(idCardFront)) {
      return null;
    }
    String faceId = faceInfo.getFaceId();
    String idCardFrontKey = resourceResolver.savePhoto(faceId, idCardFront);
    if (StringUtils.isEmpty(idCardFrontKey)) {
      return null;
    }

    return  FaceIdCardFrontResource.createBuilder()
        .setProvider(faceInfo.getProvider())
        .setFaceId(faceId)
        .setResourceContent(idCardFrontKey)
        .build();
  }

  private FaceIdCardBackResource detectFaceIdCardBackResource(
      String idCardBack, FaceInfo faceInfo) {
    if (StringUtils.isEmpty(idCardBack)) {
      return null;
    }
    String faceId = faceInfo.getFaceId();
    String idCardBackKey = resourceResolver.savePhoto(faceId, idCardBack);
    if (StringUtils.isEmpty(idCardBackKey)) {
      return null;
    }

    return  FaceIdCardBackResource.createBuilder()
        .setProvider(faceInfo.getProvider())
        .setFaceId(faceId)
        .setResourceContent(idCardBackKey)
        .build();
  }


}
