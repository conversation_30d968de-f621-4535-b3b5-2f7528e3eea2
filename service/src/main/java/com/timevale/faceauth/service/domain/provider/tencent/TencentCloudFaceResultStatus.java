package com.timevale.faceauth.service.domain.provider.tencent;

import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.exception.ProviderErrorEnum;
import com.timevale.mandarin.base.util.StringUtils;

/**
 * 微众刷脸错误码
 *
 * <AUTHOR>
 * @since 2020-11-09 23:16
 */
public enum  TencentCloudFaceResultStatus implements ProviderErrorEnum {

  // 活体通用返回码
  SUCCESS(0,true,true,"成功",FaceStatusCode.OK),
  SYSTEM_ERROR(4,true,false,"系统错误", FaceStatusCode.PROVIDER_FAILED_API),
  INTERFACE_ERROR(1001, true,false,"调用活体引擎接口出错", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  MAYBE_NOT_PERSON(1002, true,false,"疑似非真人录制", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  VIDEO_PERSON_NOT_PASS(1003, true,false,"视频实人（真人）检测未通过", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  FACE_CHECK_FAIL(1004, true,false,"人脸检测失败，无法提取比对照", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  PROVIDER_FAILED_API(1005, true,false,"活体检测未通过", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  IDENTITY_PHOTO_ERROR(1006, true,false,"证照库出错，请重试", FaceStatusCode.PROVIDER_FAILED_API),

  // 数字活体返回码
  DIGIT_1101(1101, true,false,"未检测到声音", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  DIGIT_1102(
      1102, true,true,"脸部未完整露出", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),
  DIGIT_1103(1103, true,false,"声音识别失败", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  DIGIT_1104(1104, true,false,"视频格式有误", FaceStatusCode.LIVENESS_VIDEO_FORMAT_FAIL),
  DIGIT_1105(1105, true,false,"视频拉取失败，请重试", FaceStatusCode.LIVENESS_VIDEO_FORMAT_FAIL),
  DIGIT_1106(1106, true,false,"视频声音太小", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  DIGIT_1107(
      1107, true,false,"视频为空，或大小不合适，请控制录制时长在6s左右", FaceStatusCode.LIVENESS_VIDEO_FORMAT_FAIL),
  DIGIT_1108(
      1108, true,false,"视频像素太低", FaceStatusCode.LIVENESS_RESOLUTION_CHECK_NOT_PASS),
  DIGIT_1109(1109, true,false,"嘴唇动作幅度过小", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),

  //动作活体返回码
  ACTION_1201(1201, true,false,"光线太暗", FaceStatusCode.LIVENESS_BRIGHTNESS_CHECK_NOT_PASS),
  ACTION_1202(1202, true,false,"光线太强", FaceStatusCode.LIVENESS_BRIGHTNESS_CHECK_NOT_PASS),
  ACTION_1203(1203, true,false,"脸离屏幕太近", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),
  ACTION_1204(1204, true,false,"脸离屏幕太右", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),
  ACTION_1205(1205, true,false,"脸离屏幕太远", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),
  ACTION_1206(1206, true,false,"脸离屏幕太左", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),
  ACTION_1207(1207, true,false,"未检测到闭眼动作", FaceStatusCode.LIVENESS_EYE_BLINK_NOT_FOUND),
  ACTION_1208(1208, true,false,"未检测到第一个动作", FaceStatusCode.LIVENESS_FIRST_ACTION_NOT_FOUND),
  ACTION_1209(1209, true,false,"未检测到张嘴动作", FaceStatusCode.LIVENESS_MOUTH_OPEN_NOT_FOUND),
  ACTION_1210(1210, true,false,"未能检测到完整人脸", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),

  //静默活体返回码
  SILENT_1301(1301, true,false,"实人检测失败", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  SILENT_1302(1302, true,false,"实人检测未达到通过标准", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  SILENT_1303(1303, true,false,"视频录制时间过短，请录制2秒以上的视频", FaceStatusCode.LIVENESS_VIDEO_TOO_SHORT),


  //证件图像比对返回码
  IDENTITY_2001(2001, true,false,"调用比对引擎接口出错", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  DENTITY_2002(2002, true,false,"输入的姓名有误", FaceStatusCode.IDENTITY_PHOTO_NAME_IDNO_ERROR),
  DENTITY_2003(2003, true,false,"输入的身份证号有误", FaceStatusCode.IDENTITY_PHOTO_NAME_IDNO_ERROR),
  DENTITY_2004(2004, true,false,"传入图片过大或过小", FaceStatusCode.IDENTITY_PHOTO_SIZE_NOT_STANDARD),
  IDENTITY_2005(2005, true,false,"库中无此号，请到户籍所在地进行核实", FaceStatusCode.IDENTITY_PHOTO_SOURCE_NOT_FOUND),
  DENTITY_2006(2006, true,true,"姓名和身份证号不一致，请核实后重试", FaceStatusCode.IDENTITY_PHOTO_NAME_IDNO_ERROR),
  DENTITY_2007(2007, true,true,"库中无此用户照片，请到户籍所在地进行核实", FaceStatusCode.IDENTITY_PHOTO_PHOTO_NOT_FOUND),
  DENTITY_2008(2008, true,false,"客户库自建库或认证中心返照失败，请稍后再试", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  IDENTITY_2009(2009, true,true,"传入图片分辨率太低，请重新上传", FaceStatusCode.IDENTITY_PHOTO_RESOLUTION_NOT_PASS),
  DENTITY_2010(2010, true,true,"人脸检测失败，无法提取比对照", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  DENTITY_2011(2011, true,true,"实人比对未通过", FaceStatusCode.IDENTITY_PHOTO_CHECK_FAIL),
  DENTITY_2012(2012, true,true,"检测到多张人脸", FaceStatusCode.LIVENESS_FOUND_MULTI_FACE),
  IDENTITY_2013(2013, true,true,"未能检测到完整人脸", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),
  DENTITY_2014(2014, true,true,"传入图片分辨率太低，请重新上传", FaceStatusCode.IDENTITY_PHOTO_RESOLUTION_NOT_PASS),
  DENTITY_2015(2015, true,true,"比对失败", FaceStatusCode.IDENTITY_PHOTO_CHECK_FAIL),
  DENTITY_2016(2016, true,true,"比对相似度未达到通过标准", FaceStatusCode.IDENTITY_PHOTO_CHECK_FAIL),

  //请使用其它验证方案
  IDENTITY_3001(3001, true,false,"该浏览器不支持视频录制", FaceStatusCode.FACE_BROWSER_NOT_SUPPORT_VIDEO_RECORD),
  //重新进入
  IDENTITY_3002(3002, true,false,"登录态异常，cookie 参数缺失", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  //重新进入
  IDENTITY_3003(3003, true,false,"人脸核身中途中断", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  //重新进入并授权摄像头
  IDENTITY_3004(3004, true,false,"无摄像头权限", FaceStatusCode.RESOURCE_CAMERA_PERMISSION_NOT_ENABLE),
  // 请使用其它浏览器
  IDENTITY_3005(3005, true,false,"该浏览器不支持实时检测模式", FaceStatusCode.FACE_BROWSER_NOT_SUPPORT_VIDEO_RECORD),
  //重新进入
  IDENTITY_300101(300101, true,false,"报文包体问题", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),


  //其他返回码
  OTHER_400101(400101, true,false,"签名校验不通过", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  OTHER_400102(400102, true,false,"登录态失效", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  //400103,IP未加入白名单列表,ip:**************
  OTHER_400103(400103, true,false,"服务器拒绝访问此接口", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  OTHER_400104(400104, true,false,"服务器拒绝访问此接口", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  OTHER_400502(400502, true,false,"请求上送版本参数错误", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  OTHER_400504(400504, true,false,"请求访问频率过高", FaceStatusCode.PROVIDER_REQUEST_TOO_MUCH),
  OTHER_400506(400506, true,false,"请求频率过高,请稍后再试", FaceStatusCode.PROVIDER_REQUEST_TOO_MUCH),
  // 上传的视频非实时录制
  OTHER_400601(400601, true, false, "视频或图片异常", FaceStatusCode.RESOURCE_VIDEO_NOT_REAL_TIME),
  OTHER_400604(400604, true,false,"上送视频非实时录制", FaceStatusCode.PROVIDER_VIDEO_NOT_ALWAYS),
  OTHER_66660001(66660001, true,false,"不合法请求", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  OTHER_66660002(66660002, true,false,"服务已过有效期", FaceStatusCode.PROVIDER_FAILED_API),
  OTHER_66660003(66660003, true,false,"试用版最大刷脸次数已超", FaceStatusCode.PROVIDER_NOT_FEE),
  OTHER_66660004(66660004, true,true,"无法确认为同一人", FaceStatusCode.IDENTITY_PHOTO_CHECK_FAIL),

  OTHER_66660005(66660005, true,false,"权威数据源无照片", FaceStatusCode.IDENTITY_PHOTO_PHOTO_NOT_FOUND),
  OTHER_66660010(66660010, true,true,"姓名和身份证不一致，请确认", FaceStatusCode.IDENTITY_PHOTO_NAME_IDNO_ERROR),
  OTHER_66660011(66660011, false,false,"无此查询结果", FaceStatusCode.UNCOMPLETED_AUTHORIZATION),
  OTHER_66660015(66660015, true,false,"姓名或身份证不合法", FaceStatusCode.IDENTITY_PHOTO_NAME_IDNO_ERROR),
  OTHER_66660016(66660016, true,false,"视频格式或大小不合法", FaceStatusCode.LIVENESS_VIDEO_FORMAT_FAIL),
  OTHER_66660017(66660017, true,false,"当日用户验证次数超限", FaceStatusCode.PROVIDER_DAY_TIMES_LIMIT),
  OTHER_66660018(66660018, true,false,"操作超时，请退出重试", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  OTHER_66660021(66660021, false,false,"系统超时，请您稍后再试", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  OTHER_66660022(66660022, true,false,"动态数字失效，请重新验证", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  OTHER_66660023(66660023, true,false,"请确保本人操作且正脸对框", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_66660036(66660036, true,false,"请确保本人操作且正脸对框", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_5020(-5020, true,false,"请确保本人操作且正脸对框", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_5025(-5025, true,false,"请确保本人操作且正脸对框", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_66660024(66660024, true,false,"权威数据源返回无此证件号码", FaceStatusCode.IDENTITY_PHOTO_SOURCE_NOT_FOUND),
  OTHER_66660025(66660025, true,false,"请确保本人操作且正脸对框", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_66660026(66660026, true,false,"高清照大小或格式不符合", FaceStatusCode.IDENTITY_PHOTO_SIZE_NOT_STANDARD),
  OTHER_66660030(66660030, true,false,"不支持此类型服务", FaceStatusCode.PROVIDER_FAILED_API),
  OTHER_66660033(66660033, true,false,"图片中人脸数超限，请确保最多只有两张脸", FaceStatusCode.LIVENESS_FOUND_MULTI_FACE),
  OTHER_66660035(66660035, true,false,"未识别到人脸，请确保正脸对框且清晰完整", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_1102(-1102, true,false,"未识别到人脸，请确保正脸对框且清晰完整", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_66660037(66660037, true,false,"照片出现多张脸", FaceStatusCode.LIVENESS_FOUND_MULTI_FACE),
  OTHER_66660038(66660038, true,false,"未识别到人脸，请确保正脸对框且清晰完整", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_1101(-1101, true,false,"未识别到人脸，请确保正脸对框且清晰完整", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_4014(-4014, true,false,"未识别到人脸，请确保正脸对框且清晰完整", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_66660039(66660039, true,false,"证件照不存在或无法处理", FaceStatusCode.IDENTITY_PHOTO_PHOTO_NOT_FOUND),
  OTHER_66660040(66660040, true,true,"证件照不存在或无法处理", FaceStatusCode.IDENTITY_PHOTO_PHOTO_NOT_FOUND),
  OTHER_4017(-4017, true,false,"证件照不存在或无法处理", FaceStatusCode.IDENTITY_PHOTO_PHOTO_NOT_FOUND),
  OTHER_4018(-4018, true,false,"证件照不存在或无法处理", FaceStatusCode.IDENTITY_PHOTO_PHOTO_NOT_FOUND),

  OTHER_66660041(66660041, true,false,"脸部有遮挡，请保持人脸清晰无遮挡", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),
  OTHER_66660042(66660042, true,false,"照片模糊，请保持人脸清晰勿晃动", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),
  OTHER_5000(-5000, true,false,"服务内部 SDK 没有正常工作", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  OTHER_5001(-5001, true,false,"请确保本人操作且正脸对框", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),
  OTHER_5005(-5005, true,false,"离屏幕太近或太远；录制时，确保人脸清晰完整", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),
  OTHER_5008(-5008, true,false,"声音未能识别，请大声慢读一遍数字", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_5009(-5009, true,false,"视频中人脸检测不到；录制时，确保人脸清晰完整", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),
  OTHER_5010(-5010, true,false,"声音未能识别；请大声慢读一遍数字", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_5011(-5011, true,false,"未识别到人脸；请保持本人操作且正脸对框", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),
  OTHER_5012(-5012, true,false,"未识别到人脸；请保持本人操作且正脸对框", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),
  OTHER_5102(-5102, true,false,"照片模糊，录制时，确保人脸清晰勿晃动", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),
  OTHER_5103(-5103, true,false,"脸部有遮挡，录制时，确保人脸清晰完整", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),
  OTHER_5018(-5018, true,false,"未识别到眨眼；录制时请保持正脸对框且眨眼", FaceStatusCode.LIVENESS_EYE_BLINK_NOT_FOUND),
  OTHER_5026(-5026, true,false,"请确保本人操作且正脸对框", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_5027(-5027, true,false,"请确保本人操作且正脸对框", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_5052(-5052, true,false,"异常视频解析失败", FaceStatusCode.LIVENESS_VIDEO_FORMAT_FAIL),
  OTHER_1304(-1304, true,false,"视频由单张图片合成而来", FaceStatusCode.LIVENESS_MAYBE_NOT_PERSON),
  OTHER_1404(-1404, true,false,"活体识别不通过，按照如下提示再次尝试", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_15001(-15001, true,false,"视频中人脸检测不到", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_15002(-15002, true,false,"请确保本人操作且正脸对框", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_14099(-14099, true,false,"活体识别不通过，按照如下提示再次尝试", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_7001(-7001, true,false,"活体识别不通过，按照如下提示再次尝试", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_5051(-5051, true,false,"视频格式或大小不合法", FaceStatusCode.LIVENESS_VIDEO_FORMAT_FAIL),
  OTHER_5050(-5050, true,false,"视频格式或大小不合法", FaceStatusCode.LIVENESS_VIDEO_FORMAT_FAIL),
  OTHER_5024(-5024, true,false,"活体识别不通过，按照如下提示再次尝试", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_5023(-5023, true,false,"光线太强，请到室内识别", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_5021(-5021, true,false,"防攻击活体检测不通过", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_5019(-5019, true,false,"请确保本人操作且正脸对框", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_4003(-4003, true,false,"人脸检测失败", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_1497(-1497, true,false,"请确保本人操作且正脸对框", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_1493(-1493, true,false,"录制过程中请勿闭眼", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_1492(-1492, true,false,"防攻击活体检测不通过", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_1490(-1490, true,false,"防攻击活体检测不通过", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_1406(-1406, true,false,"人脸检测失败", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_1301(-1301, true,false,"请求参数字段非法", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  OTHER_1105(-1105, true,false,"服务器处理失败", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  OTHER_1104(-1104, true,false,"服务器处理失败", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  OTHER_15501(-15501, true,false,"请勿晃动人脸，保持正脸对框", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_15502(-15502, true,false,"未识别到眨眼;录制时请保持正脸对框且眨眼", FaceStatusCode.LIVENESS_EYE_BLINK_NOT_FOUND),
  OTHER_15503(-15503, true,false,"环境光线过强", FaceStatusCode.LIVENESS_BRIGHTNESS_CHECK_NOT_PASS),
  OTHER_15003(-15003, true,false,"疑似视频拼接", FaceStatusCode.LIVENESS_MAYBE_NOT_PERSON),
  OTHER_15504(-15504, true,false,"请勿遮挡", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_15505(-15505, true,false,"视频解码错误，请重新录制", FaceStatusCode.LIVENESS_VIDEO_FORMAT_FAIL),
  OTHER_999997(999997, true,false,"连接超时", FaceStatusCode.RESOURCE_NETWORK_EXCEPTION),
  OTHER_999998(999998, true,false,"连接超时", FaceStatusCode.RESOURCE_NETWORK_EXCEPTION),
  OTHER_999999(999999, true,false,"网络不给力,请稍后再试", FaceStatusCode.RESOURCE_NETWORK_EXCEPTION),
  OTHER_5014(-5014, true,false,"服务内部错误或者活体参数无效", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  OTHER_5208(-5208, true,false,"服务内部错误或者活体参数无效", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  OTHER_5017(-5017, true,false,"静默活体防翻拍失败", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_5016(-5016, true,false,"视频里的人疑似不是活体(翻拍等攻击)", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_66660006(66660006, true,false,"视频里的人疑似不是活体(翻拍等攻击)", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_66660014(66660014, true,false,"视频里的人疑似不是活体(翻拍等攻击)", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_5015(-5015, true,false,"视频像素太低,最小270*480", FaceStatusCode.LIVENESS_RESOLUTION_CHECK_NOT_PASS),
  OTHER_5013(-5013, true,false,"视频里的声音太小", FaceStatusCode.LIVENESS_VOICE_TOO_SMILE),
  OTHER_5007(-5007, true,false,"视频没有声音", FaceStatusCode.LIVENESS_VOICE_TOO_SMILE),
  OTHER_5002(-5002, true,false,"活体检测失败；录制时，普通话大声读数字", FaceStatusCode.LIVENESS_VOICE_TOO_SMILE),
  OTHER_4015(-4015, true,false,"自拍照人脸检测失败", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_4016(-4016, true,false,"自拍照人脸检测失败", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_1103(-1103, true,false,"自拍照人脸检测失败", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_4006(-4006, true,false,"自拍照人脸检测失败", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_4010(-4010, true,false,"比对相似度得分不够", FaceStatusCode.IDENTITY_PHOTO_CHECK_SIMILARITY_FAIL),
  OTHER_4009(-4009, true,false,"比对源照片提取特征失败", FaceStatusCode.IDENTITY_PHOTO_RESOLUTION_NOT_PASS),
  OTHER_66660019(66660019, true,false,"比对源照片提取特征失败", FaceStatusCode.IDENTITY_PHOTO_RESOLUTION_NOT_PASS),
  OTHER_1422(-1422, true,false,"人脸不在框内", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_1302(-1302, true,false,"参数错误", FaceStatusCode.PROVIDER_FAILED_API),
  OTHER_1303(-1303, true,false,"参数错误", FaceStatusCode.PROVIDER_FAILED_API),
  OTHER_1410(-1410, true,false,"参数错误", FaceStatusCode.PROVIDER_FAILED_API),
  OTHER_1411(-1411, true,false,"参数错误", FaceStatusCode.PROVIDER_FAILED_API),
  OTHER_1413(-1413, true,false,"参数错误", FaceStatusCode.PROVIDER_FAILED_API),
  OTHER_1414(-1414, true,false,"参数错误", FaceStatusCode.PROVIDER_FAILED_API),
  OTHER_1421(-1421, true,false,"参数错误", FaceStatusCode.PROVIDER_FAILED_API),
  OTHER_1412(-1412, true,false,"配准点数量异常", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),
  OTHER_1405(-1405, true,false,"图片模糊，无法识别", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),

  OTHER_1403(-1403, true,false,"疑似照片合成攻击", FaceStatusCode.LIVENESS_MAYBE_NOT_PERSON),
  OTHER_1100(-1100, true,false,"相似度错误", FaceStatusCode.IDENTITY_PHOTO_CHECK_SIMILARITY_FAIL),
  OTHER_999996(999996, true,false,"系统维护中", FaceStatusCode.PROVIDER_UP_KEEP),
  OTHER_66660007(66660007, true,false,"风险控制不过", FaceStatusCode.PROVIDER_RISK_LIMIT),
  OTHER_66660008(66660008, true,false,"解密/加密出错", FaceStatusCode.PROVIDER_FAILED_API),
  OTHER_66660009(66660009, true,false,"解密/加密出错", FaceStatusCode.PROVIDER_FAILED_API),
  OTHER_66660012(66660012, true,false,"触发了限流风控", FaceStatusCode.PROVIDER_RISK_LIMIT),
  OTHER_66660027(66660027, true,false,"没检测到人脸，请正脸对框", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),

  OTHER_66660028(66660028, true,false,"没有最优图片", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),
  OTHER_66660043(66660043, true,false,"录制过程中请勿闭眼", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_66660044(66660044, true,false,"应用服务版本过低，请升级", FaceStatusCode.PROVIDER_FAILED_API),
  OTHER_66662001(66662001, true,false,"无此授权结果", FaceStatusCode.PROVIDER_AUTH_FAIL),
  OTHER_66663001(66663001, true,false,"该订单号没有人脸识别记录", FaceStatusCode.PROVIDER_REQUEST_IS_NOT_EXIST),
  OTHER_66663002(66663002, true,false,"该订单号人脸识别未通过", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),
  OTHER_66663003(66663003, true,false,"没检测到人脸，请正脸对框", FaceStatusCode.IDENTITY_PHOTO_NAME_IDNO_ERROR),
  OTHER_14100(-14100, true,false,"疑似非实时录制视频", FaceStatusCode.PROVIDER_VIDEO_NOT_ALWAYS),


  //  腾讯云SDK错误码
  /* 安卓
   * https://cloud.tencent.com.cn/document/product/1007/35871
   * */
//WBFaceErrorDomainParams
  error_11000(11000, true, false, "传入参数为空", FaceStatusCode.PROVIDER_FAILED_API),
  error_11001(11001, true, false, "传入的licence不可用", FaceStatusCode.APP_SDK_CONFIG_LICENCE_ERROR),
  error_11002(11002, true, false, "报文加解密失败", FaceStatusCode.PROVIDER_FAILED_API),
  //WBFaceErrorDomainLoginNetwork
  error_21100(21100, true, false, "网络异常", FaceStatusCode.RESOURCE_NETWORK_EXCEPTION), //-登录时网络异常（请求未到达后台）
  error_21200(21200, true, false, "网络异常", FaceStatusCode.RESOURCE_NETWORK_EXCEPTION), //登录时后台返回参数有误（请求已到达后台
  //WBFaceErrorDomainGetInfoNetwork
  error_31100(31100, true, false, "网络异常", FaceStatusCode.RESOURCE_NETWORK_EXCEPTION), //获取活体类型/光线资源，网络异常（请求未到达后台）
  error_31200(31200, true, false, "网络异常", FaceStatusCode.RESOURCE_NETWORK_EXCEPTION),//获取活体类型/光线资源，后台返回参数有误（请求到达后台）
  //WBFaceErrorDomainNativeProcess
  error_41000(41000, true, false, "用户取消", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),
  error_41001(41001, true, false, "无法获取唇语数据", FaceStatusCode.LIVENESS_MOUTH_OPEN_NOT_FOUND),
  error_41002(41002, true, false, "权限异常，未获取权限", FaceStatusCode.RESOURCE_CAMERA_PERMISSION_NOT_ENABLE),// 相机
  error_41003(41003, true, false, "相机运行中出错", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),// 相机
  error_41004(41004, true, false, "视频录制中出错", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),// 不能存/启动失败/结束失败
  error_41005(41005, true, false, "请勿晃动人脸,保持姿势", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),// 未获取到最佳图片
  error_41006(41006, true, false, "视频大小不满足要求", FaceStatusCode.LIVENESS_VIDEO_FORMAT_FAIL),//
  error_41007(41007, true, false, "超时", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),// 预检测/动作活体
  error_41008(41008, true, false, "检测中人脸移出框外", FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),// 活体/数字/反光
  error_41009(41009, true, false, "光线活体本地错误", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),//
  error_41010(41010, true, false, "风险控制超出次数", FaceStatusCode.PROVIDER_RISK_LIMIT),// 用户重试太多次
  error_41011(41011, true, false, "没有检测到读数声音", FaceStatusCode.LIVENESS_VOICE_TOO_SMILE),// 数字活体过程中没有发声
  error_41012(41012, true, false, "初始化模型失败，请重试", FaceStatusCode.PROVIDER_FAILED_API),// 初始化算法模型失败
  error_41013(41013, true, false, "初始化 SDK 异常", FaceStatusCode.PROVIDER_FAILED_API),// WbCloudFaceVerifySdk 未被初始化
  error_41014(41014, true, false, "简单模式本地加密失败", FaceStatusCode.PROVIDER_FAILED_API),// 编码转换异常/加解密编码失败
  //WBFaceErrorDomainCompareNetwork
  error_51100(51100, true, false, "网络异常", FaceStatusCode.RESOURCE_NETWORK_EXCEPTION),// 对比时，网络异常（请求未到达后台）
  error_51200(51200, true, false, "网络异常", FaceStatusCode.RESOURCE_NETWORK_EXCEPTION),// 对比时，后台返回参数有误（请求已到达后台）

  /*
   * IOS
   * https://cloud.tencent.com.cn/document/product/1007/35877
   * */
//WBFaceErrorDomainInputParams
  error_12000(12000, true, false, "传入参数为空", FaceStatusCode.PROVIDER_FAILED_API),
  error_12001(12001, true, false, "传入keyLicence不可用", FaceStatusCode.APP_SDK_CONFIG_LICENCE_ERROR),
  error_12002(12002, true, false, "身份证格式不正确", FaceStatusCode.IDENTITY_PHOTO_NAME_IDNO_ERROR),
  error_12003(12003, true, false, "使用自带对比源，传入参数错误，非 base64", FaceStatusCode.LIVENESS_VIDEO_FORMAT_FAIL),// 传入的 srcPhotoString 不是 base64
  error_12004(12004, true, false, "使用自带对比源，传入参数错误，超过1MB", FaceStatusCode.LIVENESS_VIDEO_FORMAT_FAIL),//  传入的 srcPhotoString 超过1MB
  error_12005(12005, true, false, "SDK 资源引入版本不匹配", FaceStatusCode.PROVIDER_FAILED_API),//  没有引入资源包或者引入的资源包版本与当前 SDK 版本不匹配
  error_12006(12006, true, false, "订单号不能为0或者超过32位", FaceStatusCode.PROVIDER_FAILED_API),//
  error_12007(12007, true, false, "nonce 字符串位数不为32位", FaceStatusCode.PROVIDER_FAILED_API),//
  error_12008(12008, true, false, "SDK 正在提供服务", FaceStatusCode.PROVIDER_FAILED_API),//连续调用了 SDK 的启动方法
  //WBFaceErrorDomainLoginNetwork
  error_22100(22100, true, false, "网络异常", FaceStatusCode.RESOURCE_NETWORK_EXCEPTION),// 登录时网络异常（请求未到达后台）
  error_22200(22200, true, false, "网络异常", FaceStatusCode.RESOURCE_NETWORK_EXCEPTION),// 登录时后台返回参数有误（请求已到达后台）
  //WBFaceErrorDomainGetInfo
  error_32100(32100, true, false, "网络异常", FaceStatusCode.RESOURCE_NETWORK_EXCEPTION),// 获取活体类型或光线阈值，网络异常(请求未到达后台)
  error_32200(32200, true, false, "网络异常", FaceStatusCode.RESOURCE_NETWORK_EXCEPTION),// 获取活体类型或光线阈值，后台返回参数有误（请求已到达后台）
  //WBFaceErrorDomainNativeProcess
  error_42000(42000, true, false, "用户取消", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),// 回到后台或单击 home 或左上角或上传时左上角取消
  error_42001(42001, true, false, "网络环境不满足认证需求", FaceStatusCode.RESOURCE_NETWORK_EXCEPTION),// 无网络或2G 网络
  error_42002(42002, true, false, "权限异常，未获取权限", FaceStatusCode.RESOURCE_CAMERA_PERMISSION_NOT_ENABLE),// 相机或麦克风或 read phone 或 external 或 storage
  error_42003(42003, true, false, "相机运行中出错", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),//
  error_42004(42004, true, false, "视频录制中出错", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),// 不能存或启动失败或结束失败
  error_42005(42005, true, false, "请勿晃动人脸，保持姿势", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),// 未获取到最佳图片
  error_42006(42006, true, false, "视频大小不满足要求", FaceStatusCode.LIVENESS_VIDEO_FORMAT_FAIL),// 视频大小不满足要求
  error_42007(42007, true, false, "超时", FaceStatusCode.RESOURCE_EXCEPTION_INTERRUPT),// 预检测/动作活体
  error_42008(42008, true, false, "检测中人脸移出框外", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),// 活体或反光
  error_42009(42009, true, false, "光线活体本地错误", FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),// 活体或反光
  error_42010(42010, true, false, "风险控制超出次数", FaceStatusCode.PROVIDER_RISK_LIMIT),// 用户重试太多次
  error_42011(42011, true, false, "没有检测到读数声音", FaceStatusCode.LIVENESS_VOICE_TOO_SMILE),// 活体过程中没有发声
  error_42012(42012, true, false, "模型初始化失败", FaceStatusCode.PROVIDER_FAILED_API),//
  error_42015(42015, true, false, "报文解密失败", FaceStatusCode.PROVIDER_FAILED_API),// 请求报文解密失败
  //WBFaceErrorDomainCompareNetwork
  error_52100(52100, true, false, "网络异常", FaceStatusCode.RESOURCE_NETWORK_EXCEPTION),//对比时，网络异常（请求未到达后台）
  error_52200(52200, true, false, "网络异常", FaceStatusCode.RESOURCE_NETWORK_EXCEPTION),//对比时，后台返回参数有误（请求已到达后台）

  ;

  private int code;
  private boolean completed;
  private boolean fee;
  private String desc;
  private FaceStatusCode faceCode;

  TencentCloudFaceResultStatus(int code, boolean completed, boolean fee, String desc, FaceStatusCode faceCode) {
    this.code = code;
    this.completed = completed;
    this.fee = fee;
    this.desc = desc;
    this.faceCode = faceCode;
  }

  public boolean isCompleted() {
    return completed;
  }

  public String getDesc() {
    return desc;
  }

  public int getCode() {
    return code;
  }

  public boolean isFee() {
    return fee;
  }

  public FaceStatusCode getFaceCode() {
    return faceCode;
  }

  public static TencentCloudFaceResultStatus getStatusByCode(int code) {
    for (TencentCloudFaceResultStatus status : TencentCloudFaceResultStatus.values()) {
      if (status.code == code) {
        return status;
      }
    }

    return TencentCloudFaceResultStatus.SYSTEM_ERROR;
  }

  public boolean ifSuccess(){
     return this.code == SUCCESS.code;
  }

  public boolean ifFail(){
    return !ifSuccess();
  }

  @Override
  public String errCode() {
    return String.valueOf(code);
  }

  @Override
  public String errMsg() {
    return desc;
  }

  public void setDesc(String desc) {
    if(StringUtils.isBlank(desc)){
       return;
    }
    this.desc = desc;
  }
}
