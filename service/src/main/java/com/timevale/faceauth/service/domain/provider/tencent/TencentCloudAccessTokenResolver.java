package com.timevale.faceauth.service.domain.provider.tencent;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.provider.support.ELock;
import com.timevale.faceauth.service.domain.provider.support.ELockExe;
import com.timevale.faceauth.service.domain.provider.support.RestTemplateRequestResolver;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/21 18
 */
@Slf4j
@Component
public class TencentCloudAccessTokenResolver {

  private static final String TOKEN_KEY_ACCESS_TOKEN_PRE = "face06:tencent:cloud:access-token:";
  private static final String TOKEN_LOCK = TOKEN_KEY_ACCESS_TOKEN_PRE + "lock";
  //  timeout a hour
  private static final int TOKEN_KEY_PUT_TIMEOUT_SECONDS = 50 * 60;

  private final ConfigurableProperties properties;
  private final RestTemplateRequestResolver requestResolver;

  @Autowired
  public TencentCloudAccessTokenResolver(
          ConfigurableProperties properties, RestTemplateRequestResolver requestResolver) {
    this.properties = properties;
    this.requestResolver = requestResolver;
  }

  TencentCloudAccessToken resolveAccessToken( TencentWebAppIdVersion appIdVersion)
          throws FaceException {
    TencentCloudAccessToken __access_token = getAndRefreshIfExpired( appIdVersion);
    ;
    return __access_token;
  }

  private TencentCloudAccessToken obtainAccessTokenFromCache(TencentWebAppIdVersion appIdVersion) {
    Object data;
    try {
      data = TedisUtil.tedis().string().get(tokenKey(appIdVersion));
    } catch (Exception cause) {
      log.warn("Fail fetch access token from cache .", cause);
      return null;
    }
    if (null == data) {
      return null;
    }

    String content = String.valueOf(data);
    try {
      return JsonUtils.json2pojo(content, TencentCloudAccessToken.class);
    } catch (Exception cause) {
      log.warn(
              "Data["
                      + content
                      + "] could not instantiate to type["
                      + TencentCloudAccessToken.class
                      + "] .",
              cause);
      return null;
    }
  }

  private TencentCloudAccessToken getAndRefreshIfExpired(
          TencentWebAppIdVersion appIdVersion) {
    TencentCloudAccessToken accessToken = obtainAccessTokenFromCache(appIdVersion);
    return (isAvailable(accessToken) ? accessToken : refreshToken( appIdVersion));
  }

  private boolean isAvailable(TencentCloudAccessToken accessToken) {
    if (accessToken == null) {
      return false;
    }
    return accessToken.available(properties.getTencentCloudTokenExpiredLeadTime());
  }

  private TencentCloudAccessToken refreshToken(TencentWebAppIdVersion appIdVersion)
          throws FaceException {
    return new ELock<TencentCloudAccessToken>()
            .lock(
                    TOKEN_LOCK,
                    new ELockExe() {
                      @Override
                      public TencentCloudAccessToken process() {
                        TencentCloudAccessToken accessToken = resolveToken( appIdVersion);
                        putAccessTokenToCache(accessToken, appIdVersion);
                        return accessToken;
                      }

                      @Override
                      public TencentCloudAccessToken get() {
                        return obtainAccessTokenFromCache(appIdVersion);
                      }
                    });
  }

  private TencentCloudAccessToken resolveToken(TencentWebAppIdVersion appIdVersion) {
    long startTime = System.currentTimeMillis();
    TencentCloudAccessTokenRequest request =
            TencentCloudAccessTokenRequest.createBuilder()
                    .setApp_id(appIdVersion.getAccessHolder().getWebAppId())
                    .setSecret(appIdVersion.getAccessHolder().getWebAppSecret())
                    .build();
    String queryString = requestResolver.resolveQueryString(request);
    String uriString = properties.getTencentCloudTokenApi() + "?" + queryString;
    RequestEntity<String> requestEntity =
            requestResolver.resolveRequestEntity(null, uriString, HttpMethod.GET, null);
    ResponseEntity<String> responseEntity =
            requestResolver.resolveResponse(
                    appIdVersion.getProviderName(), requestEntity, String.class);
    TencentCloudAccessToken accessToken = detectAccessToken(requestEntity, responseEntity);
    long endTime = System.currentTimeMillis();
    log.info(
            "tencent cloud get token={},cost={} ms",
            JSON.toJSONString(accessToken),
            (endTime - startTime));
    return accessToken;
  }

  private TencentCloudAccessToken detectAccessToken(
          RequestEntity<String> requestEntity, ResponseEntity<String> responseEntity)
          throws FaceException {
    TencentCloudAccessTokenResponse response;
    try {
      response =
              JsonUtils.json2pojo(responseEntity.getBody(), TencentCloudAccessTokenResponse.class);
    } catch (Exception cause) {
      throw FaceException.valueOf(
              FaceStatusCode.PROVIDER_ERROR_API_RESULT_SYNTAX, "Error response data context .", cause);
    }
    if (response.success()) {
      TencentCloudAccessToken accessToken = (new TencentCloudAccessToken());
      accessToken.setAccess_token(response.getAccess_token());
      accessToken.setExpire_in(response.getExpire_in());
      accessToken.setExpire_time(response.getExpire_time());
      accessToken.setTransactionTime(response.getTransactionTime());
      return accessToken;
    }

    throw FaceException.valueOf(
            FaceStatusCode.PROVIDER_ERROR_API_RESPONSE,
            "Fail response["
                    + response.getCode()
                    + ","
                    + response.getMsg()
                    + "] api["
                    + requestEntity.getUrl().toString()
                    + "]");
  }

  @SuppressWarnings("unchecked")
  private void putAccessTokenToCache(
          TencentCloudAccessToken accessToken, TencentWebAppIdVersion appIdVersion)
          throws FaceException {
    String data;
    try {
      data = JsonUtils.obj2json(accessToken);
    } catch (Exception cause) {
      throw FaceException.valueOf(
              FaceStatusCode.PROVIDER_FAILED_CACHED_TICKET, "Fail serialize access token .", cause);
    }
    try {
      TedisUtil.tedis()
              .string()
              .set(
                      tokenKey(appIdVersion), data, deduceTokenCacheTimeout(accessToken), TimeUnit.SECONDS);
    } catch (Exception cause) {
      throw FaceException.valueOf(
              FaceStatusCode.PROVIDER_FAILED_CACHED_TICKET, "Fail put accessToken .", cause);
    }
  }

  private long deduceTokenCacheTimeout(TencentCloudAccessToken accessToken) {
    long expireIn = accessToken.getExpire_in();
    return (0 >= expireIn ? TOKEN_KEY_PUT_TIMEOUT_SECONDS : expireIn);
  }

  public String tokenKey(TencentWebAppIdVersion appIdVersion) {
    return TOKEN_KEY_ACCESS_TOKEN_PRE + appIdVersion.getAccessHolder().getWebAppId();
  }
}
