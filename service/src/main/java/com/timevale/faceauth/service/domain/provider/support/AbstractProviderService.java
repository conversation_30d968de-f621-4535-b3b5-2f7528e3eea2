package com.timevale.faceauth.service.domain.provider.support;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.core.support.ArgumentUtil;
import com.timevale.faceauth.service.core.support.FaceQueryableExtend;
import com.timevale.faceauth.service.domain.ConfigurableFaceAuthorizationCompletedInvocationHandler;
import com.timevale.faceauth.service.domain.ConfigurableFaceAuthorizationCompletedInvocationHandlers;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.FaceAuthorizationCompletedInvocationHandler;
import com.timevale.faceauth.service.domain.event.FaceEventPublisher;
import com.timevale.faceauth.service.domain.event.ProviderFaceAuthorizationCompletedEvent;
import com.timevale.faceauth.service.domain.event.ProviderFaceAuthorizationInitializedEvent;
import com.timevale.faceauth.service.domain.processor.ConfigurableFaceProcessors;
import com.timevale.faceauth.service.domain.processor.FaceRequestContext;
import com.timevale.faceauth.service.domain.processor.FaceReturnProcessor;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationData;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.domain.repository.FaceOSSResources;
import com.timevale.faceauth.service.domain.repository.FaceRepository;
import com.timevale.faceauth.service.domain.repository.FaceResourceRepository;
import com.timevale.faceauth.service.domain.repository.FaceSwitchRepository;
import com.timevale.faceauth.service.domain.repository.ProviderFaceInfo;
import com.timevale.faceauth.service.domain.repository.ProviderFaceRepository;
import com.timevale.faceauth.service.domain.repository.ProviderReturnInfo;
import com.timevale.faceauth.service.domain.support.DnsResolver;
import com.timevale.faceauth.service.exception.ProviderException;
import com.timevale.faceauth.service.input.HandleFaceAuthorizationReturnInput;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.DateUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 供应商服务基础架构实现
 *
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/9/30 17
 */
@Slf4j
public abstract class AbstractProviderService implements ConfigurableProviderService {


  //23小时过期
  private static final int EXPIRED_HOUR = 23;
  private static final int EXPIRED_HOUR_FOR_BYTEDANCE = 1;
  private static final String SERVICE_HOST_PATTERN_DATA_GROUP = "data";

  private final Pattern serviceHostPattern =
      Pattern.compile("^(?<" + SERVICE_HOST_PATTERN_DATA_GROUP + ">\\S+)[\\\\/]*$");


  @Autowired FaceResourceRepository faceResourceRepository;
  private final FaceRepository faceRepository;
  private final ProviderFaceRepository providerFaceRepository;
  protected final ConfigurableProperties properties;
  private final DnsResolver dnsResolver;
  private FaceSwitchRepository faceSwitchRepository;

  public AbstractProviderService(
      FaceRepository faceRepository,
      ProviderFaceRepository providerFaceRepository,
      DnsResolver dnsResolver,
      ConfigurableProperties configurableProperties,
      FaceSwitchRepository faceSwitchRepository) {
    this.faceRepository = faceRepository;
    this.providerFaceRepository = providerFaceRepository;
    this.properties = configurableProperties;
    this.dnsResolver = dnsResolver;
    this.faceSwitchRepository = faceSwitchRepository;
  }

  public AbstractProviderService(
      FaceRepository faceRepository,
      ProviderFaceRepository providerFaceRepository,
      DnsResolver dnsResolver,
      ConfigurableProperties configurableProperties) {
    this.faceRepository = faceRepository;
    this.providerFaceRepository = providerFaceRepository;
    this.properties = configurableProperties;
    this.dnsResolver = dnsResolver;
  }

  @Override
  public ProviderFaceAuthorizationResult queryAuthorizeResult(
      FaceQueryableExtend extend, FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo)
      throws FaceException {
    FaceInfo faceRequest = prepareFace(providerFaceInfo.getFaceId(), faceInfo);
    return doQuery(
        ConfigurableFaceAuthorizationCompletedInvocationHandler.TYPE_COMPLETED_QUERY,
        extend,
        faceRequest,
        providerFaceInfo);
  }



  protected ProviderFaceAuthorizationResult doQuery(
      String completedType,
      FaceQueryableExtend extend,
      FaceInfo faceInfo,
      ProviderFaceInfo providerFaceInfo)
      throws FaceException {
    FaceInfo faceRequest = prepareFace(providerFaceInfo.getFaceId(), faceInfo);
    try {
      return (providerFaceInfo.isDone()
          ? resolveResultIfDoneAuthorization(completedType, extend, faceRequest, providerFaceInfo)
          : doQueryFromProvider(
              extend,
              faceRequest,
              providerFaceInfo));
    } catch (Exception cause) {
      log.warn("Fail authorization on face[" + faceRequest.getFaceId() + "] .", cause);
      return resolveFailAuthorizationResult(cause, faceRequest, providerFaceInfo);
    }
  }

  private FaceInfo prepareFace(String faceId, FaceInfo faceInfo) throws FaceException {
    return (null == faceInfo ? faceRepository.getFaceInfoByFaceId(faceId) : faceInfo);
  }

  private ProviderFaceAuthorizationResult resolveResultIfDoneAuthorization(
      String completedType,
      FaceQueryableExtend extend,
      FaceInfo faceInfo,
      ProviderFaceInfo providerFaceInfo)
      throws FaceException {
    ProviderReturnInfo providerReturnWithOutType =
            providerFaceRepository.getProviderReturnWithOutType(providerFaceInfo.getFaceId());
    ProviderReturnInfo providerReturn =
        providerFaceRepository.getProviderReturn(providerFaceInfo.getFaceId(), completedType);
    // 避免供应商回调后的重复查询！！！此次只适配字节！！！
    if ((null == providerReturn && null != providerReturnWithOutType && !providerReturnWithOutType.getProvider().equals(PROVIDER_BYTEDANCE))
            || null == providerReturnWithOutType) {
      return doQueryFromProvider(
          extend,
          faceInfo,
          providerFaceInfo);
    }
    if (null == providerReturn && null != providerReturnWithOutType && providerReturnWithOutType.getProvider().equals(PROVIDER_BYTEDANCE)
            && !providerReturnWithOutType.isCompleted()) {
      return doQueryFromProvider(
              extend,
              faceInfo,
              providerFaceInfo);
    }

    if (null == providerReturn){
      providerReturn = providerReturnWithOutType;
    }

    if (providerReturn.isCompleted()) {
      AbstractProviderFaceAuthorizationResult result =
          resolveDoneAuthorizationResult(providerReturn, extend, faceInfo, providerFaceInfo);
      if (result == null) {
        return doQueryFromProvider(
            extend,
            faceInfo,
            providerFaceInfo);
      }
      if (!faceInfo.isOk()) {
        result.fillFaceError(
            ProviderException.buildFaceError(
                faceInfo.getResultCode(),
                faceInfo.getResultMsg(),
                providerFaceInfo.getErrCode(),
                providerFaceInfo.getErrMsg()));
      }
      return result;
    }

    long returnTime = providerReturn.getTimestamp();
    long doneTime = providerFaceInfo.getDoneTime();
    // 假如提前查询，再检测一次结果
    if (returnTime <= doneTime) {
      return doQueryFromProvider(
          extend,
          faceInfo,
          providerFaceInfo);
    }

    // 幂等处理，在已执行认证结果查询之后，总是已查询的数据作为兜底，不在继续向供应商查询
    try {
      log.warn(
          "Fail authorization on provider response[" + JsonUtils.obj2json(providerReturn) + "] . ");
    } catch (Exception ignore) {
      log.warn("Fail authorization on face[" + faceInfo.getFaceId() + "] .");
    }

    ProviderFaceAuthorizationResponse authorizationResponse =
        detectActualAuthorizationResponse(faceInfo, providerFaceInfo, providerReturn);
    return (new FailProviderFaceAuthorizationResult(
        getProviderName(), authorizationResponse, faceInfo, providerFaceInfo, providerReturn));
  }

  protected String obtainProviderReturnTypeForQuery() {
    return ConfigurableFaceAuthorizationCompletedInvocationHandler.TYPE_COMPLETED_QUERY;
  }

  /**
   * 获取刷脸的资源：视频、照片
   * @param faceId
   * @return
   */
  protected FaceOSSResources getFaceResources(String faceId){
    return faceResourceRepository.getFaceResources(faceId);
  }

  protected ProviderFaceAuthorizationResponse detectActualAuthorizationResponse(
      FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo, ProviderReturnInfo providerReturnInfo) {
    return (new ProviderFaceAuthorizationResponse());
  }

  protected abstract AbstractProviderFaceAuthorizationResult resolveDoneAuthorizationResult(
      ProviderReturnInfo providerReturn,
      FaceQueryableExtend extend,
      FaceInfo faceInfo,
      ProviderFaceInfo providerFaceInfo)
      throws FaceException;

  private ProviderFaceAuthorizationResult doQueryFromProvider(
      FaceQueryableExtend extend, FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo)
      throws FaceException {
    ProviderFaceAuthorizationResult result =
        doQueryAuthorizeResult(extend, faceInfo, providerFaceInfo);
    // 刷脸没有完成，且是查询请求，不保存记录
    if (!result.isCompleted()) {
      return result;
    }
    try {
      onCompletedFaceAuthorization(
          ConfigurableFaceAuthorizationCompletedInvocationHandler.TYPE_COMPLETED_QUERY,
          result,
          null,
          null);
    } finally {

      if (result.isSuccess()) {

      }
    }
    return result;
  }


  //超过23小时消失不允许查询
  protected boolean checkTasKExpired(FaceInfo faceInfo) {
    Date startTime = faceInfo.getCreateTime();
    long expiredTime = DateUtils.addHours(startTime, EXPIRED_HOUR).getTime();
    if (expiredTime < System.currentTimeMillis()) {
//      throw FaceException.valueOf(FaceStatusCode.FACE_TASK_EXPIRED);
      return true;
    }
    return false;
  }

  //超过1小时消失不查底层供应商
  protected boolean checkTasKExpiredForByteDance(FaceInfo faceInfo) {
    Date startTime = faceInfo.getCreateTime();
    long expiredTime = DateUtils.addHours(startTime, EXPIRED_HOUR_FOR_BYTEDANCE).getTime();
    if (expiredTime < System.currentTimeMillis()) {
//      throw FaceException.valueOf(FaceStatusCode.FACE_TASK_EXPIRED);
      return true;
    }
    return false;
  }


  protected abstract ProviderFaceAuthorizationResult doQueryAuthorizeResult(
      FaceQueryableExtend extend, FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo)
      throws FaceException;

  protected void onCompletedFaceAuthorization(
      String completedType,
      ProviderFaceAuthorizationResult result,
      HttpServletRequest request,
      HttpServletResponse response)
      throws FaceException {
    // Publish event on provider face authorization completed
    ProviderFaceAuthorizationCompletedEvent event =
        (new ProviderFaceAuthorizationCompletedEvent(completedType, result));
    FaceEventPublisher.publishEvent(event);
    // Invocation on face authorization completed
    FaceAuthorizationCompletedInvocationHandler handler =
        ConfigurableFaceAuthorizationCompletedInvocationHandlers
            .getFaceAuthorizationCompletedInvocationHandler(completedType);
    handler.invoke(result, request, response);
  }

  @Override
  public void faceAuthorizationReturned(
      String faceId, HttpServletRequest request, HttpServletResponse response) {
    ProviderFaceAuthorizationResult faceAuthorizationResult;
    try {
      faceAuthorizationResult = detectFaceAuthorizationResultOnReturn(faceId, request);
    } catch (Exception cause) {
      faceAuthorizationResult = resolveFailAuthorizationResult(cause, faceId, request);
    }
    try {
      onCompletedFaceAuthorization(
          ConfigurableFaceAuthorizationCompletedInvocationHandler.TYPE_COMPLETED_RETURN,
          faceAuthorizationResult,
          request,
          response);
    } catch (Exception cause) {
      log.warn("Failed provider return .", cause);
    }
  }

    /**
     * 第四种方式，小程序端上调用  查询供应商结果
     */
    @Override
    public ProviderFaceAuthorizationResult handleFaceAuthorizationResponse(String faceId, HandleFaceAuthorizationReturnInput request) {

        ProviderFaceInfo providerFaceInfo = providerFaceRepository.getByFaceId(faceId);
        FaceInfo faceRequest = prepareFace(faceId, null);
        if (providerFaceInfo.isDone()) {
          log.info("Face authorization is done, use resolveResultIfDoneAuthorization  faceId: {}", faceId);
          return resolveResultIfDoneAuthorization(ConfigurableFaceAuthorizationCompletedInvocationHandler.TYPE_COMPLETED_CALLBACK, null, faceRequest, providerFaceInfo);
        }
        //执行处理逻辑
        ProviderFaceAuthorizationResult faceAuthorizationResult = detectHandleFaceAuthorizationReturned(faceId, request);
        if (!faceAuthorizationResult.isCompleted()) {
            return faceAuthorizationResult;
        }

        //尝试完成流程  这里还是设置为供应商回调完成方式
        onCompletedFaceAuthorization(ConfigurableFaceAuthorizationCompletedInvocationHandler.TYPE_COMPLETED_CALLBACK, faceAuthorizationResult, null, null);
        return faceAuthorizationResult;
    }

  protected ProviderFaceAuthorizationResult resolveFailAuthorizationResult(
      Exception cause, String faceId, HttpServletRequest request) throws FaceException {
    log.warn("Fail authorization on face[" + faceId + "] .", cause);
    FaceInfo faceInfo = faceRepository.getFaceInfoByFaceId(faceId);
    ProviderFaceInfo providerFaceInfo = providerFaceRepository.getByFaceId(faceId);
    return resolveFailAuthorizationResult(cause, faceInfo, providerFaceInfo);
  }

  private ProviderFaceAuthorizationResult resolveFailAuthorizationResult(
      Exception cause, FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo) {
    FaceException faceException =
        (cause instanceof FaceException
            ? (FaceException) cause
            : FaceException.valueOf(FaceStatusCode.FAIL_AUTHORIZATION, cause));
    return (new FailProviderFaceAuthorizationResult(
        getProviderName(), null, faceInfo, providerFaceInfo, faceException));
  }

  protected byte[] extractRequestData(HttpServletRequest request) throws FaceException {
    if(request == null){
       return null;
    }
    int size = 2048;
    byte[] buffer = (new byte[size]);
    try (InputStream ins = request.getInputStream();
        ByteArrayOutputStream ous = (new ByteArrayOutputStream())) {
      int l;
      while (0 <= (l = ins.read(buffer, 0, size))) {
        ous.write(buffer, 0, l);
      }

      return ous.toByteArray();
    } catch (Exception cause) {
      String msg = "Error obtain provider request data on url[" + request.getRequestURI() + "] .";
      log.warn(msg, cause);
      throw FaceException.valueOf(FaceStatusCode.PROVIDER_ERROR_CALLBACK_RESPONSE, msg);
    }
  }

  /** 推断供应商回跳的数据结果 */
  protected abstract ProviderFaceAuthorizationResult detectFaceAuthorizationResultOnReturn(
      String faceId, HttpServletRequest request) throws FaceException;

  /**
   * 推断供应商新回跳的数据结果
   */
  protected ProviderFaceAuthorizationResult detectHandleFaceAuthorizationReturned(String faceId, HandleFaceAuthorizationReturnInput request) {
    throw FaceException.valueOf(FaceStatusCode.SERVICE_UNSUPPORTED);
  }

  @Override
  public String faceAuthorizationCallback(String faceId, HttpServletRequest request)
      throws FaceException {
    byte[] dataBuffer = extractRequestData(request);
    ProviderFaceAuthorizationResult faceAuthorizationResult =
        detectFaceAuthorizationResultOnCallback(faceId, dataBuffer, request);
    onCompletedFaceAuthorization(
        ConfigurableFaceAuthorizationCompletedInvocationHandler.TYPE_COMPLETED_CALLBACK,
        faceAuthorizationResult,
        request,
        null);
    try {
      return successCallback(faceAuthorizationResult);
    } catch (Exception cause) {
      String msg = "Error obtain callback success response .";
      log.warn(msg, cause);
      throw FaceException.valueOf(FaceStatusCode.FACE_FIELD_COMPLETED_INVOCATION, msg, cause);
    }
  }

  /** 推断供应商回调的数据结果 */
  protected abstract ProviderFaceAuthorizationResult detectFaceAuthorizationResultOnCallback(
      String faceId, byte[] dataBuffer, HttpServletRequest request) throws FaceException;

  /** 返回 callback 处理成功时，的响应数据内容 */
  protected String successCallback(
      ProviderFaceAuthorizationResult providerFaceAuthorizationResult) {
    return "SUCCESS";
  }

  @Override
  public ProviderFaceAuthorizationData initialize(
      FaceRequestContext requestContext, FaceInfo faceInfo) throws FaceException {
    String faceId = faceInfo.getFaceId();
    String provider = getProviderName();
    if (!StringUtils.hasLength(provider)) {
      throw FaceException.valueOf(FaceStatusCode.SERVICE_UNDEFINED, getClass().getName());
    }
    String serviceHost = detectServiceHost();
    String callbackUrl = deduceCallbackUrl(serviceHost, provider, faceId);
    log.info("Found callbackUrl '" + callbackUrl + "' .");
    String serviceReturnUrl = obtainServiceReturnUrl(serviceHost, provider, faceId);
    FaceReturnProcessor faceReturnProcessor =
        ConfigurableFaceProcessors.getProcessor(requestContext.getClientType());
    String returnUrl = deduceReturnUrl(serviceReturnUrl, requestContext, faceReturnProcessor);
    log.info("Found returnUrl '" + returnUrl + "' .");
    String actualReturnURL = faceReturnProcessor.removeCompletedReturnURL(returnUrl);
    log.info("Found actual return url '" + actualReturnURL + "' .");
    FaceAuthorizationInitializingContext initializingContext =
        (new FaceAuthorizationInitializingContext(
            faceInfo.getFaceId(),
            requestContext,
            callbackUrl,
            actualReturnURL,
            deduceActualMerchantName()));
    ProviderFaceAuthorizationData authorizationData = doInitialize(initializingContext);
    onInitialized(authorizationData, initializingContext);
    return authorizationData;
  }

  protected String deduceActualMerchantName() {
    return properties.getDefaultMerchantName();
  }

  private String obtainServiceReturnUrl(String host, String provider, String faceId)
      throws FaceException {
    String router = ProviderFaceAuthorizationCompletionUtil.getReturnUrl(provider, faceId);
    return (host + router);
  }

  private String detectServiceHost() throws FaceException {
    String host = dnsResolver.resolveCallbackDns();
    ArgumentUtil.throwIfEmptyArgument(host, "serviceHost");
    try {
      Matcher matcher = serviceHostPattern.matcher(host);
      if (matcher.find()) {
        host = matcher.group(SERVICE_HOST_PATTERN_DATA_GROUP);
      }
    } catch (Exception cause) {
      String msg = "Error format '" + host + "' .";
      log.warn(msg, cause);
      throw FaceException.valueOf(FaceStatusCode.ILLEGAL_ARGUMENT, msg, cause);
    }
    ArgumentUtil.throwIfEmptyArgument(host, "serviceHost");
    return host;
  }

  protected String deduceCallbackUrl(String host, String provider, String faceId) {
    String router = ProviderFaceAuthorizationCompletionUtil.getCallbackUrl(provider, faceId);
    return (host + router);
  }

  protected String deduceReturnUrl(
      String returnServiceUrl,
      FaceRequestContext requestContext,
      FaceReturnProcessor faceReturnProcessor) {
    if (faceReturnProcessor.canReturnService()) {
      return returnServiceUrl;
    }
    String returnUrl = faceReturnProcessor.obtainActualReturnUrl(requestContext.getReturnUrl());
    if (StringUtils.isEmpty(returnUrl) && faceReturnProcessor.canReturnService()) {
      return returnServiceUrl;
    }
    return returnUrl;
  }



  protected abstract ProviderFaceAuthorizationData doInitialize(
      FaceAuthorizationInitializingContext initializingContext) throws FaceException;

  protected void onInitialized(
      ProviderFaceAuthorizationData authorizationData,
      FaceAuthorizationInitializingContext initializingContext) {
    // Publish event on provider face authorization initialized
    ProviderFaceAuthorizationInitializedEvent event =
        (new ProviderFaceAuthorizationInitializedEvent(
            initializingContext.getFaceId(), authorizationData));
    FaceEventPublisher.publishEvent(event);
  }


  private static final class FailProviderFaceAuthorizationResult
      implements ProviderFaceAuthorizationResult {

    private final String provider;
    private final FaceInfo faceInfo;
    private final ProviderFaceInfo providerFaceInfo;
    private final ProviderReturnInfo providerReturnInfo;
    private final ProviderException providerException;
    private final ProviderFaceAuthorizationResponse providerFaceAuthorizationResponse;

    private FailProviderFaceAuthorizationResult(
        String provider,
        ProviderFaceAuthorizationResponse response,
        FaceInfo faceInfo,
        ProviderFaceInfo providerFaceInfo,
        ProviderReturnInfo providerReturnInfo) {
      this.provider = provider;
      this.providerFaceAuthorizationResponse = response;
      this.faceInfo = faceInfo;
      this.providerFaceInfo = providerFaceInfo;
      this.providerReturnInfo = providerReturnInfo;
      this.providerException = ProviderException.valueOf(providerReturnInfo.getErrorMsg());
    }

    private FailProviderFaceAuthorizationResult(
        String provider,
        ProviderFaceAuthorizationResponse response,
        FaceInfo faceInfo,
        ProviderFaceInfo providerFaceInfo,
        FaceException cause) {
      this.provider = provider;
      this.providerFaceAuthorizationResponse = response;
      this.faceInfo = faceInfo;
      this.providerFaceInfo = providerFaceInfo;
      this.providerReturnInfo = null;
      this.providerException = (new ProviderException(cause));
    }

    @Override
    public String getProvider() {
      return provider;
    }

    @Override
    public String getFaceId() {
      return faceInfo.getFaceId();
    }

    @Override
    public String getResultContent() {
      return (null == providerReturnInfo ? null : providerReturnInfo.getData());
    }

    @Override
    public boolean isCompleted() {
      return providerFaceInfo.isDone();
    }

    @Override
    public boolean isSuccess() {
      return faceInfo.isOk();
    }

    @Override
    public ProviderException getError() {
      return providerException;
    }

    @Override
    public long getTimestamp() {
      return faceInfo.getCreateTime().getTime();
    }

    @Override
    public float getLiveRate() {
      return (null == providerFaceAuthorizationResponse
          ? -1F
          : providerFaceAuthorizationResponse.getLiveRate());
    }

    @Override
    public float getSimilarity() {
      return (null == providerFaceAuthorizationResponse
          ? -1F
          : providerFaceAuthorizationResponse.getSimilarity());
    }

    @Override
    public String getPhoto() {
      return (null == providerFaceAuthorizationResponse
          ? ""
          : providerFaceAuthorizationResponse.getPhoto());
    }

    @Override
    public String getIdCardFront() {
      return (null == providerFaceAuthorizationResponse
          ? ""
          : providerFaceAuthorizationResponse.getIdCardFront());
    }

    @Override
    public String getIdCardBack() {
      return (null == providerFaceAuthorizationResponse
          ? ""
          : providerFaceAuthorizationResponse.getIdCardBack());
    }

    @Override
    public String getPhotoType() {
      return faceInfo.getPhotoType();
    }

    @Override
    public String getVideo() {
      return (null == providerFaceAuthorizationResponse
          ? ""
          : providerFaceAuthorizationResponse.getVideo());
    }
  }

  protected String loadFacePhotoBest(String faceId) {
    List<String> photoAll = loadFacePhotos( faceId);
    if(CollectionUtils.isEmpty(photoAll)){
       return null;
    }
    return photoAll.get(0);
  }

  protected List<String> loadFacePhotos(String faceId) {
    FaceOSSResources ossResources = faceResourceRepository.getFaceResources(faceId);
    return ossResources.getPhotoAll();
  }
}
