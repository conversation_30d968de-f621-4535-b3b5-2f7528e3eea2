package com.timevale.faceauth.service.utils.providerLog.result;

import com.alipay.api.response.AlipayUserCertifyOpenCertifyResponse;
import com.timevale.faceauth.service.apispace.facade.com.timevale.apispace.request.ApiResponseBaseMetric;
import com.timevale.faceauth.service.utils.providerLog.AbstractProviderLogResultResolver;
import com.timevale.faceauth.service.utils.providerLog.ProviderActionEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderKeyIdEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogConfiguration;
import java.util.List;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
public class AliMiniProgInitCertifyUrlResult
    extends AbstractProviderLogResultResolver<AlipayUserCertifyOpenCertifyResponse> {

  public AliMiniProgInitCertifyUrlResult(String bizId, long responseTime) {
    super(bizId, responseTime);
  }

  @Override
  public ProviderKeyIdEnum getProviderKeyId() {
    return ProviderKeyIdEnum.AliMiniProg;
  }

  @Override
  public ProviderActionEnum getAction() {
    return ProviderActionEnum.INITIALIZATION;
  }

  @Override
  public String getCode(AlipayUserCertifyOpenCertifyResponse response) {
    return response.getCode();
  }

  @Override
  public String getMsg(AlipayUserCertifyOpenCertifyResponse response) {
    return response.getMsg();
  }

  @Override
  public String getResult(AlipayUserCertifyOpenCertifyResponse response) {
    return null;
  }

  @Override
  public List<String> getFieldEmptyMappingStautsUnknown() {
    return ProviderLogConfiguration
        .aliMiniProgCertifyUrlActionInitializationFieldEmptyMappingStautsUnknown;
  }

  @Override
  public List<String> getCodeMsgMappingStautsFailure() {
    return ProviderLogConfiguration
        .aliMiniProgCertifyUrlActionInitializationCodeMsgMappingStautsFailure;
  }

  @Override
  public List<String> getCodeMsgMappingStautsSuccess() {
    return ProviderLogConfiguration
        .aliMiniProgCertifyUrlActionInitializationCodeMsgMappingStautsSuccess;
  }

  @Override
  public ApiResponseBaseMetric resolver(AlipayUserCertifyOpenCertifyResponse response) {
    ApiResponseBaseMetric apiResponseBaseMetric = super.resolver(response);
    // 支付宝小程序初始化分为两步（第二步）
    apiResponseBaseMetric.setTag(String.valueOf(ATTRIBUTE_2));
    return apiResponseBaseMetric;
  }
}
