package com.timevale.faceauth.service.domain.support;

import com.timevale.faceauth.service.core.AudioAndVideoDualException;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.ConfigurableFaceAuthorizationInitializer;
import com.timevale.faceauth.service.domain.FaceAuthorization;
import com.timevale.faceauth.service.domain.processor.FaceRequestContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/22 22
 */
@Component
class ParallelFaceAuthorizationInitializer implements ConfigurableFaceAuthorizationInitializer {

  private final SupportFaceAuthorizationInitializer supportFaceAuthorizationInitializer;

  @Autowired
  public ParallelFaceAuthorizationInitializer(
      SupportFaceAuthorizationInitializer supportFaceAuthorizationInitializer) {
    this.supportFaceAuthorizationInitializer = supportFaceAuthorizationInitializer;
  }

  @Override
  public String getAuthorizeType() {
    return TYPE_PARALLEL;
  }

  @Override
  public FaceAuthorization initialize(FaceRequestContext context) throws FaceException {
    try {
      return supportFaceAuthorizationInitializer.initialize(context);
    } catch (AudioAndVideoDualException | FaceException cause) {
      throw cause;
    } catch (Exception cause) {
      throw FaceException.valueOf(FaceStatusCode.FACE_FAILED_AUTHORIZATION_INITIALIZE, cause);
    }
  }
}
