package com.timevale.faceauth.service.domain.provider.tencent;

import com.timevale.faceauth.service.component.OpenPlatformClient;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceResult;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.core.support.FaceQueryableExtend;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.FaceAuthorizationPhotoResolver;
import com.timevale.faceauth.service.domain.processor.FaceRequestContext;
import com.timevale.faceauth.service.domain.processor.FaceReturnProcessor;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationData;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.support.AbstractProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.support.AbstractProviderService;
import com.timevale.faceauth.service.domain.provider.support.FaceAuthorizationInitializingContext;
import com.timevale.faceauth.service.domain.provider.support.ProviderFaceAuthorizationResponse;
import com.timevale.faceauth.service.domain.provider.tencent.domain.TencentWebAppIdVersionHookDTO;
import com.timevale.faceauth.service.domain.provider.tencent.domain.TencentWebAppIdVersionUtil;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.domain.repository.FaceRepository;
import com.timevale.faceauth.service.domain.repository.FaceSwitchRepository;
import com.timevale.faceauth.service.domain.repository.ProviderFaceInfo;
import com.timevale.faceauth.service.domain.repository.ProviderFaceRepository;
import com.timevale.faceauth.service.domain.repository.ProviderReturnInfo;
import com.timevale.faceauth.service.domain.repository.UserPhoto;
import com.timevale.faceauth.service.domain.support.DnsResolver;
import com.timevale.faceauth.service.domain.support.SupportFaceAuthorizationFinishedResolver;
import com.timevale.faceauth.service.exception.ProviderException;
import com.timevale.mandarin.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 基于腾讯云活体识别服务实现刷脸认证
 *
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/9 19
 */
@Slf4j
@Component
public class TencentCloudService extends AbstractProviderService {

  static final String PROVIDER = PROVIDER_TENCENT_CLOUD;

  @Autowired private ConfigurableProperties properties;
  @Autowired private SupportFaceAuthorizationFinishedResolver authorizationFinishedResolver;

  @Autowired
  private TencentCloudCertificationInitializationUploadInvocationHandler uploadInvocationHandler;

  @Autowired
  private TencentCloudCertificationInitializationBeginningInvocationHandler
      beginningInvocationHandler;

  @Autowired private TencentCloudCertificationReturnInvocationHandler returnInvocationHandler;
  @Autowired private TencentCloudCertificationQueryInvocationHandler queryInvocationHandler;
  @Autowired private FaceAuthorizationPhotoResolver faceAuthorizationPhotoResolver;
  @Autowired private OpenPlatformClient platformClient;
  @Autowired private FaceRepository faceRepository;


  @Autowired
  public TencentCloudService(
      FaceRepository faceRepository,
      ProviderFaceRepository providerFaceRepository,
      DnsResolver dnsResolver,
      ConfigurableProperties configurableProperties,
      FaceSwitchRepository faceSwitchRepository) {
    super(
        faceRepository,
        providerFaceRepository,
        dnsResolver,
        configurableProperties,
        faceSwitchRepository);
  }

  @Override
  public String getProviderName() {
    return PROVIDER;
  }

  @Override
  public String getFullName() {
    return FULL_NAME_PROVIDER_TENCENT_CLOUD;
  }

  @Override
  public String resolveSimilarity(FaceResult result) {
    return "" + result.getSimilarity();
  }

  @Override
  public String resolveLiveRate(FaceResult result) {
    return "" + result.getLiveRate();
  }

  @Override
  public FaceRequestContext refreshRequest(FaceRequestContext context) {
    if (context instanceof TencentCloudFaceAuthorizationRequestContext) {
      return context;
    }

    // 尝试推断用户的认证照片数据，并构建新的刷脸认证请求上下文
    UserPhoto userPhoto = faceAuthorizationPhotoResolver.resolvePhoto(context);
    return (new TencentCloudFaceAuthorizationRequestContext(context, userPhoto));
  }

  @Override
  protected ProviderFaceAuthorizationData doInitialize(
      FaceAuthorizationInitializingContext initializingContext) throws FaceException {

    // 接口使用版本
    String appId = initializingContext.getRequest().getAppId();
    final TencentWebAppIdVersion appIdVersion = getProviderVersion(appId);
    final String webThemeColor = getWebThemeColor(appId);

    TencentCloudCertificationInitializationUploadResultWrap uploadResultWrap =
        uploadInvocationHandler.invoke(initializingContext, appIdVersion);
    String beginUrl =
        beginningInvocationHandler.invoke(
            uploadResultWrap.getResult(), initializingContext, appIdVersion, webThemeColor);

    faceRepository.faceApiVersion(initializingContext.getFaceId(), appIdVersion.getVersion());
    return ProviderFaceAuthorizationData.createBuilder()
        .setProvider(getProviderName())
        .setProviderVersion(appIdVersion.getVersion())
        .setProviderName(getFullName())
        .setOrderNo(uploadResultWrap.getResult().getOrderNo())
        .setExpireMinutes(properties.getTencentCloudTaskExpire())
        .setThirdpartId(
            uploadResultWrap.getResult().getBizSeqNo()
                + "@"
                + uploadResultWrap.getResult().getH5faceId())
        .setData(beginUrl)
        .setAuthorizeInput(uploadResultWrap.getInput())
        .build();
  }

  @Override
  protected ProviderFaceAuthorizationResult doQuery(
      String completedType,
      FaceQueryableExtend extend,
      FaceInfo faceInfo,
      ProviderFaceInfo providerFaceInfo)
      throws FaceException {
    // 检测是否是端问题，假如是则不需要查询供应商
    if (providerFaceInfo.isDone() && properties.isNotNeedQueryErrCode(providerFaceInfo.errCode)) {
      ProviderException providerEx = null;
      try {
        int code = Integer.parseInt(providerFaceInfo.errCode);
        TencentCloudFaceResultStatus resultStatus =
            TencentCloudFaceResultStatus.getStatusByCode(code);
        providerEx = new ProviderException(resultStatus.getFaceCode(), resultStatus);
      } catch (Exception e) {
        log.error("转换腾讯云刷脸错误码失败，" + providerFaceInfo.errCode, e);
      }

      return TencentCloudFaceAuthorizationResult.createBuilder()
              .setProvider(getProviderName())
          .setTimestamp(providerFaceInfo.getDoneTime())
          .setSuccess(faceInfo.isOk())
          .setContent(providerFaceInfo.getFaceData())
          .setCompleted(providerFaceInfo.isDone())
          .setFaceId(providerFaceInfo.getFaceId())
          .setLiveRate(-1)
          .setSimilarity(-1)
          .setPhoto(null)
          .setPhotoType(null)
          .setVideo(null)
          .setError(providerEx)
          .build();
    }

    ProviderFaceAuthorizationResult authResult =
        super.doQuery(completedType, extend, faceInfo, providerFaceInfo);
    boolean ifNeedCompensateFacePhoto = faceInfo.isOk()
            && (StringUtils.isEmpty(authResult.getPhoto()));
    if (ifNeedCompensateFacePhoto) {
      //TODO 存在多次调用问题 （1）回跳更新刷脸状态没有照片 （2）查询刷脸结果
      // 同步刷脸照片
      ProviderFaceAuthorizationResult newResult =
          this.doQueryAuthorizeResult(extend, faceInfo, providerFaceInfo);
      // 1. 保存供应商调用记录
      authorizationFinishedResolver.saveProviderReturn(completedType, newResult);
      return newResult;
    }

    return authResult;
  }

  @Override
  protected ProviderFaceAuthorizationResult doQueryAuthorizeResult(
      FaceQueryableExtend extend, FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo)
      throws FaceException {
    if(this.checkTasKExpired(faceInfo)){
      throw FaceException.valueOf(FaceStatusCode.FACE_TASK_EXPIRED);
    }
    final TencentWebAppIdVersion appIdVersion =
            getProviderVersion(faceInfo.getAppId(),faceInfo.getProviderApiVersion());
    return queryInvocationHandler.invoke(faceInfo, providerFaceInfo, appIdVersion);
  }

  @Override
  protected ProviderFaceAuthorizationResult detectFaceAuthorizationResultOnReturn(
      String faceId, HttpServletRequest request) throws FaceException {
    return returnInvocationHandler.invoke(faceId, request, getProviderName());
  }

  @Override
  protected ProviderFaceAuthorizationResult detectFaceAuthorizationResultOnCallback(
      String faceId, byte[] dataBuffer, HttpServletRequest request) throws FaceException {
    throw FaceException.valueOf(
        FaceStatusCode.SERVICE_UNSUPPORTED,
        "Unsupported callback operation on provider[" + getProviderName() + "] .");
  }

  @Override
  protected String deduceReturnUrl(
      String returnServiceUrl,
      FaceRequestContext requestContext,
      FaceReturnProcessor faceReturnProcessor) {
    return returnServiceUrl;
  }

  @Override
  protected AbstractProviderFaceAuthorizationResult resolveDoneAuthorizationResult(
      ProviderReturnInfo providerReturn,
      FaceQueryableExtend extend,
      FaceInfo faceInfo,
      ProviderFaceInfo providerFaceInfo)
      throws FaceException {
    String content = providerReturn.getData();
    return (StringUtils.isEmpty(content)
        ? null
        : queryInvocationHandler.resolveDoneQueryResult(content, faceInfo, providerFaceInfo, getProviderName()));
  }

  @Override
  protected ProviderFaceAuthorizationResponse detectActualAuthorizationResponse(
      FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo, ProviderReturnInfo providerReturnInfo) {
    String content = providerReturnInfo.getData();
    if (StringUtils.isEmpty(content)) {
      return super.detectActualAuthorizationResponse(
          faceInfo, providerFaceInfo, providerReturnInfo);
    }

    ProviderFaceAuthorizationResponse response;
    try {
      response = JsonUtils.json2pojo(content, ProviderFaceAuthorizationResponse.class);
    } catch (Exception cause) {
      log.warn(
          "Fail deserialize bean of type["
              + ProviderFaceAuthorizationResponse.class
              + "] on data["
              + content
              + "] .",
          cause);
      return super.detectActualAuthorizationResponse(
          faceInfo, providerFaceInfo, providerReturnInfo);
    }

    return response;
  }

  protected TencentWebAppIdVersion getProviderVersion(String appId, String version) {
    return TencentCloudUtil.getProviderVersion(appId, version);
  }


  protected TencentWebAppIdVersion getProviderVersion(String appId) {
    //走新逻辑
    TencentWebAppIdVersion appIdVersionHook = TencentWebAppIdVersionUtil.getAppVersionByAppId(TencentWebAppIdVersionHookDTO.ABILITY_H5, appId);
    if (Objects.nonNull(appIdVersionHook)) {
      return appIdVersionHook;
    }

    String code = platformClient.getTencentFaceType(appId);
    appIdVersionHook = TencentWebAppIdVersionUtil.getAppVersionByCode(TencentWebAppIdVersionHookDTO.ABILITY_H5, code);
    if (Objects.nonNull(appIdVersionHook)) {
      return appIdVersionHook;
    }
    return TencentWebAppIdVersionUtil.getH5RecordVideoAppVersion();

//    // 2024/8/26  mangcao     打error重点关注
//    log.error("走到逻辑  需要重点关注 : appId = {}  ", appId);
//    //走老逻辑
//    TencentEsignAppIdWebAccess esignAppIdWebAccess = properties.getWebAccessByEsignAppId(appId);
//    if(!StringUtils.isEmpty(esignAppIdWebAccess)){
//        TencentWebAppIdVersionEnum appIdVersion = TencentWebAppIdVersionEnum.SPECIAL;
//        appIdVersion.setAccessHolder(esignAppIdWebAccess);
//        return appIdVersion;
//    }
//
//    return TencentWebAppIdVersionEnum.getByCode(code);
  }

  private String getWebThemeColor(String appId) {
    String code = platformClient.getTencentThemeColor(appId);
    if(TencentWebThemeColor.isDefault(code)){
       return null;
    }
    return code.trim();
  }

  public static final class TencentCloudFaceAuthorizationRequestContext
      implements FaceRequestContext {

    private final FaceRequestContext request;
    private final String photo;
    private final String photoType;

    public TencentCloudFaceAuthorizationRequestContext(
        FaceRequestContext request, UserPhoto userPhoto) {
      this.request = request;
      this.photo = userPhoto.getPhoto();
      this.photoType = userPhoto.getPhotoType();
    }

    @Override
    public String getAppId() {
      return request.getAppId();
    }

    @Override
    public String getOid() {
      return request.getOid();
    }

    @Override
    public String getBizId() {
      return request.getBizId();
    }

    @Override
    public String getBizScene() {
      return request.getBizScene();
    }

    @Override
    public String getBizCode() {
      return request.getBizCode();
    }

    @Override
    public String getClientType() {
      return request.getClientType();
    }

    @Override
    public String getName() {
      return request.getName();
    }

    @Override
    public String getIdNo() {
      return request.getIdNo();
    }

    @Override
    public String getIdType() {
      return request.getIdType();
    }

    @Override
    public String getProvider() {
      return request.getProvider();
    }

    @Override
    public String getPhoto() {
      return photo;
    }

    @Override
    public String getPhotoType() {
      return photoType;
    }

    @Override
    public String getReturnUrl() {
      return request.getReturnUrl();
    }

    @Override
    public String getCallbackUrl() {
      return request.getCallbackUrl();
    }

    @Override
    public long getTimestamp() {
      return request.getTimestamp();
    }

    @Override
    public String getInput() {
      return request.getInput();
    }
  }
}
