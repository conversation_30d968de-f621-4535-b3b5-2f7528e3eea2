package com.timevale.faceauth.service.impl.support;

import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import com.timevale.faceauth.service.enums.FaceAuthModeEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * 刷脸provider和faceMode 对应适配
 *
 * <AUTHOR>
 * @since 2022-01-04 19:34
 */
public class FaceAuthModeAdapter {

  private static final Map<String, FaceAuthModeEnum> PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER;

  static {
    PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER = (new HashMap<>(16));
    PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER.put(
        ConfigurableProviderService.PROVIDER_TENCENT_CLOUD, FaceAuthModeEnum.TECENT_CLOUD_H5);
    PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER.put(
            ConfigurableProviderService.PROVIDER_WE_CHAT_FACE, FaceAuthModeEnum.WE_CHAT_FACE);
    PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER.put(
            ConfigurableProviderService.PROVIDER_ALI_TENCENT_MINI_PROG,
            FaceAuthModeEnum.ALI_TENCENT_MINI_PROGR);

    PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER.put(
        ConfigurableProviderService.PROVIDER_DING_TALK, FaceAuthModeEnum.DING_TALK);
    PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER.put(
        ConfigurableProviderService.PROVIDER_ANT_BLOCK_CHAIN, FaceAuthModeEnum.ANT_BLOCK_CHAIN);
    PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER.put(
            ConfigurableProviderService.PROVIDER_ALI_MINI_PROG, FaceAuthModeEnum.ALI_MINI_PROGRAM);
    PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER.put(
            ConfigurableProviderService.PROVIDER_ALIPAY, FaceAuthModeEnum.ALI_MINI_PROGRAM);

    PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER.put(
        ConfigurableProviderService.PROVIDER_LIVENESS, FaceAuthModeEnum.FACE_LIVENESS_RECOGNITION);
    PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER.put(
        ConfigurableProviderService.PROVIDER_AUDIO_VIDEO_DUAL, FaceAuthModeEnum.AUDIO_VIDEO_DUAL);
    PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER.put(
        ConfigurableProviderService.PROVIDER_WE_CHAT_VIDEO_DUAL,
        FaceAuthModeEnum.WE_CHAT_VIDEO_DUAL);

    PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER.put(
            ConfigurableProviderService.PROVIDER_AUDIO_VIDEO_ESIGN,
            FaceAuthModeEnum.AUDIO_VIDEO_ESIGN);

    PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER.put(
            ConfigurableProviderService.PROVIDER_BYTEDANCE, FaceAuthModeEnum.BYTEDANCE);

    // //新增刷脸渠道配置 抖音刷脸
    PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER.put(
            ConfigurableProviderService.PROVIDER_FACE_TIKTOK_MINI, FaceAuthModeEnum.FACE_TIKTOK_MINI);
    // 2024/8/12  mangcao    新增腾讯云SDK刷脸配置
    PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER.put(
            ConfigurableProviderService.PROVIDER_FACE_TENCENT_SDK_BASIC, FaceAuthModeEnum.FACE_TECENT_SDK_BASIC);
    PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER.put(
            ConfigurableProviderService.PROVIDER_FACE_TENCENT_SDK_PLUS, FaceAuthModeEnum.FACE_TECENT_SDK_PLUS);

    PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER.put(
            ConfigurableProviderService.PROVIDER_MOCK, FaceAuthModeEnum.MOCK);

  }

    public static int deduceFaceAuthMode(String provider) {
        FaceAuthModeEnum mode = PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER.get(provider);
        return (null == mode ? 0 : mode.getMode());
    }

  public static FaceAuthModeEnum deduceFaceAuthModeEnum(String provider) {
    FaceAuthModeEnum mode = PROVIDER_FACE_AUTH_MODE_ENUM_MAPPER.get(provider);
    return mode;
  }

}
