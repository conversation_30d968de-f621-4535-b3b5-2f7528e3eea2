package com.timevale.faceauth.service.inner.impl.tecent;

import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudUtil;
import com.timevale.faceauth.service.domain.provider.tencent.domain.TencentWebAppIdVersionUtil;
import com.timevale.faceauth.service.enums.TecentFaceAuthCodeEnum;
import com.timevale.mandarin.base.util.JsonUtils;
import esign.utils.exception.SuperException;
import esign.utils.httpclient.HttpConfig;
import esign.utils.httpclient.HttpUtil;
import esign.utils.timeunit.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on 2018/7/19
 *     <p>腾讯云业务处理基类
 */
public abstract class BaseTecentCloudServiceImpl {

  /** NONCE 类型ticket 常量值 */
  protected static final String TICKET_TYPE_NONCE = "NONCE";

  private static final Logger LOGGER = LoggerFactory.getLogger(BaseTecentCloudServiceImpl.class);

  /** 获取ticket api */
  private static final String API_TICKET =
      "https://idasc.webank.com/api/oauth2/api_ticket?app_id=%s&access_token=%s&type=%s&version=1.0.0&user_id=%s";

  /**
   * 获取缓存中的ticket
   *
   * @return
   */
  protected String getTicketInCache(String faceId) {
//    return TencentCloudUtil.getTicketCache(faceId,TencentWebAppIdVersion.RECORD_VIDEO);
    return TencentCloudUtil.getTicketCache(faceId, TencentWebAppIdVersionUtil.getH5RecordVideoAppVersion());
  }

  /**
   * 获取缓存中的ticket
   *
   * @return
   */
  protected String getAccessTokenInCache() {
//    return TencentCloudUtil.getAccessToken( TencentWebAppIdVersion.RECORD_VIDEO)
    return TencentCloudUtil.getAccessToken( TencentWebAppIdVersionUtil.getH5RecordVideoAppVersion())
        .getAccess_token();
  }

  /**
   * 获取腾讯云ticket
   *
   * @param appId 应用id
   * @param token token
   * @param type SIGN类型和NONCE类型
   * @param userId 用户id
   * @return
   */
  protected String getTecentCloudTicket(String appId, String token, String type, String userId) {

    String api = String.format(API_TICKET, appId, token, type, userId);

    try {
      String resultJson = HttpUtil.getAsString(api, buildHttpConfig());

      Map<String, Object> map = JsonUtils.json2map(resultJson);

      String code = (String) map.get("code");

      if (TecentFaceAuthCodeEnum.SUCCESS_CODE.getCode().equals(code)) {

        List<Map<String, Object>> tickets = (List<Map<String, Object>>) map.get("tickets");

        Map<String, Object> objectMap = tickets.get(0);

        String ticket = (String) objectMap.get("value");

        return ticket;
      } else {
        LOGGER.info("getTecentCloudTicket result : {}", resultJson);
      }

    } catch (SuperException e) {
      LOGGER.warn("getTecentCloudTicket fail .", e);
    }

    return "";
  }

  protected HttpConfig buildHttpConfig() {
    HttpConfig httpConfig = new HttpConfig();
    httpConfig.setRetry(3);
    httpConfig.setTimeoutConnect(TimeUnit.MILLISECOND.unit(500));
    httpConfig.setTimeoutRequest(TimeUnit.SECOND.unit(1));
    return httpConfig;
  }
}
