package com.timevale.faceauth.service.impl.api;

import com.alibaba.fastjson.JSON;
import com.timevale.base.elock.Elock;
import com.timevale.base.elock.LockFactory;
import com.timevale.faceauth.service.api.FaceAuthService;
import com.timevale.faceauth.service.api.FaceAuthServiceV3;
import com.timevale.faceauth.service.component.ControlFaceDataPrivilegeComponent;
import com.timevale.faceauth.service.component.FaceSubjectAgeLegalComponent;
import com.timevale.faceauth.service.convertor.FaceSwitchModelResultConverter;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.fun.FaceAuthStartFun;
import com.timevale.faceauth.service.domain.repository.FaceLocalLibraryPhotoRepository;
import com.timevale.faceauth.service.domain.repository.FaceSwitchModel;
import com.timevale.faceauth.service.domain.repository.FaceSwitchRepository;
import com.timevale.faceauth.service.domain.repository.UserPhoto;
import com.timevale.faceauth.service.domain.support.RequestVersionResolver;
import com.timevale.faceauth.service.domain.support.VersionSupport;
import com.timevale.faceauth.service.enums.FaceAuthBizTypeEnum;
import com.timevale.faceauth.service.enums.FaceAuthModeEnum;
import com.timevale.faceauth.service.enums.FaceCertTypeEnum;
import com.timevale.faceauth.service.impl.ConfigurableFaceAuthorizationService;
import com.timevale.faceauth.service.impl.FaceRequestResolver;
import com.timevale.faceauth.service.impl.support.FaceCertTypeSupport;
import com.timevale.faceauth.service.inner.impl.afterface.ProviderFaceResourceFactory;
import com.timevale.faceauth.service.inner.impl.afterface.ProviderFaceResourceService;
import com.timevale.faceauth.service.input.BaseInput;
import com.timevale.faceauth.service.input.FaceAuthInput;
import com.timevale.faceauth.service.input.FaceAuthResInput;
import com.timevale.faceauth.service.input.FaceAuthUserInfoInput;
import com.timevale.faceauth.service.input.FaceAuthorizationDetailQueryInput;
import com.timevale.faceauth.service.input.FaceSwitchRecordQueryInput;
import com.timevale.faceauth.service.input.HandleFaceAuthorizationReturnInput;
import com.timevale.faceauth.service.input.UserPhotoQueryInput;
import com.timevale.faceauth.service.input.WakeupFaceInput;
import com.timevale.faceauth.service.mq.MQManager;
import com.timevale.faceauth.service.mq.StartFaceSuccessTask;
import com.timevale.faceauth.service.result.FaceAuthResult;
import com.timevale.faceauth.service.result.FaceAuthUserInfoResult;
import com.timevale.faceauth.service.result.FaceAuthorizationDetailResult;
import com.timevale.faceauth.service.result.FaceSwitchRecordQueryResult;
import com.timevale.faceauth.service.result.HandleFaceAuthorizationReturnResult;
import com.timevale.faceauth.service.result.QueryFaceAuthResResult;
import com.timevale.faceauth.service.result.SupportResult;
import com.timevale.faceauth.service.result.UserPhotoQueryResult;
import com.timevale.faceauth.service.result.WakeupFaceResult;
import com.timevale.faceauth.service.utils.CardUtils;
import com.timevale.faceauth.service.utils.FaceAuthValidationUtils;
import com.timevale.mandarin.base.exception.BaseBizRuntimeException;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.common.annotation.RestService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/22 12
 */
@Slf4j
@RestService
@Api(tags = "刷脸RPC接口")
public class DelegateFaceAuthorizationService implements FaceAuthService, FaceAuthServiceV3 {

  @Autowired
  private FaceSubjectAgeLegalComponent faceSubjectAgeLegalComponent;

  @Autowired
  private MQManager mqManager;
  @Autowired
  private FaceRequestResolver faceRequestResolver;
  //  @Autowired
//  private ProviderFaceResourceService providerFaceResourceService;
  @Autowired
  private ProviderFaceResourceFactory providerFaceResourceFactory;
  @Autowired
  private FaceLocalLibraryPhotoRepository userPhotoRepository;
  @Autowired
  private ControlFaceDataPrivilegeComponent controlFaceDataPrivilegeComponent;

  private final FaceSwitchRepository faceSwitchRepository;


  private final Map<String, ConfigurableFaceAuthorizationService>
          configurableFaceAuthorizationServices;
  private final RequestVersionResolver requestVersionResolver;
  private final Map<FaceAuthBizTypeEnum, AbstractFaceAuthorizationRequestVersionResolver>
          bizTypeFaceAuthorizationRequestVersionResolverMap;
  private final Map<String, AbstractFaceAuthorizationRequestRefresher>
      faceAuthorizationRequestRefresherMap;

  @Autowired
  public DelegateFaceAuthorizationService(
      ConfigurableProperties configurableProperties,
      FaceSwitchRepository faceSwitchRepository,
      RequestVersionResolver requestVersionResolver,
      ConfigurableFaceAuthorizationService[] configurableFaceAuthorizationServices) {
    this.faceSwitchRepository = faceSwitchRepository;
    this.requestVersionResolver = requestVersionResolver;
    this.configurableFaceAuthorizationServices =
        (new HashMap<>(2 * configurableFaceAuthorizationServices.length));
    configFaceAuthorizationServices(
        this.configurableFaceAuthorizationServices, configurableFaceAuthorizationServices);
    this.bizTypeFaceAuthorizationRequestVersionResolverMap = (new HashMap<>(4));
    configurationBizTypeFaceAuthorizationRequestVersionResolverMap(
        configurableProperties, this.bizTypeFaceAuthorizationRequestVersionResolverMap);
    this.faceAuthorizationRequestRefresherMap = (new HashMap<>(4));
    configurationFaceAuthorizationRequestRefresherMap(this.faceAuthorizationRequestRefresherMap);
  }

  private void configFaceAuthorizationServices(
      Map<String, ConfigurableFaceAuthorizationService> map,
      ConfigurableFaceAuthorizationService[] services) {
    for (ConfigurableFaceAuthorizationService service : services) {
      map.put(service.getVersion(), service);
    }
  }

  private ConfigurableFaceAuthorizationService deduceActualFaceAuthorizationService(String version)
      throws FaceException {
    String actualVersion = (StringUtils.isEmpty(version) ? VersionSupport.VERSION_1_0_0 : version);
    ConfigurableFaceAuthorizationService faceAuthorizationService =
        this.configurableFaceAuthorizationServices.get(actualVersion);
    if (null == faceAuthorizationService) {
      throw FaceException.valueOf(FaceStatusCode.SERVICE_UNSUPPORTED, "Error version .");
    }
    return faceAuthorizationService;
  }

  private void configurationBizTypeFaceAuthorizationRequestVersionResolverMap(
      ConfigurableProperties configurableProperties,
      final Map<FaceAuthBizTypeEnum, AbstractFaceAuthorizationRequestVersionResolver> map) {
    map.put(
        FaceAuthBizTypeEnum.WILL_AUTH,
        (new WillFaceAuthorizationRequestVersionResolver(configurableProperties)));
  }

  private void configurationFaceAuthorizationRequestRefresherMap(
      final Map<String, AbstractFaceAuthorizationRequestRefresher> map) {
    map.put(
        RequestVersionResolver.DEFAULT_VERSION, (new Version1FaceAuthorizationRequestRefresher()));
  }

  @Override
  public SupportResult<FaceAuthResult> startFaceAuth(FaceAuthInput faceAuthInput)
      throws FaceException {
    return faceStarter.startFace(faceAuthInput);
  }

  /** version 1.3 */
  @Override
  @SuppressWarnings("unchecked")
  @ApiOperation(value = "获取刷脸地址", httpMethod = "POST")
  public SupportResult<FaceAuthResult> startNewFaceAuth(@RequestBody FaceAuthInput faceAuthInput) {
    log.info("获取刷脸地址 startNewFaceAuth, faceAuthInput:{}", JSON.toJSONString(faceAuthInput));
    SupportResult result = this.validFaceAuthRequest(faceAuthInput);
    if(result.ifFail()){
       return result;
    }
    this.format(faceAuthInput);
    return faceStarter.startFace(faceAuthInput);
  }

  private void format(FaceAuthInput faceAuthInput){
    String idNo = faceAuthInput.getIdNo();
    FaceCertTypeEnum certTypeEnum = FaceCertTypeEnum.nameOf(faceAuthInput.getCertType());

    //大陆身份证15位转为18位
    if(StringUtils.isNotBlank(idNo)
            && Objects.equals(certTypeEnum, FaceCertTypeEnum.IDENTITY_CARD)
            && idNo.length() == CardUtils.ID_CARD_BASE_LENGTH_15){
        faceAuthInput.setIdNo(CardUtils.getEighteenIDCard(idNo));
    }
  }

  private SupportResult validFaceAuthRequest(FaceAuthInput faceAuthInput){

     FaceAuthModeEnum faceAuthMode =
             faceAuthInput.getFaceAuthMode();
     FaceCertTypeEnum certTypeEnum =
             FaceCertTypeEnum.nameOf(faceAuthInput.getCertType());
     if(certTypeEnum == null){
       return SupportResult.fail(FaceStatusCode.IDENTITY_PHOTO_ID_TYPE_ERROR);
     }

     if(FaceCertTypeSupport.checkModeNotSupport(certTypeEnum, faceAuthMode)){
       return SupportResult.fail(FaceStatusCode.FACE_UNSUPPORTED_INITIALIZE_TYPE);
     }
     if(Objects.equals(certTypeEnum, FaceCertTypeEnum.IDENTITY_CARD)){
       return validCHAccount(faceAuthInput);
     }
    return SupportResult.success(null);
  }

  private SupportResult validCHAccount(FaceAuthInput faceAuthInput){
    String name = CardUtils.formatName(faceAuthInput.getName());
    faceAuthInput.setName(name);
    boolean isBadName = !CardUtils.validName(faceAuthInput.getName());
    if(isBadName){
      return SupportResult.fail(FaceStatusCode.IDENTITY_PHOTO_NAME_ERROR);
    }

    boolean isBadIdNo = !CardUtils.checkIdCardFormat(faceAuthInput.getIdNo());
    if(isBadIdNo){
      return SupportResult.fail(FaceStatusCode.IDENTITY_PHOTO_ID_NO_ERROR);
    }
    return SupportResult.success(null);
  }

  @Override
  @ApiOperation(value = "查询刷脸结果", httpMethod = "POST")
  public SupportResult<QueryFaceAuthResResult> queryFaceAuthResult(
      @RequestBody FaceAuthResInput faceAuthResInput) throws FaceException {
    return getQueryFaceAuthResResultRpcOutput(faceAuthResInput);
  }

  /** version 1.3 */
  @Override
  @SuppressWarnings("unchecked")
  public SupportResult<QueryFaceAuthResResult> queryNewFaceAuthResult(
      FaceAuthResInput faceAuthResInput) {

    SupportResult<QueryFaceAuthResResult> rpcOutput =
            getQueryFaceAuthResResultRpcOutput(faceAuthResInput);

    return rpcOutput;
  }

  @Override
  @ApiOperation(value = "查询刷脸详情", httpMethod = "POST")
  public SupportResult<FaceAuthorizationDetailResult> getAuthorizationDetail(
      @RequestBody FaceAuthorizationDetailQueryInput input) {
    ConfigurableFaceAuthorizationService faceAuthorizationService =
        deduceFaceAuthorizationService(input.getFaceId(), input.getVersion());
    SupportResult<FaceAuthorizationDetailResult> result = faceAuthorizationService.queryDetail(input);

    return controlFaceDataPrivilegeComponent.controlData(input, result);
  }

  @Override
  @ApiOperation(value = "查询刷脸切换记录", httpMethod = "POST")
  public SupportResult<List<FaceSwitchRecordQueryResult>> queryFaceSwitchRecord(
          @RequestBody FaceSwitchRecordQueryInput input) {
    FaceSwitchModel faceSwitchModel = FaceSwitchModel.builder()
            .idNo(input.getIdNo())
            .name(input.getName())
            .faceMode(input.getFaceMode())
            .start(input.getStart())
            .pageSize(input.getPageSize())
            .build();
    return SupportResult.success(
            faceSwitchRepository.querySwitchRecords(faceSwitchModel)
                    .stream()
                    .map(FaceSwitchModelResultConverter::convert)
                    .collect(Collectors.toList()));
  }

  @Override
  public SupportResult<UserPhotoQueryResult> queryUserPhoto(UserPhotoQueryInput input) {
    UserPhotoQueryResult result = new UserPhotoQueryResult();
    if(StringUtils.isEmpty(input.getName()) || StringUtils.isEmpty(input.getIdNo())) {
      result.setHasUserPhoto(Boolean.FALSE);
    }else {
      UserPhoto userPhoto = userPhotoRepository.getPhotoByFactor2(input.getAppId(), input.getIdNo(), input.getName());
      result.setHasUserPhoto(Objects.nonNull(userPhoto));
    }
    return SupportResult.success(result);
  }

  @Override
  public SupportResult<Boolean> clearUserPhoto(UserPhotoQueryInput input) {
    if(StringUtils.isEmpty(input.getName()) || StringUtils.isEmpty(input.getIdNo())) {
      return SupportResult.success(Boolean.FALSE);
    }
    boolean optSuccess = userPhotoRepository.clearUserPhoto(input.getIdNo(), input.getName());
    return SupportResult.success(optSuccess);
  }

  private String deduceFaceAuthorizationServiceVersion(FaceAuthInput faceAuthInput) {

    FaceAuthBizTypeEnum bizType = faceAuthInput.getBizType();
    if (null == bizType) {
      return faceAuthInput.getVersion();
    }

    AbstractFaceAuthorizationRequestVersionResolver requestVersionResolver =
        bizTypeFaceAuthorizationRequestVersionResolverMap.get(bizType);
    if (null == requestVersionResolver) {
      return faceAuthInput.getVersion();
    }

    String version = requestVersionResolver.resolveVersion(faceAuthInput);
    return version;
  }

  // 老版本的回调就是回跳
  private void adapterV1FaceReturnUrl(String version, FaceAuthInput faceAuthInput) {
    if (VersionSupport.VERSION_1_0_0.equals(version)) {
      String callbackUrl = faceAuthInput.getCallbackUrl();
      faceAuthInput.setReturnUrl(callbackUrl);
    }
  }

  private void configurationRequest(FaceAuthInput input, String requestVersion) {
    AbstractFaceAuthorizationRequestRefresher requestRefresher =
        faceAuthorizationRequestRefresherMap.get(requestVersion);
    if (null == requestRefresher) {
      return;
    }
    requestRefresher.refreshFaceAuthorizationRequest(input);
  }

  private void persistentFaceAuthorizationRequestVersion(
          SupportResult<FaceAuthResult> output, String requestVersion) {
    if (output.ifSuccess()) {
      requestVersionResolver.putFaceAuthorizationRequestVersion(
          output.getData().getFaceAuthId(), requestVersion);
    }
  }

  private ConfigurableFaceAuthorizationService deduceFaceAuthorizationService(
      String faceId, String defaultVersion) {
    String version = deduceFaceAuthorizationServiceVersion(faceId, defaultVersion);
    version = (StringUtils.isEmpty(version) ? RequestVersionResolver.DEFAULT_VERSION : version);
    return deduceActualFaceAuthorizationService(version);
  }

  private String deduceFaceAuthorizationServiceVersion(String faceId, String defaultVersion) {
    if (StringUtils.isEmpty(faceId)) {
      return defaultVersion;
    }

    String version = requestVersionResolver.resolveFaceRequestVersion(faceId, defaultVersion);
    return (StringUtils.isEmpty(version) ? defaultVersion : version);
  }

  @Override
  public SupportResult<FaceAuthUserInfoResult> queryFaceAuthUserInfo(
      @RequestBody FaceAuthUserInfoInput faceAuthUserInfoInput) throws FaceException {
    ConfigurableFaceAuthorizationService faceAuthorizationService =
        deduceFaceAuthorizationService(
            faceAuthUserInfoInput.getFaceAuthId(), faceAuthUserInfoInput.getVersion());
    return faceAuthorizationService.queryFaceAuthUserInfo(faceAuthUserInfoInput);
  }



  @Override
  public SupportResult<?> refresh(BaseInput input) throws FaceException {
    return SupportResult.empty();
  }

  private abstract static class AbstractFaceAuthorizationRequestVersionResolver {

    abstract String resolveVersion(FaceAuthInput input);
  }

  private static class WillFaceAuthorizationRequestVersionResolver
      extends AbstractFaceAuthorizationRequestVersionResolver {

    private final Set<Integer> authModes;
    private final ConfigurableProperties configurableProperties;

    public WillFaceAuthorizationRequestVersionResolver(
        ConfigurableProperties configurableProperties) {
      this.configurableProperties = configurableProperties;
      this.authModes = prepareAuthModes();
    }

    private Set<Integer> prepareAuthModes() {
      Set<Integer> set = (new HashSet<>(4));
      set.add(FaceAuthModeEnum.TECENT_CLOUD_H5.getMode());
      set.add(FaceAuthModeEnum.TECENT_CLOUD_XCX.getMode());
      return set;
    }

    @Override
    String resolveVersion(FaceAuthInput input) {
      FaceAuthModeEnum authMod = input.getFaceAuthMode();
      if (null == authMod) {
        return input.getVersion();
      }

      if (this.configurableProperties.isEnableChangeRequestVersionWithV1ForWillnessByTencentCloud()
          && this.authModes.contains(authMod.getMode())) {
        return RequestVersionResolver.DEFAULT_VERSION;
      }

      return input.getVersion();
    }
  }

  private abstract static class AbstractFaceAuthorizationRequestRefresher {

    abstract void refreshFaceAuthorizationRequest(FaceAuthInput input);
  }

  private static class Version1FaceAuthorizationRequestRefresher
      extends AbstractFaceAuthorizationRequestRefresher {

    @Override
    void refreshFaceAuthorizationRequest(FaceAuthInput input) {
      String callbackUrl = input.getCallbackUrl();
      if (StringUtils.isNotBlank(callbackUrl)) {
        return;
      }

      String returnUrl = input.getReturnUrl();
      if (StringUtils.isNotBlank(returnUrl)) {
        input.setCallbackUrl(returnUrl);
      }
    }
  }

  private FaceAuthStartFun<FaceAuthInput, SupportResult<FaceAuthResult>> faceStarter =
      (input) -> {
        refresh(input);

        // 2025/6/26  校验年纪
        faceSubjectAgeLegalComponent.checkAge(input.getIdNo(),input.getCertType());

        //实名还有部分老版本的请求，需要兼容callbackUrl
        String version = deduceFaceAuthorizationServiceVersion(input);
        this.adapterV1FaceReturnUrl(version, input);


        version = VersionSupport.VERSION_1_3_0;
        ConfigurableFaceAuthorizationService faceAuthorizationService =
            deduceActualFaceAuthorizationService(version);
        configurationRequest(input, version);

        SupportResult<FaceAuthResult> rpcOutput = faceAuthorizationService.startFaceAuth(input);

        if(rpcOutput.ifSuccess()){
          // 持久化请求版本，以支持使用 faceId 查询使用
          persistentFaceAuthorizationRequestVersion(rpcOutput, version);
        }

        if(rpcOutput.ifFail()){
          return rpcOutput;
        }
        // 补偿刷脸结果的延迟消息
        try {
          StartFaceSuccessTask faceSuccessTask = new StartFaceSuccessTask();
          faceSuccessTask.setBizCode(faceRequestResolver.detectBizCode2(input));
          faceSuccessTask.setBizId(input.getBizId());
          faceSuccessTask.setFaceAuthId(rpcOutput.getData().getFaceAuthId());
          faceSuccessTask.setVersion(version);
          mqManager.sendFaceStartSuccessTask(faceSuccessTask);
        }catch (Exception e){
          log.error("sendFaceStartSuccessTask error, input={}, e={}", JSON.toJSONString(input), e);
        }

        return rpcOutput;
      };

  private SupportResult<QueryFaceAuthResResult> getQueryFaceAuthResResultRpcOutput(
      @RequestBody FaceAuthResInput faceAuthResInput) {
    ConfigurableFaceAuthorizationService faceAuthorizationService =
        deduceFaceAuthorizationService(
            faceAuthResInput.getFaceAuthId(), faceAuthResInput.getVersion());

    SupportResult<QueryFaceAuthResResult> result = faceAuthorizationService.queryFaceAuthResult(faceAuthResInput);

    // 2025/7/7  刷脸合规
    return controlFaceDataPrivilegeComponent.controlData(faceAuthResInput, result);
  }


  @Override
  public SupportResult<WakeupFaceResult> wakeupFace(WakeupFaceInput input) {
    //先参数校验
    FaceAuthValidationUtils.validateBean(input);

    //加一个锁 防止并发过大
    final String lockKey = "faceauth_lock_wakeupFace_" + input.getFaceAuthCode();
    Elock eLock = LockFactory.getReentrantLock(lockKey);
    try {
      ProviderFaceResourceService providerFaceResourceService = providerFaceResourceFactory.getService(input.getFaceAuthModeValue());
      return eLock.lockAndProtect(2, ()-> SupportResult.success(providerFaceResourceService.wakeupFace(input)));
    } catch (BaseBizRuntimeException| FaceException  e) {
      throw e;
    } catch (Exception e) {
      log.error("getFaceResource error, input={}  e:{} ", input, e);
      throw FaceException.valueOf(FaceStatusCode.PROVIDER_REQUEST_TOO_MUCH);
    }
  }

  @Override
  public SupportResult<HandleFaceAuthorizationReturnResult> handleFaceAuthorizationReturn(HandleFaceAuthorizationReturnInput input) {
    ProviderFaceResourceService providerFaceResourceService = providerFaceResourceFactory.getService(input.getFaceAuthModeValue());
    HandleFaceAuthorizationReturnResult response = providerFaceResourceService.handleFaceAuthorizationReturn(input);
    return SupportResult.success(response);

  }
}
