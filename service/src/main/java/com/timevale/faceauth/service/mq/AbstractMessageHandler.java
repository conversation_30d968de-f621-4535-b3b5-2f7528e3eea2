package com.timevale.faceauth.service.mq;

import com.timevale.framework.mq.client.consumer.Listener;
import com.timevale.framework.mq.client.consumer.ReceiveResult;
import com.timevale.framework.mq.client.producer.Msg;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.nio.charset.Charset;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2019/11/1 下午7:14
 */
@Slf4j
public abstract class AbstractMessageHandler implements Listener {


  protected String topic;

  public AbstractMessageHandler(String topic) {
    this.topic = topic;
  }

  @Override
  public ReceiveResult receive(List<Msg> msgs) {

    if (CollectionUtils.isEmpty(msgs)) {
      return ReceiveResult.success();
    }
    for (Msg msg : msgs) {
      try {
        String message = StringUtils.toEncodedString(msg.getBody(), Charset.forName("UTF-8"));
        log.info("topic={} msgId:{} , body:{}", topic, msg.getMsgId(), message);
        execute(message);
      } catch (Exception e) {
        log.error("topic=" + topic + " insert error", e);
      }
    }

    return ReceiveResult.success();
  }

  public abstract void execute(String message);
}
