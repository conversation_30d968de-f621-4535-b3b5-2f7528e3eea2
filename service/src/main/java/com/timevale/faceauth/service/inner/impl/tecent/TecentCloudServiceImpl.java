package com.timevale.faceauth.service.inner.impl.tecent;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.timevale.faceauth.service.aop.TimeRecord;
import com.timevale.faceauth.service.bean.FaceAuthBaseInfo;
import com.timevale.faceauth.service.bean.TecentFaceAuthResponse;
import com.timevale.faceauth.service.bean.TecentFaceAuthResultResponse;
import com.timevale.faceauth.service.bean.TecentParam;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudUtil;
import com.timevale.faceauth.service.domain.provider.tencent.domain.TencentWebAppIdVersionUtil;
import com.timevale.faceauth.service.enums.TecentFaceAuthCodeEnum;
import com.timevale.faceauth.service.inner.FaceAuthExtService;
import com.timevale.faceauth.service.inner.FileService;
import com.timevale.faceauth.service.inner.TecentCloudService;
import com.timevale.faceauth.service.input.FaceAuthInput;
import com.timevale.faceauth.service.utils.OrderGenerator;
import com.timevale.faceauth.service.utils.TecentSignUtil;
import com.timevale.faceauth.service.utils.UUIDUtil;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import esign.utils.exception.SuperException;
import esign.utils.httpclient.HttpUtil;
import esign.utils.httpclient.Method;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/** <AUTHOR> on 2018/7/19 */
@Service
public class TecentCloudServiceImpl extends BaseTecentCloudServiceImpl
    implements TecentCloudService {

  private static final Logger logger = LoggerFactory.getLogger(TecentCloudServiceImpl.class);

  /** 获取h5刷脸id url */
  private static final String GET_H5_FACEID_URL =
      "https://idasc.webank.com/api/server/h5/geth5faceid";

  /** 开始调用h5刷脸认证url */
  private static final String START_H5_FACE_AUTH_URL = "https://ida.webank.com/api/web/login";

  /** 查询人脸识别结果信息url */
  private static final String SYS_FACE_AUTH_RESULT =
      "https://idasc.webank.com/api/server/sync?app_id=%s&nonce=%s&order_no=%s&version=%s&sign=%s&get_file=%d";

  /** 腾讯云api参数版本号常量 */
  private static final String VERSION = "1.0.0";

  /** 腾讯云返回值result 节点 */
  private static final String RESULT = "result";

  /** 应用id */
  private String appId;

  private ConfigurableProperties configurablePro;
  @Autowired private FaceAuthExtService faceAuthExtService;
  @Autowired private FileService fileService;

  @Autowired
  public TecentCloudServiceImpl(ConfigurableProperties configurableProperties) {
    this.configurablePro = configurableProperties;
    this.appId = configurablePro.getTencentCloudAppId();
  }

  @Override
  public TecentFaceAuthResponse startH5FaceAuth(
      String faceAuthId, FaceAuthInput input, String callbackUrl) {

    String orderNo = OrderGenerator.generateOrder();

    TecentFaceAuthResponse h5FaceIdResult = getH5FaceId(faceAuthId, input, orderNo);

    String h5faceId = h5FaceIdResult.getH5faceId();

    String ticket =
        TencentCloudUtil.getNonceTicket(
            h5FaceIdResult.getUserId(), TencentWebAppIdVersionUtil.getH5RecordVideoAppVersion());

    List<TecentParam> paramList =
        Arrays.asList(
            TecentParam.build("webankAppId", appId, true, true),
            TecentParam.build("orderNo", orderNo, true, true),
            TecentParam.build("userId", h5FaceIdResult.getUserId(), true, true),
            TecentParam.build("version", VERSION, true, true),
            TecentParam.build("h5faceId", h5faceId, true, true),
            TecentParam.build("nonce", UUIDUtil.generateUUID(), true, true),
            TecentParam.build("url", callbackUrl, false, true),
            TecentParam.build("resultType", "1", false, true),
            TecentParam.build(
                "from", input.getFrom() != null ? input.getFrom().getFrom() : "", false, true),
            TecentParam.build("ticket", ticket, true, false));

    JsonObject jsonObject = buildRequestParam(paramList);

    StringBuffer sb = new StringBuffer("?");
    jsonObject
        .entrySet()
        .forEach(
            jo -> {
              sb.append(jo.getKey()).append("=").append(jo.getValue().getAsString());
              sb.append("&");
            });
    String params = sb.deleteCharAt(sb.length() - 1).toString();

    h5FaceIdResult.setFaceAuthUrl(START_H5_FACE_AUTH_URL + params);

    logger.info("face url NONCE ticket : {}", ticket);

    return h5FaceIdResult;
  }

  @Override
  public TecentFaceAuthResponse startXcxFaceAuth(String faceAuthId, FaceAuthInput input) {

    String orderNo = OrderGenerator.generateOrder();

    // 获取腾讯云刷脸id
    TecentFaceAuthResponse h5FaceIdResult = getH5FaceId(faceAuthId, input, orderNo);

    // 腾讯云需要的字母和数字的 随机数
    String nonce = UUIDUtil.generateUUID();

    h5FaceIdResult.setWebankAppId(appId);
    h5FaceIdResult.setNonce(nonce);
    h5FaceIdResult.setVersion(VERSION);

    String ticket =
        getTecentCloudTicket(
            appId, getAccessTokenInCache(), TICKET_TYPE_NONCE, h5FaceIdResult.getUserId());

    // 构建签名参数值
    List<TecentParam> signList =
        Arrays.asList(
            TecentParam.build("webankAppId", appId, true, false),
            TecentParam.build("orderNo", orderNo, true, false),
            TecentParam.build("userId", h5FaceIdResult.getUserId(), true, false),
            TecentParam.build("version", VERSION, true, false),
            TecentParam.build("h5faceId", h5FaceIdResult.getH5faceId(), true, false),
            TecentParam.build("nonce", nonce, true, false),
            TecentParam.build("ticket", ticket, true, false));

    String sign = sign(signList);

    h5FaceIdResult.setSign(sign);

    return h5FaceIdResult;
  }

  @TimeRecord(name = "调用腾讯云查询人脸识别结果接口")
  @Override
  public TecentFaceAuthResultResponse queryFaceAuthResult(
      String faceAuthId, String orderNo, Boolean needFile) {
    FaceAuthBaseInfo faceAuthBaseInfo = faceAuthExtService.getFaceAuthBaseInfo(faceAuthId);
    /* 封装参数 */
    String nonce = UUIDUtil.generateUUID();
    List<TecentParam> paramList =
        Arrays.asList(
            TecentParam.build("app_id", appId, true, true),
            TecentParam.build("order_no", orderNo, true, true),
            TecentParam.build("version", VERSION, true, true),
            TecentParam.build("nonce", nonce, true, true),
            TecentParam.build("ticket", getTicketInCache(faceAuthId), true, false));
    String sign = sign(paramList);
    String api =
        String.format(SYS_FACE_AUTH_RESULT, appId, nonce, orderNo, VERSION, sign, needFile ? 1 : 0);
    logger.info(
        "query faceauth result params : {}, token : {}, ticket : {}",
        api,
        getAccessTokenInCache(),
        getTicketInCache(faceAuthId));
    try {
      JsonObject resultJson = HttpUtil.getBody(Method.Get, api, buildHttpConfig());
      String code = resultJson.get("code").getAsString();
      String msg = resultJson.get("msg").getAsString();
      TecentFaceAuthResultResponse response = null;
      JsonElement element = resultJson.get(RESULT);
      if (element != null) {
        JsonObject resultJsonObject = element.getAsJsonObject();
        if (resultJsonObject != null) {
          response =
              JsonUtils.json2pojo(resultJsonObject.toString(), TecentFaceAuthResultResponse.class);
        }
      }
      if (response == null) {
        response = new TecentFaceAuthResultResponse();
      }
      response.setMsg(msg);
      response.setCode(code);
      // 此处给出成功/失败标记
      return response;
    } catch (Exception e) {
      logger.error("Tecent cloud query face auth result fail : {}", e);
      throw new FaceException(FaceStatusCode.PROVIDER_FAILED_API);
    }
  }

  /**
   * 上传用户信息 获取人脸识别id
   *
   * @param faceAuthId
   * @param input
   * @param orderNo
   * @return
   */
  private TecentFaceAuthResponse getH5FaceId(
      String faceAuthId, FaceAuthInput input, String orderNo) {

    String photo = input.getPhoto();
    String photoBase64 = null;
    if (StringUtils.isNotBlank(photo)) {
      // 下载photo,获取照片base64编码数据
      photoBase64 = fileService.getPhotoBase64(photo);
    }
    String userId = input.getUserId();
    // 刷脸人的用户id，唯一标识，如果没传 就传faceAuthId当作userId
    if (StringUtils.isBlank(userId)) {
      userId = faceAuthId;
    }
    String ticket = getTicketInCache(userId);
    // 封装请求参数
    List<TecentParam> paramList =
        Arrays.asList(
            TecentParam.build("webankAppId", appId, true, true),
            TecentParam.build("orderNo", orderNo, true, true),
            TecentParam.build("name", input.getName(), true, true),
            TecentParam.build("idNo", input.getIdNo(), true, true),
            TecentParam.build("userId", userId, true, true),
            TecentParam.build("version", VERSION, true, true),
            TecentParam.build("sourcePhotoStr", photoBase64, false, true),
            TecentParam.build("sourcePhotoType", input.getPhotoType().getType(), false, true),
            TecentParam.build("ticket", ticket, true, false));

    // 发送post请求
    try {
      JsonObject requestParam = buildRequestParam(paramList);
      JsonObject resultJson =
          HttpUtil.postBody(Method.Post, GET_H5_FACEID_URL, requestParam, buildHttpConfig());
      // 替换base64 使用 filekey 打印
      requestParam.addProperty("sourcePhotoStr", photo);
      logger.info(
          "Tecent cloud get h5 face id request params : {}, token : {}, ticket : {}",
          requestParam.toString(),
          getAccessTokenInCache(),
          ticket);

      String code = resultJson.get("code").getAsString();
      if (TecentFaceAuthCodeEnum.SUCCESS_CODE.getCode().equals(code)) {
        // 更新获取刷脸地址成功标记
        JsonObject result = resultJson.getAsJsonObject("result");
        String bizSeqNo = result.get("bizSeqNo").getAsString();
        String h5faceId = result.get("h5faceId").getAsString();
        logger.info("h5faceId is {}", h5faceId);
        TecentFaceAuthResponse tecentFaceAuthResponse =
            new TecentFaceAuthResponse(bizSeqNo, orderNo, h5faceId);
        tecentFaceAuthResponse.setUserId(userId);
        return tecentFaceAuthResponse;
      } else {
        String msg = resultJson.get("msg").getAsString();
        logger.error("Tecent cloud get h5 face id fail : {}", msg);
        throw new FaceException(FaceStatusCode.FACE_FAILED_AUTHORIZATION_INITIALIZE, msg);
      }
    } catch (SuperException e) {
      logger.error("Tecent cloud get h5 face id fail : {}", e);
      throw new FaceException(FaceStatusCode.ERR_INNER);
    }
  }

  /**
   * 获取api调用签署值
   *
   * @param paramList
   * @return
   */
  private String sign(List<TecentParam> paramList) {
    // 获取签名数据
    List<String> data = new ArrayList<>();
    paramList.stream()
        .filter(
            sp -> {
              if (sp.isSign() && sp.getValue() != null) {
                return true;
              }
              return false;
            })
        .forEach(sp -> data.add(sp.getValue()));

    // 计算签名
    String sign = TecentSignUtil.sign(data);

    return sign;
  }

  /**
   * 构建请求参数
   *
   * @param paramList
   * @return
   */
  private JsonObject buildRequestParam(List<TecentParam> paramList) {
    // 设置post参数
    List<TecentParam> requestParamList =
        paramList.stream()
            .filter(
                sp -> {
                  if (sp.isParam() && sp.getValue() != null) {
                    return true;
                  }
                  return false;
                })
            .collect(Collectors.toList());

    String sign = sign(paramList);

    requestParamList.add(TecentParam.build("sign", sign, false, true));

    JsonObject jsonObject = new JsonObject();
    requestParamList.forEach(sp -> jsonObject.addProperty(sp.getName(), sp.getValue()));

    return jsonObject;
  }
}
