package com.timevale.faceauth.service.utils.providerLog.result;

import com.alipay.api.response.AlipayUserCertifyOpenQueryResponse;
import com.timevale.faceauth.service.utils.providerLog.AbstractProviderLogResultResolver;
import com.timevale.faceauth.service.utils.providerLog.ProviderActionEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderKeyIdEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogConfiguration;
import java.util.List;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
public class AliMiniProgQueryResult
    extends AbstractProviderLogResultResolver<AlipayUserCertifyOpenQueryResponse> {

  public static final String SUCCESS_MSG = "Success";

  public AliMiniProgQueryResult(String bizId, long responseTime) {
    super(bizId, responseTime);
  }

  @Override
  public ProviderKeyIdEnum getProviderKeyId() {
    return ProviderKeyIdEnum.AliMiniProg;
  }

  @Override
  public ProviderActionEnum getAction() {
    return ProviderActionEnum.QUERY;
  }

  @Override
  public String getCode(AlipayUserCertifyOpenQueryResponse response) {
    return response.getCode();
  }

  @Override
  public String getMsg(AlipayUserCertifyOpenQueryResponse response) {
    if(SUCCESS_MSG.equals(response.getMsg())){
        //10000SuccessT 刷脸成功； 10000SuccessF 刷脸失败 先判断失败 兼容原有埋点判断
        return response.getMsg() + response.getPassed();
    }
    return response.getMsg();
  }

  @Override
  public String getResult(AlipayUserCertifyOpenQueryResponse response) {
    return null;
  }

  @Override
  public List<String> getFieldEmptyMappingStautsUnknown() {
    return ProviderLogConfiguration.aliMiniProgActionQueryFieldEmptyMappingStautsUnknown;
  }

  @Override
  public List<String> getCodeMsgMappingStautsFailure() {
    return ProviderLogConfiguration.aliMiniProgActionQueryCodeMsgMappingStautsFailure;
  }

  @Override
  public List<String> getCodeMsgMappingStautsSuccess() {
    return ProviderLogConfiguration.aliMiniProgActionQueryCodeMsgMappingStautsSuccess;
  }
}
