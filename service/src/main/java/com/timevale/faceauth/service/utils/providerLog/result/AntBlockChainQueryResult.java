package com.timevale.faceauth.service.utils.providerLog.result;

import cn.com.antcloud.api.realperson.v1_0_0.response.QueryFacevrfServerResponse;
import com.timevale.faceauth.service.utils.providerLog.AbstractProviderLogResultResolver;
import com.timevale.faceauth.service.utils.providerLog.ProviderActionEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderKeyIdEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogConfiguration;
import java.util.List;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
public class AntBlockChainQueryResult
    extends AbstractProviderLogResultResolver<QueryFacevrfServerResponse> {

  public AntBlockChainQueryResult(String bizId, long responseTime) {
    super(bizId, responseTime);
  }

  @Override
  public ProviderKeyIdEnum getProviderKeyId() {
    return ProviderKeyIdEnum.AntBlockChain;
  }

  @Override
  public ProviderActionEnum getAction() {
    return ProviderActionEnum.QUERY;
  }

  @Override
  public String getCode(QueryFacevrfServerResponse response) {
    return response.getResultCode();
  }

  @Override
  public String getMsg(QueryFacevrfServerResponse response) {
    return response.getResultMsg();
  }

  @Override
  public String getResult(QueryFacevrfServerResponse response) {
    return null;
  }

  @Override
  public List<String> getFieldEmptyMappingStautsUnknown() {
    return ProviderLogConfiguration.antBlockChainActionQueryFieldEmptyMappingStautsUnknown;
  }

  @Override
  public List<String> getCodeMsgMappingStautsFailure() {
    return ProviderLogConfiguration.antBlockChainActionQueryCodeMsgMappingStautsFailure;
  }

  @Override
  public List<String> getCodeMsgMappingStautsSuccess() {
    return ProviderLogConfiguration.antBlockChainActionQueryCodeMsgMappingStautsSuccess;
  }
}
