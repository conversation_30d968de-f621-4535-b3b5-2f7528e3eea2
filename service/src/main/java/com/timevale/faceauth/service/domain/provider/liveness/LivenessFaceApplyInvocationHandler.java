package com.timevale.faceauth.service.domain.provider.liveness;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.processor.FaceRequestContext;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationData;
import com.timevale.faceauth.service.domain.provider.support.FaceAuthorizationInitializingContext;
import com.timevale.faceauth.service.domain.support.PlaintextResourceStorageService;
import com.timevale.faceauth.service.enums.FaceAuthPhotoTypeEnum;
import com.timevale.faceauth.service.enums.FaceBizContextConstants;
import com.timevale.faceauth.service.esignface.config.EsignFaceConfig;
import com.timevale.faceauth.service.liveness.facade.LivenessApplyResult;
import com.timevale.faceauth.service.liveness.support.*;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogService;
import com.timevale.faceauth.service.utils.providerLog.result.LivenessInitResult;
import com.timevale.mandarin.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 活体刷脸请求处理
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/3/11
 */
@Slf4j
@Component
public class LivenessFaceApplyInvocationHandler {

  private final LivenessAuthorizationApplyService livenessAuthorizationApplyService;
  private final LivenessAuthorizationCutOverPointHolder livenessAuthorizationCutOverPointHolder;
  private final PlaintextResourceStorageService plaintextResourceStorageService;
  private final Map<String, LivenessAuthorizationPhotoType> livenessPhotoTypes;
  @Autowired private ProviderLogService providerLogService;
  @Autowired private EsignFaceConfig config;

  @Autowired
  public LivenessFaceApplyInvocationHandler(
          LivenessAuthorizationApplyService livenessAuthorizationApplyService,
          LivenessAuthorizationCutOverPointHolder livenessAuthorizationCutOverPointHolder,
          PlaintextResourceStorageService plaintextResourceStorageService) {
    this.livenessAuthorizationApplyService = livenessAuthorizationApplyService;
    this.livenessAuthorizationCutOverPointHolder = livenessAuthorizationCutOverPointHolder;
    this.plaintextResourceStorageService = plaintextResourceStorageService;
    this.livenessPhotoTypes = prepareLivenessPhotoTypes();
  }

  private Map<String, LivenessAuthorizationPhotoType> prepareLivenessPhotoTypes() {
    Map<String, LivenessAuthorizationPhotoType> map = (new HashMap<>(4));
    map.put(
            FaceAuthPhotoTypeEnum.HIGH_DEFINITION.name(),
            LivenessAuthorizationPhotoType.TYPE_HIGH_DEFINITION);
    map.put(
            FaceAuthPhotoTypeEnum.WATER_WAVE.name(), LivenessAuthorizationPhotoType.TYPE_WATER_MARK);
    return map;
  }

  ProviderFaceAuthorizationData invoke(
          FaceAuthorizationInitializingContext initializingContext, ConfigurableProviderService service)
          throws FaceException {
    LivenessAuthorizationApplyRequest request =
            LivenessAuthorizationApplyRequest.createBuilder()
                    .setLivenessType(LivenessAuthorizationType.TYPE_VIDEO_ACTION)
                    .setShowAgreement(this.isShowFaceAgreement(initializingContext))
                    .setAppId(initializingContext.getRequest().getAppId())
                    .setBizId(initializingContext.getFaceId())
                    .setBizType(LivenessAuthorizationBizType.TYPE_FACE_AUTHORIZATION)
                    .setName(initializingContext.getRequest().getName())
                    .setIdType(initializingContext.getRequest().getIdType())
                    .setIdNo(initializingContext.getRequest().getIdNo())
                    .build();
    LivenessApplyResult livenessApplyResult;
    long startTime = System.currentTimeMillis();
    try {
      livenessApplyResult = livenessAuthorizationApplyService.applyLiveness(request);
      providerLogService.logLivenessInit(
              new LivenessInitResult(
                      initializingContext.getFaceId(), System.currentTimeMillis() - startTime),
              request,
              livenessApplyResult);
    } catch (Exception cause) {
      providerLogService.logLivenessInitWithException(
              new LivenessInitResult(
                      initializingContext.getFaceId(), System.currentTimeMillis() - startTime),
              request,
              cause);
      log.warn(
              "Fail to apply liveness recognition at '" + initializingContext.getFaceId() + "' .",
              cause);
      throw FaceStatusCode.LIVENESS_FAIL_RECOGNITION.createException("Failure apply liveness .");
    }
    LivenessAuthorizationBeginningResolver beginningResolver =
            livenessAuthorizationCutOverPointHolder.getBeginningResolver();
    LivenessFaceBeginningRequest faceBeginningRequest =
            prepareFaceBeginningRequest(livenessApplyResult, request, initializingContext);
    String startPage;
    try {
      startPage = beginningResolver.resolveStartPage(faceBeginningRequest);
    } catch (Exception cause) {
      log.warn("Fail begin liveness at '" + livenessApplyResult.getLivenessId() + "' .", cause);
      throw FaceStatusCode.LIVENESS_FAIL_RECOGNITION.createException(
              "Failure begin liveness at '" + livenessApplyResult.getLivenessId() + "' .");
    }
    return ProviderFaceAuthorizationData.createBuilder()
            .setData(startPage)
            .setProvider(service.getProviderName())
            .setProviderName(service.getFullName())
            .setAuthorizeInput(resolveRequestContent(request, livenessApplyResult))
            .setOrderNo(livenessApplyResult.getLivenessId())
            .setExpireMinutes(config.getFaceH5UrlTimeout())
            .build();
  }

  /**
   * 是否展示刷脸协议页面，假如是实名SASS则不展示
   *
   * @param initializingContext
   * @return
   */
  private boolean isShowFaceAgreement(FaceAuthorizationInitializingContext initializingContext) {
    FaceRequestContext requestContext = initializingContext.getRequestContext();
    if (requestContext == null) {
      return true;
    }
    Map<String, String> bizContext = requestContext.bizContext();
    if (bizContext == null || bizContext.isEmpty()) {
      return true;
    }
//    String show = bizContext.get(FaceBizContextConstants.FACE_SHOW_AGREEMENT);
//    if (FaceBizContextConstants.FACE_SHOW_AGREEMENT_NO.equals(show)) {
//      return false;
//    }

    return true;
  }

  private LivenessFaceBeginningRequest prepareFaceBeginningRequest(
          LivenessApplyResult livenessApplyResult,
          LivenessAuthorizationApplyRequest request,
          FaceAuthorizationInitializingContext initializingContext) {
    return LivenessFaceBeginningRequest.createBuilder()
            .setFaceId(initializingContext.getFaceId())
            .setCallbackUrl(initializingContext.getCallbackUrl())
            .setReturnUrl(initializingContext.getReturnUrl())
            .setLivenessId(livenessApplyResult.getLivenessId())
            .setExpired(livenessApplyResult.getExpired())
            .setLivenessType(request.getLivenessType())
            .setAppId(request.getAppId())
            .setBizId(request.getBizId())
            .setBizType(request.getBizType())
            .setIdType(request.getIdType())
            .setIdNo(request.getIdNo())
            .setName(request.getName())
            .setPhoto(initializingContext.getPhotoKey())
            .setPhotoType(resolvePhotoType(initializingContext))
            .build();
  }

  private LivenessAuthorizationPhotoType resolvePhotoType(
          FaceAuthorizationInitializingContext initializingContext) {
    LivenessAuthorizationPhotoType photoType =
            livenessPhotoTypes.get(initializingContext.getPhotoType());
    return (null == photoType ? LivenessAuthorizationPhotoType.TYPE_WATER_MARK : photoType);
  }

  private String resolveRequestContent(
          LivenessAuthorizationApplyRequest request, LivenessApplyResult applyResult) {
    String content;
    try {
      content = JsonUtils.obj2json(request);
    } catch (Exception cause) {
      try {
        log.warn("Fail to serialization on request .", cause);
      } catch (Exception ignore) {
        // do nothing ...
      }
      return applyResult.getLivenessId();
    }

    try {
      return plaintextResourceStorageService.save(
              applyResult.getLivenessId() + "#request", content);
    } catch (Exception cause) {
      try {
        log.warn("Fail to store request content .", cause);
      } catch (Exception ignore) {
        // do nothing ...
      }
      return applyResult.getLivenessId();
    }
  }
}
