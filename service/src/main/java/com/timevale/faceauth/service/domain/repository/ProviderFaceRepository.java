package com.timevale.faceauth.service.domain.repository;

import com.timevale.faceauth.dal.pfs.face.support.ProviderFaceCompletionDO;
import com.timevale.faceauth.dal.pfs.face.support.ProviderFaceDAO;
import com.timevale.faceauth.dal.pfs.face.support.ProviderFaceDO;
import com.timevale.faceauth.dal.pfs.face.support.ProviderReturnDAO;
import com.timevale.faceauth.dal.pfs.face.support.ProviderReturnDO;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.utils.FaceAuthValidationUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 供应商刷脸信息仓库
 *
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/9/29 15
 */
@Slf4j
@Repository
public class ProviderFaceRepository {

  private final ProviderFaceDAO providerFaceDAO;
  private final ProviderReturnDAO providerReturnDAO;

  @Autowired
  public ProviderFaceRepository(
      ProviderFaceDAO providerFaceDAO, ProviderReturnDAO providerReturnDAO) {
    this.providerFaceDAO = providerFaceDAO;
    this.providerReturnDAO = providerReturnDAO;
  }

  public void saveProviderFaceInfo(ProviderFaceInfo faceInfo) throws FaceException {
    ProviderFaceDO entity = (new ProviderFaceDO());
    entity.faceId = faceInfo.getFaceId();
    entity.provider = faceInfo.getProvider();
    entity.fullName = faceInfo.getProviderName();
    entity.faceInput = faceInfo.getFaceInput();
    entity.faceData = faceInfo.getFaceData();
    entity.orderNo = faceInfo.getOrderNo();
    entity.isDone = (byte) (faceInfo.isDone() ? 0x01 : 0x00);
    entity.doneTime = faceInfo.getDoneTime();
    entity.thirdpartId = faceInfo.getThirdpardId();
    int rows;
    try {
      rows = providerFaceDAO.insert(entity);
    } catch (Exception cause) {
      throw FaceException.valueOf(
          FaceStatusCode.RESOURCE_FAILED_DATASOURCE,
          "Error persistence provider face authorization on '" + JsonUtils.obj2json(entity) + "'",
          cause);
    }
    if (0 >= rows) {
      throw FaceException.valueOf(
          FaceStatusCode.RESOURCE_FAILED_DATASOURCE,
          "Failed persistence provider face authorization on '" + JsonUtils.obj2json(entity) + "'");
    }
  }

  public ProviderFaceInfo getByFaceId(String faceId) throws FaceException {
    ProviderFaceDO entity;
    try {
      entity = providerFaceDAO.getByFaceId(faceId);
    } catch (Exception cause) {
      throw FaceException.valueOf(
          FaceStatusCode.RESOURCE_FAILED_DATASOURCE,
          "Error fetch provider face authorization on face '" + faceId + "' .",
          cause);
    }
    if (null == entity) {
      throw FaceException.valueOf(
          FaceStatusCode.FACE_NOT_FOUND_AUTHORIZATION,
          "Not found provider face authorization on face '" + faceId + "' .");
    }
    return ProviderFaceInfo.value(entity);
  }

  public ProviderFaceInfo getByProviderOrder(String provider, String order) throws FaceException {
    ProviderFaceDO entity;
    try {
      entity = providerFaceDAO.getByProviderOrder(provider, order);
    } catch (Exception cause) {
      log.error(
          "Error fetch provider face authorization on provider["
              + provider
              + "] at order["
              + order
              + "] .",
          cause);
      throw FaceException.valueOf(FaceStatusCode.RESOURCE_FAILED_DATASOURCE);
    }
    if (null == entity) {
      log.error(
          "Not found provider face authorization on provider["
              + provider
              + "] at order["
              + order
              + "] .");
      throw FaceException.valueOf(FaceStatusCode.FACE_NOT_FOUND_AUTHORIZATION);
    }
    return ProviderFaceInfo.value(entity);
  }

  public void completedProviderFace(ProviderFaceCompletion completion) throws FaceException {
    ProviderFaceCompletionDO entity = (new ProviderFaceCompletionDO());
    entity.faceId = completion.getFaceId();
    entity.isDone = (byte) (completion.isCompleted() ? 0x01 : 0x00);
    entity.doneTime = completion.getTimestamp();
    entity.errCode = completion.getErrCode();
    entity.errMsg = completion.getErrMsg();
    int rows;
    try {
      rows = providerFaceDAO.completedProviderFace(entity);
    } catch (Exception cause) {
      throw FaceException.valueOf(
          FaceStatusCode.RESOURCE_FAILED_DATASOURCE,
          "Error completed provider face authorization on '" + JsonUtils.obj2json(entity) + "'",
          cause);
    }
    if (0 >= rows) {
      throw FaceException.valueOf(
          FaceStatusCode.RESOURCE_FAILED_DATASOURCE,
          "Failed completed provider face authorization on '" + JsonUtils.obj2json(entity) + "'");
    }
  }

  public void saveProviderReturnInfo(ProviderReturnInfo returnInfo) throws FaceException {
    ProviderReturnDO entity = (new ProviderReturnDO());
    entity.faceId = returnInfo.getFaceId();
    entity.provider = returnInfo.getProvider();
    entity.returnType = returnInfo.getReturnType();
    entity.data = returnInfo.getData();
    entity.isOk = (byte) (returnInfo.isCompleted() ? 1 : 0);
    entity.errMsg = returnInfo.getErrorMsg();
    entity.errCode = returnInfo.getErrorCode();
    int rows;
    try {
      rows = providerReturnDAO.insert(entity);
    } catch (Exception cause) {
      throw FaceException.valueOf(
          FaceStatusCode.RESOURCE_FAILED_DATASOURCE,
          "Error persistence provider face return on '" + JsonUtils.obj2json(entity) + "' .",
          cause);
    }
    if (0 >= rows) {
      throw FaceException.valueOf(
          FaceStatusCode.RESOURCE_FAILED_DATASOURCE,
          "Failed persistence provider face return on '" + JsonUtils.obj2json(entity) + "' .");
    }
  }

  public ProviderReturnInfo getProviderReturn(String faceId, String returnType)
      throws FaceException {
    ProviderReturnDO entity;
    try {
      entity = providerReturnDAO.getProviderReturn(faceId, returnType);
    } catch (Exception cause) {
      throw FaceException.valueOf(
          FaceStatusCode.RESOURCE_FAILED_DATASOURCE,
          "Error fetch return context on authorization[" + faceId + "/" + returnType + "] .",
          cause);
    }
    if (null == entity) {
      log.warn("");
      return null;
    }

    return ProviderReturnInfo.valueOf(entity);
  }

  public ProviderReturnInfo getProviderReturnWithOutType(String faceId)
          throws FaceException {
    ProviderReturnDO entity;
    try {
      entity = providerReturnDAO.getProviderReturnWithOutType(faceId);
    } catch (Exception cause) {
      throw FaceException.valueOf(
              FaceStatusCode.RESOURCE_FAILED_DATASOURCE,
              "Error fetch return context on authorization[" + faceId +"] .",
              cause);
    }
    if (null == entity) {
      log.warn("");
      return null;
    }

    return ProviderReturnInfo.valueOf(entity);
  }

  public void updateProviderFaceThirdpartId(String thirdpartId, String faceInput, String faceId) {
    try {
      providerFaceDAO.updateProviderFaceThirdpartId(thirdpartId, faceInput, faceId);
    } catch (Exception cause) {
      throw FaceException.valueOf(FaceStatusCode.RESOURCE_FAILED_DATASOURCE, "Error update  return updateProviderFaceThirdpartId on faceId [" + faceId + "] .", cause);
    }
  }

  public void updateProviderFaceByFaceId(String orderNo, String faceData, String faceInput, String faceId) {
    try {
      providerFaceDAO.updateByFaceId(orderNo, faceData, faceInput, faceId);
    } catch (Exception cause) {
      throw FaceException.valueOf(FaceStatusCode.RESOURCE_FAILED_DATASOURCE, "Error update  return updateProviderFaceByFaceId on faceId [" + faceId + "] .", cause);
    }

  }

  public ProviderFaceInfo selectByProviderOrder(String order) {

    ProviderFaceDO entity = providerFaceDAO.selectByProviderOrder(order);
    FaceAuthValidationUtils.notNull(entity, FaceException.valueOf(FaceStatusCode.FACE_NOT_FOUND_AUTHORIZATION,
            "Not found provider face authorization on order '" + order + "' ."));
   return ProviderFaceInfo.value(entity);
  }
}
