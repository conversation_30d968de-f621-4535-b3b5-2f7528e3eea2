package com.timevale.faceauth.service.domain.repository.conv;

import com.timevale.faceauth.service.domain.FaceResource;
import com.timevale.faceauth.service.domain.UserFacePhotoResource;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.domain.repository.FaceOSSResources;

/**
 * <AUTHOR>
 * @since 2023/7/5 11:22
 */
public class UserFacePhotoResourceConv {

    public static UserFacePhotoResource conv(FaceInfo faceInfo, FaceOSSResources resources){
        UserFacePhotoResource photoResource = UserFacePhotoResource.createBuilder()
                .setOriginPhoto(faceInfo.getPhoto())
                .setName(faceInfo.getName())
                .setIdNo(faceInfo.getIdNo())
                .setProvider(faceInfo.getProvider())
                .setFaceId(faceInfo.getFaceId())
                .setResourceType(FaceResource.TYPE_RESOURCE_PHOTO)
                .setResourceContent(resources.getPhoto())
                .setMimeType(resources.getPhotoType())
                .build();
        return photoResource;
    }
}
