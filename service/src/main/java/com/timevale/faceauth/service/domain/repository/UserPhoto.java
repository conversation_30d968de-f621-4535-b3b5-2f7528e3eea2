package com.timevale.faceauth.service.domain.repository;

import com.timevale.faceauth.dal.pfs.face.support.UserPhotoDO;
import com.timevale.faceauth.service.core.support.ArgumentUtil;
import com.timevale.faceauth.service.domain.UserFactor2Token;
import lombok.Getter;
import org.springframework.util.StringUtils;
import sun.util.calendar.BaseCalendar;

import java.util.*;

/**
 * 用户照片
 *
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/31 17
 */
@Getter
public class UserPhoto {

  private final Date createTime;
  private final String idNo;
  private final String name;
  private final String hashFactor2;
  private final String photo;
  private final String photoType;

  private UserPhoto(UserPhotoBuilder builder) {
    this.createTime = builder.createTime;
    this.idNo = builder.idNo;
    this.name = builder.name;
    this.hashFactor2 = builder.hashFactor2;
    this.photo = builder.photo;
    this.photoType = builder.photoType;
  }

  public static UserPhotoBuilder createBuilder() {
    return (new UserPhotoBuilder());
  }

  static UserPhoto valueOf(UserPhotoDO entity) {
    return createBuilder()
        .setCreateTime(entity.createTime)
        .setIdNo(entity.idNo)
        .setName(entity.name)
        .setHashFactor2(entity.hashFactor2)
        .setPhoto(entity.photo)
        .setPhotoType(entity.photoType)
        .build();
  }

  UserPhotoDO toEntity() {
    UserPhotoDO entity = (new UserPhotoDO());
    entity.createTime = getCreateTime();
    entity.idNo = getIdNo();
    entity.name = getName();
    entity.hashFactor2 = getHashFactor2();
    entity.photo = getPhoto();
    entity.photoType = getPhotoType();
    return entity;
  }

  public static class UserPhotoBuilder {

    private Date createTime;
    private String idNo;
    private String name = "";
    private String hashFactor2 = "";
    private String photo;
    private String photoType;

    public UserPhoto build() {
      ensureHashingFactor2();
      return (new UserPhoto(this));
    }

    private void ensureHashingFactor2() {
      if (StringUtils.isEmpty(hashFactor2)) {
        hashFactor2 = UserFactor2Token.valueOf(idNo, name).getHashingValue();
      }
    }

    public UserPhotoBuilder setCreateTime(Date createTime) {
      ArgumentUtil.throwIfNull(createTime, "createTime");
      this.createTime = createTime;
      return this;
    }

    public UserPhotoBuilder setIdNo(String idNo) {
      ArgumentUtil.throwIfEmptyArgument(idNo, "idNo");
      this.idNo = idNo;
      return this;
    }

    public UserPhotoBuilder setName(String name) {
      this.name = name;
      return this;
    }

    public UserPhotoBuilder setHashFactor2(String hashFactor2) {
      this.hashFactor2 = hashFactor2;
      return this;
    }

    public UserPhotoBuilder setPhoto(String photo) {
      ArgumentUtil.throwIfEmptyArgument(photo, "photo");
      this.photo = photo;
      return this;
    }

    public UserPhotoBuilder setPhotoType(String photoType) {
      ArgumentUtil.throwIfEmptyArgument(photoType, "photoType");
      this.photoType = photoType;
      return this;
    }
  }
}
