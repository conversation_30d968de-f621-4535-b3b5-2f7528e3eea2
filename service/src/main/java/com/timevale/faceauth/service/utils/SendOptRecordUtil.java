package com.timevale.faceauth.service.utils;

import com.timevale.component.identity.record.RecordMessage;
import com.timevale.component.identity.record.RecordResult;
import com.timevale.component.identity.record.constants.OptProcess;
import com.timevale.component.identity.record.constants.OptProductChild;
import com.timevale.component.identity.record.constants.OptProvider;
import com.timevale.component.identity.record.constants.OptResult;
import com.timevale.component.identity.record.constants.OptVersion;
import com.timevale.component.identity.sensors.SensorsBool;
import com.timevale.component.identity.sensors.SensorsData;
import com.timevale.component.identity.sensors.SensorsEvent;
import com.timevale.component.identity.sensors.constants.SensorsExtendField;
import com.timevale.faceauth.service.bean.FaceAccountBaseInfo;
import com.timevale.faceauth.service.constant.SystemConfig;
import com.timevale.faceauth.service.mq.MQManager;
import com.timevale.mandarin.weaver.utils.RequestContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2019/8/12 下午7:48
 */
@Component
@Slf4j
public class SendOptRecordUtil {

  @Autowired private SystemConfig systemConfig;

  @Autowired private MQManager notifyUtil;


  public void sendFaceProcessNotRecord(SensorsDataDTO sensorsDataDTO) {
    RecordMessage msg = new RecordMessage();
    // 2025/4/28
    this.setEnvAndVersion(msg);

    if (Objects.nonNull(sensorsDataDTO.getProvider())) {
      msg.setProvider(sensorsDataDTO.getProvider().name());
      msg.setProviderDesc(sensorsDataDTO.getProvider().getDesc());
    }

    if (Objects.nonNull(sensorsDataDTO.getFaceInfo())) {
      msg.setAppId(sensorsDataDTO.getFaceInfo().getAppId());
      msg.setName(sensorsDataDTO.getFaceInfo().getName());
      msg.setCardNo(sensorsDataDTO.getFaceInfo().getIdNo());
    }

    Map<String, String> eventExtend = new HashMap<>();
    if (Objects.nonNull(sensorsDataDTO.getDowngradeCodeDTO())) {
      eventExtend.put("downgrade", sensorsDataDTO.getDowngradeCodeDTO().getDesc());
      eventExtend.put("downgrade_code", sensorsDataDTO.getDowngradeCodeDTO().getCode());
    }
    if (org.apache.commons.lang3.StringUtils.isNotBlank(sensorsDataDTO.getFace_processtype())) {
      eventExtend.put("face_processtype", sensorsDataDTO.getFace_processtype());
    }

    String code = sensorsDataDTO.getSensorsEvent().getCode();
    SensorsData sensorsData = new SensorsData(msg, null, code, eventExtend);
    // 2025/4/28
    sensorsData.setIsDBRecord(false);
    notifyUtil.sendOperationLog(sensorsData);
  }


  /** 发送刷脸结果 */
  public void sendFaceOptionRecord(
          OptProvider provider,
          OptProductChild productChild,
          OptProcess process,
          FaceAccountBaseInfo baseInfo,
          String faceAuthId,
          RecordResult recordResult) {
    RecordMessage msg = new RecordMessage();
    msg.setProduct(productChild.getProduct().name());
    msg.setProductChild(productChild.name());
    msg.setProvider(provider.name());
    msg.setProviderDesc(provider.getDesc());

    msg.setBizId(faceAuthId);
    msg.setProcess(process.name());
    this.setBase(msg, recordResult, baseInfo);

    sensorsFaceData(process, msg, baseInfo);
  }


  private String getAppId(FaceAccountBaseInfo baseInfo) {
    if (baseInfo == null) {
      return RequestContext.getAppId();
    }
    String appId = baseInfo.getAppId();
    if (StringUtils.isEmpty(appId)) {
      appId = RequestContext.getAppId();
    }
    return appId;
  }



  private void sensorsFaceData(
          OptProcess process, RecordMessage msg,FaceAccountBaseInfo accountBaseInfo) {
    //扩展字段
    boolean isFacePhoto =
            com.timevale.mandarin.base.util.StringUtils.isNoneBlank(accountBaseInfo.getPhoto());
    Map<String, String> eventExtend = new HashMap<>();
    eventExtend.put(SensorsExtendField.FACE_PHOTO_ORIGIN,SensorsBool.getResult(isFacePhoto).getTitle());

    SensorsEvent sensorsEvent = SensorsEvent.PSN_FACE_START;
    if (OptProcess.SEND == process) {
      sensorsEvent = SensorsEvent.PSN_FACE_START;
    } else if (OptProcess.CHECK == process) {
      sensorsEvent = SensorsEvent.PSN_FACE_END;
    }
    notifyUtil.sendOperationLog(new SensorsData(msg, null, sensorsEvent.getCode(), eventExtend));
  }

  private void setBase(RecordMessage msg, RecordResult recordResult, FaceAccountBaseInfo baseInfo) {
    setEnvAndVersion(msg);
    msg.setAppId(getAppId(baseInfo));
    msg.setIdNo(baseInfo.getIdNo());
    msg.setName(baseInfo.getName());
    msg.setFee(recordResult.getFee());
    msg.setProviderResult(OptResult.getResult(recordResult.get_PR()).getCode());
    msg.setResult(OptResult.getResult(recordResult.get_R()).getCode());
    msg.setResultDesc(recordResult.get_RDesc());
    Long startTime = recordResult.get_ST();
    Long endTime = recordResult.get_ET();

    msg.setProviderStartTime(startTime);
    msg.setProviderEndTime(endTime);
    if (startTime != null && endTime != null) {
      msg.setProviderCost((int) (endTime - startTime));
    }
  }

  private void setEnvAndVersion(RecordMessage msg) {
    msg.setEnv(systemConfig.getEnv());
    msg.setVersion(OptVersion.V1_PLUS.name());
  }
}
