package com.timevale.faceauth.service.utils.providerLog.result;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.timevale.faceauth.service.utils.providerLog.AbstractProviderLogResultResolver;
import com.timevale.faceauth.service.utils.providerLog.ProviderActionEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderKeyIdEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogConfiguration;
import java.util.List;
import org.springframework.http.ResponseEntity;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
public class TencentCloudInitResult
    extends AbstractProviderLogResultResolver<ResponseEntity<String>> {

  public TencentCloudInitResult(String bizId, long responseTime) {
    super(bizId, responseTime);
  }

  @Override
  public ProviderKeyIdEnum getProviderKeyId() {
    return ProviderKeyIdEnum.TencentCloud;
  }

  @Override
  public ProviderActionEnum getAction() {
    return ProviderActionEnum.INITIALIZATION;
  }

  @Override
  public String getCode(ResponseEntity<String> response) {
    JSONObject root = JSON.parseObject(response.getBody());
    return root.getString(RESPONSE_CONTENT_FIELD_CODE);
  }

  @Override
  public String getMsg(ResponseEntity<String> response) {
    JSONObject root = JSON.parseObject(response.getBody());
    return root.getString(RESPONSE_CONTENT_FIELD_MSG);
  }

  @Override
  public String getResult(ResponseEntity<String> response) {
    return null;
  }

  @Override
  public List<String> getFieldEmptyMappingStautsUnknown() {
    return ProviderLogConfiguration.tencentcloudActionInitializationFieldEmptyMappingStautsUnknown;
  }

  @Override
  public List<String> getCodeMsgMappingStautsFailure() {
    return ProviderLogConfiguration.tencentcloudActionInitializationCodeMsgMappingStautsFailure;
  }

  @Override
  public List<String> getCodeMsgMappingStautsSuccess() {
    return ProviderLogConfiguration.tencentcloudActionInitializationCodeMsgMappingStautsSuccess;
  }
}
