package com.timevale.faceauth.service.domain.provider.tencent;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/18 18
 */
public class TencentCloudCertificationInitializationUploadResultWrap {

  private final String input;
  private final TencentCloudCertificationInitializationUploadResult result;

  TencentCloudCertificationInitializationUploadResultWrap(
          String input, TencentCloudCertificationInitializationUploadResult result) {
    this.input = input;
    this.result = result;
  }

  public String getInput() {
    return input;
  }

  public TencentCloudCertificationInitializationUploadResult getResult() {
    return result;
  }
}
