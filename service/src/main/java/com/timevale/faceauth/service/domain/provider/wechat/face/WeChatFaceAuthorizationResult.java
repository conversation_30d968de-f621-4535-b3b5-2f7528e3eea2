package com.timevale.faceauth.service.domain.provider.wechat.face;

import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import com.timevale.faceauth.service.domain.provider.support.AbstractProviderFaceAuthorizationResult;

/**
 * 微信小程序刷脸认证的刷脸模式的认证结果
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/6/9
 */
class WeChatFaceAuthorizationResult extends AbstractProviderFaceAuthorizationResult {

  private WeChatFaceAuthorizationResult(WeChatFaceAuthorizationResultBuilder builder) {
    super(builder);
  }

  static WeChatFaceAuthorizationResultBuilder createBuilder() {
    return (new WeChatFaceAuthorizationResultBuilder());
  }

  static WeChatFaceAuthorizationResultBuilder createBuilder(String provider) {
    return (new WeChatFaceAuthorizationResultBuilder(provider));
  }

  static class WeChatFaceAuthorizationResultBuilder
      extends ProviderFaceAuthorizationResultBuilder<
          WeChatFaceAuthorizationResult, WeChatFaceAuthorizationResultBuilder> {

    public WeChatFaceAuthorizationResultBuilder() {
      super(ConfigurableProviderService.PROVIDER_WE_CHAT_FACE);
    }

    public WeChatFaceAuthorizationResultBuilder(String provider) {
      super(provider);
    }

    @Override
    public WeChatFaceAuthorizationResult build() {
      if(isSuccess()){
        this.refreshContext();
        // 微信小程序扫脸不返回 刷脸活体检测得分，赋默认值
        this.refreshLiveRate();
      }
      return (new WeChatFaceAuthorizationResult(this));
    }
  }
}
