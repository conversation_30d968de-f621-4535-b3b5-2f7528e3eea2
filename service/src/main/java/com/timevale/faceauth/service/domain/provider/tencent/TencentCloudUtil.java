package com.timevale.faceauth.service.domain.provider.tencent;

import com.google.common.hash.Hashing;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.support.ArgumentUtil;
import com.timevale.faceauth.service.core.utils.SpringUtils;
import com.timevale.faceauth.service.domain.provider.tencent.domain.TencentWebAppIdVersionHookDTO;
import com.timevale.faceauth.service.domain.provider.tencent.domain.TencentWebAppIdVersionUtil;
import com.timevale.faceauth.service.domain.repository.ProviderFaceInfo;
import com.timevale.faceauth.service.lang.DateFormat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/18 16
 */
@Slf4j
public abstract class TencentCloudUtil {

  private static QCloudAccessResolverHolder accessResolverHolder;

  public static String getDefaultTicket(String faceId, TencentWebAppIdVersion appIdVersion) throws FaceException {

    TencentCloudTicketResolver ticketResolver = getAccessResolver().getTicketResolver();
    return ticketResolver.resolveDefaultTicket(faceId ,appIdVersion).getValue();
  }

  /**
   * 获取后端 缓存 ticket
   *
   * @param faceId
   * @return
   */
  public static String getTicketCache(String faceId, TencentWebAppIdVersion appIdVersion) {

    if (appIdVersion.getAccessHolder().isDoRefresh()) {
      return getTicket(faceId, appIdVersion);
    }
    return getDefaultTicket(faceId,appIdVersion);
  }

  public static String getTicket(String faceId, TencentWebAppIdVersion appIdVersion)
          throws FaceException {
    return getTicket0(faceId, appIdVersion).getValue();
  }



  /**
   * https://cloud.tencent.com/document/product/1007/37306 NONCE ticket 是合作方前端包含 App 和 H5
   * 等生成签名鉴权参数之一，启动 H5 或 SDK 人脸核身。 API ticket 的 NONCE 类型，其有效期为120秒，且一次性有效，即每次启动 SDK 刷脸都要重新请求 NONCE
   * ticket。
   *
   * @param faceId
   * @return
   * @throws FaceException
   */
  public static String getNonceTicket(String faceId, TencentWebAppIdVersion appIdVersion)
          throws FaceException {
    ArgumentUtil.throwIfEmptyArgument(faceId, "faceId");
    TencentCloudAccessToken accessToken = obtainAccessToken(appIdVersion);
    TencentCloudTicketResolver ticketResolver = getAccessResolver().getTicketResolver();
    TencentCloudTicket ticket =
            ticketResolver.resolveTicket(
                    faceId, accessToken, TencentCloudTicketType.TYPE_NONCE, appIdVersion);
    return ticket.getValue();
  }

  private static TencentCloudTicket getTicket0(String faceId, TencentWebAppIdVersion appIdVersion)
          throws FaceException {
    TencentCloudAccessToken token = obtainAccessToken(appIdVersion);
    TencentCloudTicketResolver ticketResolver = getAccessResolver().getTicketResolver();
    return ticketResolver.resolveTicket(
            faceId, token, TencentCloudTicketType.TYPE_SIGN, appIdVersion);
  }

  public static TencentCloudAccessToken getAccessToken(TencentWebAppIdVersion appIdVersion) throws FaceException {
    return obtainAccessToken( appIdVersion);
  }

  private static TencentCloudAccessToken obtainAccessToken(TencentWebAppIdVersion appIdVersion) throws FaceException {
    return getAccessResolver().getAccessTokenResolver().resolveAccessToken( appIdVersion);
  }

  private static QCloudAccessResolverHolder getAccessResolver() {
    if (null == accessResolverHolder) {
      synchronized (QCloudAccessResolverHolder.class) {
        if (null == accessResolverHolder) {
          accessResolverHolder = SpringUtils.getBean(QCloudAccessResolverHolder.class);

        }
      }
    }
    return accessResolverHolder;
  }


  public static TencentWebAppIdVersion getProviderVersion(String appId, String version) {
    //走新逻辑
    TencentWebAppIdVersion appIdVersionHook = TencentWebAppIdVersionUtil.getAppVersionByAppId(TencentWebAppIdVersionHookDTO.ABILITY_H5, appId);
    if (Objects.nonNull(appIdVersionHook)) {
      return appIdVersionHook;
    }

    appIdVersionHook = TencentWebAppIdVersionUtil.getAppVersionByVersion(TencentWebAppIdVersionHookDTO.ABILITY_H5, version);
    if (Objects.nonNull(appIdVersionHook)) {
      return appIdVersionHook;
    }
    return TencentWebAppIdVersionUtil.getH5RecordVideoAppVersion();
//    // 2024/8/26 打error重点关注 mangcao
//    log.error("走到逻辑  需要重点关注 : appId = {}  version = {}", appId, version);
//    return TencentWebAppIdVersionEnum.getByVersion(version);
  }

 public static String signature(String... args) {
    String[] buffer =
            Arrays.stream(args).filter(StringUtils::hasLength).sorted().toArray(String[]::new);
    String plaintext = String.join("", buffer);
    //noinspection UnstableApiUsage
    return Hashing.sha1().hashString(plaintext, StandardCharsets.UTF_8).toString().toUpperCase();
  }

  static long revokerDoneTime(
          TencentCloudCertificationQueryResult result, ProviderFaceInfo providerFaceInfo) {
    try {

      DateFormat dateFormat = SpringUtils.getBean(DateFormat.class);
      Date occurredTime = dateFormat.toDate(String.valueOf(result.getOccurredTime()));
      return occurredTime.getTime();
    } catch (Exception e) {
      log.warn("revoker tencent cloud face do time fail,{}", e.getMessage());
      return providerFaceInfo.getDoneTime();
    }
  }
}
