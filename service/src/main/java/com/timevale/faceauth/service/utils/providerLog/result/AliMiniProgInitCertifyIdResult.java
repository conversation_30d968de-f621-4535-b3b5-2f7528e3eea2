package com.timevale.faceauth.service.utils.providerLog.result;

import com.alipay.api.response.AlipayUserCertifyOpenInitializeResponse;
import com.timevale.faceauth.service.apispace.facade.com.timevale.apispace.request.ApiResponseBaseMetric;
import com.timevale.faceauth.service.utils.providerLog.AbstractProviderLogResultResolver;
import com.timevale.faceauth.service.utils.providerLog.ProviderActionEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderKeyIdEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogConfiguration;
import java.util.List;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
public class AliMiniProgInitCertifyIdResult
    extends AbstractProviderLogResultResolver<AlipayUserCertifyOpenInitializeResponse> {

  public AliMiniProgInitCertifyIdResult(String bizId, long responseTime) {
    super(bizId, responseTime);
  }

  @Override
  public ProviderKeyIdEnum getProviderKeyId() {
    return ProviderKeyIdEnum.AliMiniProg;
  }

  @Override
  public ProviderActionEnum getAction() {
    return ProviderActionEnum.INITIALIZATION;
  }

  @Override
  public String getCode(AlipayUserCertifyOpenInitializeResponse response) {
    return response.getCode();
  }

  @Override
  public String getMsg(AlipayUserCertifyOpenInitializeResponse response) {
    return response.getMsg();
  }

  @Override
  public String getResult(AlipayUserCertifyOpenInitializeResponse response) {
    return null;
  }

  @Override
  public List<String> getFieldEmptyMappingStautsUnknown() {
    return ProviderLogConfiguration
        .aliMiniProgCertifyIdActionInitializationFieldEmptyMappingStautsUnknown;
  }

  @Override
  public List<String> getCodeMsgMappingStautsFailure() {
    return ProviderLogConfiguration
        .aliMiniProgCertifyIdActionInitializationCodeMsgMappingStautsFailure;
  }

  @Override
  public List<String> getCodeMsgMappingStautsSuccess() {
    return ProviderLogConfiguration
        .aliMiniProgCertifyIdActionInitializationCodeMsgMappingStautsSuccess;
  }

  @Override
  public ApiResponseBaseMetric resolver(AlipayUserCertifyOpenInitializeResponse response) {
    ApiResponseBaseMetric apiResponseBaseMetric = super.resolver(response);
    // 支付宝小程序初始化分为两步（第一步）
    apiResponseBaseMetric.setTag(String.valueOf(ATTRIBUTE_1));
    return apiResponseBaseMetric;
  }
}
