package com.timevale.faceauth.service.liveness.provider.support;

import com.timevale.faceauth.service.domain.FaceAuthorizationPhotoResolver;
import com.timevale.faceauth.service.domain.UserFacePhotoResource;
import com.timevale.faceauth.service.liveness.ConfigurableLivenessAuthorizationProperty;
import com.timevale.faceauth.service.liveness.provider.ConfigurableProviderLivenessService;
import com.timevale.faceauth.service.liveness.provider.ProviderLivenessAuthorizeContext;
import com.timevale.faceauth.service.liveness.provider.ProviderLivenessAuthorizeInvocationHandler;
import com.timevale.faceauth.service.liveness.provider.esign.ESignLivenessRecognitionResult;
import com.timevale.faceauth.service.liveness.provider.esign.support.ESignLivenessIdentificationResult;
import com.timevale.faceauth.service.liveness.repository.LivenessAuthorization;
import com.timevale.faceauth.service.liveness.repository.ProviderLivenessAuthorization;
import com.timevale.faceauth.service.liveness.repository.resolver.ProviderLivenessAuthorizationResolver;
import com.timevale.faceauth.service.liveness.resource.LivenessResource;
import com.timevale.faceauth.service.liveness.resource.LivenessResourceResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.function.Consumer;

/**
 * 第三方供应商活体认证业务处理过程基本框架
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/2/26
 */
@Slf4j
public abstract class AbstractProviderLivenessAuthorizeInvocationHandler
    implements ProviderLivenessAuthorizeInvocationHandler {

  @Autowired private FaceAuthorizationPhotoResolver faceAuthorizationPhotoResolver;
  private final ProviderLivenessAuthorizationResolver providerLivenessAuthorizationResolver;
  private final LivenessResourceResolver livenessResourceResolver;
  private final ProviderLivenessResourceResolver providerLivenessResourceResolver;

  public AbstractProviderLivenessAuthorizeInvocationHandler(
      ProviderLivenessAuthorizationResolver providerLivenessAuthorizationResolver,
      LivenessResourceResolver livenessResourceResolver,
      ProviderLivenessResourceResolver providerLivenessResourceResolver) {
    this.providerLivenessAuthorizationResolver = providerLivenessAuthorizationResolver;
    this.livenessResourceResolver = livenessResourceResolver;
    this.providerLivenessResourceResolver = providerLivenessResourceResolver;
  }

  @Override
  public ProviderLivenessAuthorizeResult invoke(
      LivenessAuthorization liveness, ConfigurableProviderLivenessService providerLivenessService) {
    prepareInvoke(liveness, providerLivenessService);
    ProviderLivenessAuthorizeContext context =
        prepareAuthorizationContext(liveness, providerLivenessService);
    ProviderLivenessAuthorizeResult result =
        ProviderLivenessAuthorizeResult.createBuilder()
            .setLiveness(liveness)
            .setProviderLiveness(context.getProviderLiveness())
            .setVideoAndAudio(context.getVideoAndAudio())
            .setPhoto(context.getPhoto())
            .setLiveRate(context.getLiveRate())
            .setSimilarity(context.getSimilarity())
            .setCompletedTimestampMillis(context.getCompletedTimestampMillis())
            .setFee(context.isFee())
            .setError(context.getError())
            .setResultMsg(context.getMessage())
            .setResultCode(context.getErrCode())
            .build();
    postInvoke(result, context);

    detectAuthorizationResult(result, context);
    return result;
  }

  private void detectAuthorizationResult(
      ProviderLivenessAuthorizeResult result, ProviderLivenessAuthorizeContext context) {
    if (!result.isSuccess()) {
      return;
    }
//    LivenessAuthorization authorization = context.getLiveness();
//    UserFacePhotoResource photoResource =
//        UserFacePhotoResource.createBuilder()
//            .setIdNo(authorization.getIdNo())
//            .setName(authorization.getName())
//            .setProvider(authorization.getProvider())
//            .setFaceId(authorization.getBizId())
//            .setResourceType(context.getResourceLocation())
//            .setMimeType("HIGH_DEFINITION")
//            .setResourceContent(context.getPhoto())
//            .build();
//    faceAuthorizationPhotoResolver.saveUserPhoto(photoResource);
  }

  protected void postInvoke(
      ProviderLivenessAuthorizeResult authorizeResult, ProviderLivenessAuthorizeContext context) {}

  protected void prepareInvoke(
      LivenessAuthorization liveness,
      ConfigurableProviderLivenessService providerLivenessService) {}

  protected ProviderLivenessAuthorizeContext prepareAuthorizationContext(
      LivenessAuthorization liveness, ConfigurableProviderLivenessService providerLivenessService) {
    ProviderLivenessAuthorization providerLiveness =
        providerLivenessAuthorizationResolver.resolveProviderLivenessAndThrowIfNotFound(
            liveness.getLivenessId());
    SimpleProviderLivenessAuthorizeContext context =
        (new SimpleProviderLivenessAuthorizeContext(
            liveness, providerLiveness, providerLivenessService, this));
    context.refresh();
    return context;
  }

  protected void configPhotoSource(
      ProviderLivenessAuthorizeContext context,
      Consumer<String> consumer,
      ValueConfigurationChain<ProviderLivenessAuthorizeContext> refresherChain) {
    ProviderLivenessAuthorization providerLiveness = context.getProviderLiveness();
    try {
      String source =
          livenessResourceResolver.resolveRecoverableResource(
              providerLiveness.getPhotoSrc(), providerLiveness.getPhotoLocation());
      consumer.accept(source);
    } catch (Exception cause) {
      log.warn("Failure resolve user photo .", cause);
    } finally {
      refresherChain.doRefresh(context);
    }
  }

  protected void configVideoAndAudio(
      ProviderLivenessAuthorizeContext context,
      Consumer<LivenessResource> consumer,
      ValueConfigurationChain<ProviderLivenessAuthorizeContext> refresherChain) {
    // Acquired the file-key first .
    String fileKey =
        providerLivenessResourceResolver.resolveApplyVideoAndAudio(
            context.getLiveness(), context.getProviderLiveness());
    LivenessResource resource =
        livenessResourceResolver.recoverableInheritResource(fileKey, context.getResourceLocation());
    consumer.accept(resource);
    refresherChain.doRefresh(context);
  }

  protected void doRecognition(
      ProviderLivenessAuthorizeContext context,
      Consumer<RecognitionResult> consumer,
      ValueConfigurationChain<ProviderLivenessAuthorizeContext> refresherChain) {
    refresherChain.doRefresh(context);
  }

  protected void configPhotoForVideo(
      ProviderLivenessAuthorizeContext context,
      Consumer<String> consumer,
      ValueConfigurationChain<ProviderLivenessAuthorizeContext> refresherChain) {
    String recognitionPhoto = resolveRecognitionPhoto(context);
    consumer.accept(recognitionPhoto);
    refresherChain.doRefresh(context);
  }

  private String resolveRecognitionPhoto(ProviderLivenessAuthorizeContext context) {
    String bestPhoto = resolveBestPhotoForVideo(context);
    if (StringUtils.hasLength(bestPhoto)) {
      LivenessResource resource =
          livenessResourceResolver.resolvePersistentResource(
              bestPhoto, context.getProviderLiveness().getPhotoLocation());
      return resource.getLocation();
    }
    return "";
  }

  protected abstract String resolveBestPhotoForVideo(ProviderLivenessAuthorizeContext context);

  protected abstract void configLiveRate(
      ProviderLivenessAuthorizeContext context,
      Consumer<Float> consumer,
      ValueConfigurationChain<ProviderLivenessAuthorizeContext> refresherChain);

  protected abstract void configSimilarity(
      ProviderLivenessAuthorizeContext context,
      Consumer<Float> consumer,
      ValueConfigurationChain<ProviderLivenessAuthorizeContext> refresherChain);

  protected void configCompletedTimestampMillis(
      ProviderLivenessAuthorizeContext context,
      Consumer<Long> consumer,
      ValueConfigurationChain<ProviderLivenessAuthorizeContext> refresherChain) {
    consumer.accept(System.currentTimeMillis());
    refresherChain.doRefresh(context);
  }

  protected void configRequireFee(
      ProviderLivenessAuthorizeContext context,
      Consumer<Boolean> consumer,
      ValueConfigurationChain<ProviderLivenessAuthorizeContext> refresherChain) {
    consumer.accept(true);
    refresherChain.doRefresh(context);
  }

  private static class SimpleProviderLivenessAuthorizeContext
      implements ProviderLivenessAuthorizeContext {

    private final LivenessAuthorization liveness;
    private final ProviderLivenessAuthorization providerLiveness;
    private final ConfigurableProviderLivenessService providerLivenessService;
    private final AbstractProviderLivenessAuthorizeInvocationHandler invocationHandler;
    private final ValueProperty<String> photoSrc;
    private final ValueProperty<LivenessResource> videoAndAudio;
    private final ValueProperty<RecognitionResult> recognition;
    private final ValueProperty<String> photo;
    private final ValueProperty<Float> liveRate;
    private final ValueProperty<Float> similarity;
    private final ValueProperty<Long> completedTimestampMillis;
    private final ValueProperty<Boolean> fee;

    private Exception cause;

    public SimpleProviderLivenessAuthorizeContext(
        LivenessAuthorization liveness,
        ProviderLivenessAuthorization providerLiveness,
        ConfigurableProviderLivenessService providerLivenessService,
        AbstractProviderLivenessAuthorizeInvocationHandler invocationHandler) {
      this.liveness = liveness;
      this.providerLiveness = providerLiveness;
      this.providerLivenessService = providerLivenessService;
      this.invocationHandler = invocationHandler;
      this.photoSrc = (new ValueProperty<>());
      this.videoAndAudio = (new ValueProperty<>());
      this.recognition = (new ValueProperty<>());
      this.photo = (new ValueProperty<>());
      this.liveRate = (new ValueProperty<>());
      this.similarity = (new ValueProperty<>());
      this.completedTimestampMillis = (new ValueProperty<>());
      this.fee = (new ValueProperty<>());
    }

    void refresh() {
      DelegateValueConfiguration<Long> completedConfigurationValue =
          (new DelegateValueConfiguration<>(
              this.completedTimestampMillis, invocationHandler::configCompletedTimestampMillis));
      InnerConfigurationValueChain chain =
          (new InnerConfigurationValueChain(
              // 配置源照片
              (new DelegateValueConfiguration<>(
                  this.photoSrc, invocationHandler::configPhotoSource)),
              // 配置认证音视频
              (new DelegateValueConfiguration<LivenessResource>(
                  this.videoAndAudio, invocationHandler::configVideoAndAudio)),
              // 执行认证
              (new DelegateValueConfiguration<RecognitionResult>(
                  this.recognition, invocationHandler::doRecognition)),
              // 配置比对照片
              (new DelegateValueConfiguration<>(
                  this.photo, invocationHandler::configPhotoForVideo)),
              // 配置活体率
              (new DelegateValueConfiguration<>(this.liveRate, invocationHandler::configLiveRate)),
              // 配置相似度
              (new DelegateValueConfiguration<>(
                  this.similarity, invocationHandler::configSimilarity)),
              // 配置计费状态
              (new DelegateValueConfiguration<>(this.fee, invocationHandler::configRequireFee)),
              // 配置完成时间
              completedConfigurationValue));
      try {
        chain.doRefresh(this);
      } catch (Exception cause) {
        log.warn("Failure refresh authorization context .", cause);
        this.cause = cause;
        InnerConfigurationValueChain completedChain =
            (new InnerConfigurationValueChain(completedConfigurationValue));
        while (completedChain.iterator.hasNext()) {
          try {
            completedChain.doRefresh(this);
          } catch (Exception cause0) {
            log.warn(
                "Failure completion recognition at '" + this.getLiveness().getLivenessId() + "' .",
                cause0);
          }
        }
      }
    }

    @Override
    public LivenessAuthorization getLiveness() {
      return liveness;
    }

    @Override
    public ProviderLivenessAuthorization getProviderLiveness() {
      return providerLiveness;
    }

    @Override
    public ConfigurableProviderLivenessService getProviderLivenessService() {
      return providerLivenessService;
    }

    @Override
    public String getPhotoSrc() {
      return photoSrc.getIfAbsent("");
    }

    @Override
    public String getMessage() {
      RecognitionResult recognitionResult = recognition.getIfAbsent(null);
      if (recognitionResult != null
          && com.timevale.mandarin.base.util.StringUtils.isNotBlank(
              recognitionResult.getMessage())) {
        return recognitionResult.getMessage();
      }
      return cause != null ? cause.getMessage() : null;
    }

    @Override
    public String getErrCode() {
      RecognitionResult recognitionResult = recognition.getIfAbsent(null);
      if (recognitionResult == null) {
        return null;
      }
      return recognitionResult.getErrCode();
    }

    @Override
    public float getLiveRate() {
      return liveRate.getIfAbsent(0.00F);
    }

    @Override
    public float getSimilarity() {
      return similarity.getIfAbsent(0.00F);
    }

    @Override
    public String getPhoto() {
      return photo.getIfAbsent("");
    }

    @Override
    public String getVideoAndAudio() {
      LivenessResource resource = videoAndAudio.getIfAbsent(null);
      return (null == resource ? "" : resource.getLocation());
    }

    @Override
    public String getBase64VideoAndAudio() {
      LivenessResource resource = videoAndAudio.getIfAbsent(null);
      return (null == resource ? "" : resource.getContent());
    }

    @Override
    public long getCompletedTimestampMillis() {
      return completedTimestampMillis.getIfAbsent(0L);
    }

    @Override
    public boolean isFee() {
      return fee.getIfAbsent(false);
    }

    @Override
    public Exception getError() {
      return cause;
    }
  }

  private static class ValueProperty<T> implements Consumer<T> {

    private T value;

    @Override
    public void accept(T t) {
      this.value = t;
    }

    T getIfAbsent(T defaultValue) {
      return (null == value ? defaultValue : value);
    }
  }

  private static class DelegateValueConfiguration<T>
      implements ValueConfiguration<ProviderLivenessAuthorizeContext> {

    private final Consumer<T> consumer;
    private final ValueConfigureInvocationHandler<ProviderLivenessAuthorizeContext, T> refresher;

    DelegateValueConfiguration(
        Consumer<T> consumer,
        ValueConfigureInvocationHandler<ProviderLivenessAuthorizeContext, T> refresher) {
      this.consumer = consumer;
      this.refresher = refresher;
    }

    @Override
    public void doRefresh(
        ProviderLivenessAuthorizeContext context,
        ValueConfigurationChain<ProviderLivenessAuthorizeContext> refresherChain) {
      this.refresher.doInvoke(context, this.consumer, refresherChain);
    }
  }

  private static class InnerConfigurationValueChain
      implements ValueConfigurationChain<ProviderLivenessAuthorizeContext> {

    private final Iterator<ValueConfiguration<ProviderLivenessAuthorizeContext>> iterator;

    InnerConfigurationValueChain(DelegateValueConfiguration<?>... refreshers) {
      List<ValueConfiguration<ProviderLivenessAuthorizeContext>> list = Arrays.asList(refreshers);
      this.iterator = list.iterator();
    }

    @Override
    public void doRefresh(ProviderLivenessAuthorizeContext context) {
      if (this.iterator.hasNext()) {
        ValueConfiguration<ProviderLivenessAuthorizeContext> refresher = this.iterator.next();
        refresher.doRefresh(context, this);
      }
    }
  }
}
