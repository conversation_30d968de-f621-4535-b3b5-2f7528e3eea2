package com.timevale.faceauth.service.utils.providerLog.result;

import com.timevale.faceauth.service.apispace.facade.com.timevale.apispace.enums.MarkStatusEnum;
import com.timevale.faceauth.service.apispace.facade.com.timevale.apispace.request.ApiResponseBaseMetric;
import com.timevale.faceauth.service.utils.providerLog.AbstractProviderLogResultResolver;
import com.timevale.faceauth.service.utils.providerLog.ProviderActionEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderKeyIdEnum;
import java.util.List;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
public class DingTalkInitResult extends AbstractProviderLogResultResolver<Void> {

  public DingTalkInitResult(String bizId, long responseTime) {
    super(bizId, responseTime);
  }

  @Override
  public ProviderKeyIdEnum getProviderKeyId() {
    return ProviderKeyIdEnum.DingTalk;
  }

  @Override
  public ProviderActionEnum getAction() {
    return ProviderActionEnum.INITIALIZATION;
  }

  @Override
  public String getCode(Void response) {
    return null;
  }

  @Override
  public String getMsg(Void response) {
    return null;
  }

  @Override
  public String getResult(Void response) {
    return null;
  }

  @Override
  public List<String> getFieldEmptyMappingStautsUnknown() {
    return null;
  }

  @Override
  public List<String> getCodeMsgMappingStautsFailure() {
    return null;
  }

  @Override
  public List<String> getCodeMsgMappingStautsSuccess() {
    return null;
  }

  @Override
  public ApiResponseBaseMetric resolver(Void response) {
    ApiResponseBaseMetric apiResponseBaseMetric = super.resolver(response);
    // 钉钉初始化主要是前端SDK去做鉴权，后端不调用供应商
    apiResponseBaseMetric.setMarkStatus(MarkStatusEnum.SUCCESS.name());
    return apiResponseBaseMetric;
  }
}
