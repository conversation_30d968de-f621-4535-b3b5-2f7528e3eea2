package com.timevale.faceauth.service.enums;

import com.timevale.component.identity.record.constants.OptProductChild;
import com.timevale.component.identity.record.constants.OptProvider;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import com.timevale.faceauth.service.domain.provider.ant.call.AntFaceV2InvocationHandler;
import com.timevale.faceauth.service.domain.provider.ant.call.AntFaceV3InvocationHandler;
import com.timevale.mandarin.base.util.StringUtils;

/**
 * 刷脸供应商操作日志
 *
 * <AUTHOR>
 * @since 2019/11/11 下午3:42
 */
public enum FaceOptLogProviderEnum {
  DEFAULT(
      OptProvider.DEFAULT.name(),
      ConfigurableProviderService.PROVER_DEFAULT_V,
      OptProvider.DEFAULT,
      OptProductChild.FACE_DEFAULT_FACE,
      "默认刷脸"),

  FACE_TECENT_CLOULD_V1(
      ConfigurableProviderService.PROVIDER_TENCENT_CLOUD,
          ConfigurableProviderService.PROVER_DEFAULT_V,
      OptProvider.FACE_TECENT_CLOULD,
      OptProductChild.FACE_AUTH_TECENT_CLOULD,
      "腾讯云刷脸"),

  FACE_TECENT_CLOULD_v2(
          ConfigurableProviderService.PROVIDER_TENCENT_CLOUD,
          ConfigurableProviderService.PROVER_TOW_V,
          OptProvider.FACE_TECENT_CLOULD,
          OptProductChild.FACE_AUTH_TECENT_CLOULD,
          "腾讯云刷脸"),

  FACE_WE_CHAT_FACE(
      ConfigurableProviderService.PROVIDER_WE_CHAT_FACE,
      ConfigurableProviderService.PROVER_DEFAULT_V,
      OptProvider.FACE_WE_CHAT_FACE,
      OptProductChild.FACE_WE_CHAT_FACE,
      "微信小程序刷脸"),

  FACE_ESIGN(
      ConfigurableProviderService.PROVIDER_LIVENESS,
      ConfigurableProviderService.PROVER_DEFAULT_V,
      OptProvider.FACE_ESIGN,
      OptProductChild.FACE_FACE_ESIGN_FACE,
      "e签宝刷脸"),

  FACE_AUDIO_VIDEO_DUAL(
      ConfigurableProviderService.PROVIDER_AUDIO_VIDEO_DUAL,
      ConfigurableProviderService.PROVER_DEFAULT_V,
      OptProvider.FACE_AUDIO_VIDEO_DUAL,
      OptProductChild.FACE_AUDIO_VIDEO_DUAL,
      "智能视频认证"),

  FACE_WE_CHAT_AUDIO_VIDEO_DUAL(
      ConfigurableProviderService.PROVIDER_AUDIO_VIDEO_DUAL,
      ConfigurableProviderService.PROVER_DEFAULT_V,
      OptProvider.FACE_AUDIO_VIDEO_DUAL,
      OptProductChild.FACE_WE_CHAT_AUDIO_VIDEO_DUAL,
      "微信视频认证"),

  FACE_ANT_V2(
      ConfigurableProviderService.PROVIDER_ANT_BLOCK_CHAIN,
      AntFaceV2InvocationHandler.API_VERSION,
      OptProvider.FACE_ANT,
      OptProductChild.FACE_AUTH_ANT_FACE,
      "蚂蚁区块链"),

  FACE_ANT_V3(
      ConfigurableProviderService.PROVIDER_ANT_BLOCK_CHAIN,
      AntFaceV3InvocationHandler.API_VERSION,
      OptProvider.FACE_ALIPAY_MINI,
      OptProductChild.FACE_FACE_ALIPAY_MINI_FACE,
      "蚂蚁区块链"),

  FACE_HI_MA_CREDIT(
      ConfigurableProviderService.PROVIDER_ZHI_MA_CREDIT,
      ConfigurableProviderService.PROVER_DEFAULT_V,
      OptProvider.FACE_ANT,
      OptProductChild.FACE_AUTH_ANT_FACE,
      "蚂蚁区块链"),

  FACE_ALIYUN(
      ConfigurableProviderService.PROVIDER_ALI_TENCENT_MINI_PROG,
      ConfigurableProviderService.PROVER_DEFAULT_V,
      OptProvider.FACE_ALIYUN_TENCENT_MINI,
      OptProductChild.FACE_FACE_ALIYUN_TENCENT_MINI_FACE,
      "阿里云刷脸"),

  FACE_DINGDING(
      ConfigurableProviderService.PROVIDER_DING_TALK,
      ConfigurableProviderService.PROVER_DEFAULT_V,
      OptProvider.FACE_DINGDING,
      OptProductChild.FACE_DINGDING_FACE,
      "钉钉刷脸"),

  FACE_ALIPAY_MINI(
      ConfigurableProviderService.PROVIDER_ALI_MINI_PROG,
      ConfigurableProviderService.PROVER_DEFAULT_V,
      OptProvider.FACE_ALIPAY_MINI,
      OptProductChild.FACE_FACE_ALIPAY_MINI_FACE,
      "支付宝小程序刷脸"),

  FACE_ALIPAY_MINI_NEW(
      ConfigurableProviderService.PROVIDER_ALIPAY,
      ConfigurableProviderService.PROVER_DEFAULT_V,
      OptProvider.FACE_ALIPAY_MINI,
      OptProductChild.FACE_FACE_ALIPAY_MINI_FACE,
      "支付宝小程序刷脸"),

  FACE_ALIPAY_MINI_NEW_V2(
          ConfigurableProviderService.PROVIDER_ALIPAY,
          AntFaceV2InvocationHandler.API_VERSION,
          OptProvider.FACE_ANT,
          OptProductChild.FACE_AUTH_ANT_FACE,
          "支付宝小程序刷脸"),

  FACE_ALIPAY_MINI_NEW_V3(
          ConfigurableProviderService.PROVIDER_ALIPAY,
          AntFaceV3InvocationHandler.API_VERSION,
          OptProvider.FACE_ALIPAY_MINI,
          OptProductChild.FACE_FACE_ALIPAY_MINI_FACE,
          "支付宝小程序刷脸"),

  FACE_BYTEDANCE(
      ConfigurableProviderService.PROVIDER_BYTEDANCE,
      ConfigurableProviderService.PROVER_DEFAULT_V,
      OptProvider.FACE_BYTEDANCE,
      OptProductChild.FACE_BYTEDANCE_FACE,
      "字节火山刷脸"),

  FACE_TIKTOK_MINI_FACE(
          ConfigurableProviderService.PROVIDER_FACE_TIKTOK_MINI,
          ConfigurableProviderService.PROVER_DEFAULT_V,
          OptProvider.FACE_TIKTOK_MINI,
          OptProductChild.FACE_TIKTOK_MINI_FACE,
          "抖音刷脸"),

  FACE_TECENT_SDK_BASIC(
          ConfigurableProviderService.PROVIDER_FACE_TENCENT_SDK_BASIC,
          ConfigurableProviderService.PROVER_DEFAULT_V,
          OptProvider.FACE_TECENT_SDK_BASIC,
          OptProductChild.FACE_TECENT_SDK_BASIC,
          "腾讯云sdk刷脸基础版"),

  FACE_TECENT_SDK_PLUS(
          ConfigurableProviderService.PROVIDER_FACE_TENCENT_SDK_PLUS,
          ConfigurableProviderService.PROVER_DEFAULT_V,
          OptProvider.FACE_TECENT_SDK_PLUS,
          OptProductChild.FACE_TECENT_SDK_PLUS,
          "腾讯云sdk刷脸增强版"),
  ;

  private String provider;
  private String version;

  private OptProvider optProvider;

  private OptProductChild optProductChild;

  private String title;

  FaceOptLogProviderEnum(
      String provider,
      String version,
      OptProvider optProvider,
      OptProductChild optProductChild,
      String title) {
    this.provider = provider;
    this.version = version;
    this.optProvider = optProvider;
    this.optProductChild = optProductChild;
    this.title = title;
  }

  public String getProvider() {
    return provider;
  }

  public OptProvider getOptProvider() {
    return optProvider;
  }

  public String getTitle() {
    return title;
  }

  public OptProductChild getOptProductChild() {
    return optProductChild;
  }

  public static FaceOptLogProviderEnum getOptProvider(String provider, String version) {
    String v = version;
    if(StringUtils.isBlank(v)){
      v =  ConfigurableProviderService.PROVER_DEFAULT_V;
    }

    //匹配供应商名称和版本号一样的
    for (FaceOptLogProviderEnum optLogProviderEnum : FaceOptLogProviderEnum.values()) {
      if (optLogProviderEnum.provider.equals(provider) && optLogProviderEnum.version.equals(v)) {
        return optLogProviderEnum;
      }
    }


    //随机一个匹配供应商名称的
    for (FaceOptLogProviderEnum optLogProviderEnum : FaceOptLogProviderEnum.values()) {
      if (optLogProviderEnum.provider.equals(provider)) {
        return optLogProviderEnum;
      }
    }
    return DEFAULT;
  }
}
