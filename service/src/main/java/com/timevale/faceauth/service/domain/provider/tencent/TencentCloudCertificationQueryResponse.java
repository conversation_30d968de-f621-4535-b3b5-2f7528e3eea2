package com.timevale.faceauth.service.domain.provider.tencent;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/21 11
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TencentCloudCertificationQueryResponse
    extends TencentCloudCertificationResponse<TencentCloudCertificationQueryResult> {

  private String bizSeqNo;
  private String app_id;
  private String order_no;
  private long transactionTime;
}
