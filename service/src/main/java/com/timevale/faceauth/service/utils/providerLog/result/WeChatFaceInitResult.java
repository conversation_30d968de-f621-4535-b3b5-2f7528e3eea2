package com.timevale.faceauth.service.utils.providerLog.result;

import com.alibaba.fastjson.JSONObject;
import com.timevale.faceauth.service.utils.providerLog.AbstractProviderLogResultResolver;
import com.timevale.faceauth.service.utils.providerLog.ProviderActionEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderKeyIdEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogConfiguration;
import java.util.List;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
public class WeChatFaceInitResult extends AbstractProviderLogResultResolver<JSONObject> {

  public WeChatFaceInitResult(String bizId, long responseTime) {
    super(bizId, responseTime);
  }

  @Override
  public ProviderKeyIdEnum getProviderKeyId() {
    return ProviderKeyIdEnum.WeChatFace;
  }

  @Override
  public ProviderActionEnum getAction() {
    return ProviderActionEnum.INITIALIZATION;
  }

  @Override
  public String getCode(JSONObject response) {
    return null;
  }

  @Override
  public String getMsg(JSONObject response) {
    return null;
  }

  @Override
  public String getResult(JSONObject response) {
    return null;
  }

  @Override
  public List<String> getFieldEmptyMappingStautsUnknown() {
    return ProviderLogConfiguration.weChatFaceActionInitializationFieldEmptyMappingStautsUnknown;
  }

  @Override
  public List<String> getCodeMsgMappingStautsFailure() {
    return null;
  }

  @Override
  public List<String> getCodeMsgMappingStautsSuccess() {
    return null;
  }
}
