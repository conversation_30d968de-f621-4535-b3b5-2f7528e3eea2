package com.timevale.faceauth.service.domain.provider.tencent;

import com.timevale.faceauth.service.core.support.ArgumentUtil;

import java.util.UUID;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/21 11
 */
public class TencentCloudCertificationQueryRequest extends TencentCloudCertificationSignableRequest {

  //是否需要获取人脸识别的视频和文件，
  // 值为1则返回视频和照片、值为2则返回照片、值为3则返回视频；其他则不返回
  private String getFile;
  private final String appId;
  private final String nonce;
  private final String orderNo;
  private final String queryVersion;
  private final String ticket;

  private TencentCloudCertificationQueryRequest(
      TencentCloudCertificationQueryRequestBuilder builder) {
    super(builder);
    this.appId = builder.appId;
    this.nonce = builder.nonce;
    this.orderNo = builder.orderNo;
    this.getFile = builder.getFile;
    this.queryVersion = builder.queryVersion;
    this.ticket = builder.ticket;
  }

  public String getAppId() {
    return appId;
  }

  public String getNonce() {
    return nonce;
  }

  public String getOrderNo() {
    return orderNo;
  }

  public String getGetFile() {
    return getFile;
  }

  public String getQueryVersion() {
    return queryVersion;
  }

  String obtainTicket() {
    return ticket;
  }

  public void setGetFile(String getFile) {
    this.getFile = getFile;
  }

  static TencentCloudCertificationQueryRequestBuilder createBuilder() {
    return (new TencentCloudCertificationQueryRequestBuilder());
  }

  static class TencentCloudCertificationQueryRequestBuilder
      extends TencentCloudCertificationSignableRequestBuilder<
          TencentCloudCertificationQueryRequest, TencentCloudCertificationQueryRequestBuilder> {

    private String appId;
    private String nonce = UUID.randomUUID().toString().replace("-", "");
    private String orderNo;
    private String getFile =
        String.valueOf(TencentCloudCertificationQueryFileResultType.GET_VIDEO_AND_PHOTO.getValue());
    //查询接口版本号(传1.0则返回 sdk 版本号和 trtc 标识)
    private String queryVersion = "1.0";
    private String ticket;

    @Override
    TencentCloudCertificationQueryRequest build() {
      return (new TencentCloudCertificationQueryRequest(this));
    }

    @Override
    protected TencentCloudCertificationQueryRequestBuilder self() {
      return this;
    }

    TencentCloudCertificationQueryRequestBuilder setAppId(String appId) {
      ArgumentUtil.throwIfEmptyArgument(appId, "appId");
      this.appId = appId;
      return this;
    }

    TencentCloudCertificationQueryRequestBuilder setNonce(String nonce) {
      ArgumentUtil.throwIfEmptyArgument(nonce, "nonce");
      this.nonce = nonce;
      return this;
    }

    TencentCloudCertificationQueryRequestBuilder setOrderNo(String orderNo) {
      ArgumentUtil.throwIfEmptyArgument(orderNo, "orderNo");
      this.orderNo = orderNo;
      return this;
    }

    TencentCloudCertificationQueryRequestBuilder setFileResultType(
        TencentCloudCertificationQueryFileResultType queryFileResultType) {
      ArgumentUtil.throwIfNull(queryFileResultType, "getFile");
      this.getFile = String.valueOf(queryFileResultType.getValue());
      return this;
    }
    TencentCloudCertificationQueryRequestBuilder setQueryVersion(
            String queryVersion) {
      ArgumentUtil.throwIfNull(queryVersion, "queryVersion");
      this.queryVersion = queryVersion;
      return this;
    }

    TencentCloudCertificationQueryRequestBuilder setTicket(String ticket) {
      ArgumentUtil.throwIfEmptyArgument(ticket, "ticket");
      this.ticket = ticket;
      return this;
    }
  }
}
