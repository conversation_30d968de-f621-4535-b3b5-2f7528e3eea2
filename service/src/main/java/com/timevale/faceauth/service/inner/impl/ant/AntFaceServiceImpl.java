package com.timevale.faceauth.service.inner.impl.ant;

import com.timevale.faceauth.dal.pfs.dao.FaceauthAntDAO;
import com.timevale.faceauth.dal.pfs.dataobject.FaceauthAntDO;
import com.timevale.faceauth.service.bean.FaceAuthBaseInfo;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.core.support.FaceAuthorizationIdNoType;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.processor.ClientApplicationType;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import com.timevale.faceauth.service.domain.provider.ant.call.*;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.enums.FaceAuthBootFromEnum;
import com.timevale.faceauth.service.enums.FaceAuthModeEnum;
import com.timevale.faceauth.service.enums.FaceAuthPassedEnum;
import com.timevale.faceauth.service.enums.FaceAuthStatusEnum;
import com.timevale.faceauth.service.exception.NoCompleteFaceAuthException;
import com.timevale.faceauth.service.exception.ProviderException;
import com.timevale.faceauth.service.inner.FaceAuthExtService;
import com.timevale.faceauth.service.inner.FaceService;
import com.timevale.faceauth.service.inner.impl.BaseFaceServiceImpl;
import com.timevale.faceauth.service.inner.impl.zhima.ZhiMaFaceAuthorizationExternalService;
import com.timevale.faceauth.service.inner.support.BizCodeMapper;
import com.timevale.faceauth.service.input.FaceAuthInput;
import com.timevale.faceauth.service.input.FaceAuthorizationDetailQueryInput;
import com.timevale.faceauth.service.result.FaceAuthResult;
import com.timevale.faceauth.service.result.FaceAuthorizationDetailResult;
import com.timevale.faceauth.service.result.FaceAuthorizationProviderOrder;
import com.timevale.faceauth.service.result.QueryFaceAuthResResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/** @Author: Yukai Description: 蚂蚁区块链人脸认证与获取信息服务 create time: 2019/5/23 17:22 */
@Service(FaceService.AntFaceService)
@Slf4j
public class AntFaceServiceImpl extends BaseFaceServiceImpl
    implements ZhiMaFaceAuthorizationExternalService {

  // 供应商认证通过
  public static final Integer STATUS_PROVIDER_PASSED = 0x03;

  // 认证完成状态
  private static final int ANT_STATUS_PASS = STATUS_PROVIDER_PASSED;

  @Autowired private AntFaceInvocationHandlerCoordinator handlerCoordinator;
  @Autowired private FaceauthAntDAO faceauthAntDAO;
  @Autowired private BizCodeMapper bizCodeMapper;
  @Autowired private FaceAuthExtService faceAuthExtService;
  @Autowired private ConfigurableProperties config;

  @Override
  public FaceAuthResult startFace(String faceAuthId, FaceAuthInput faceAuthInput) {

    AntFaceInvocationHandler invocationHandler =
        handlerCoordinator.coordinateByBizAppId(faceAuthInput.getAppId(), faceAuthInput.getCertType());
    try {

      AntFaceInitializationInput initializationInput =
          prepareCertificationInitializationInput(faceAuthId, faceAuthInput);
      AntFaceInitializationResponse antResp = invocationHandler.startFace(initializationInput);
      // 蚂蚁区块链返回responseData字段不是json,而是json字符串，所以需要再次转换!

      if (antResp != null && antResp.isSuccess()) {
        faceAuthExtService.saveAntFaceAuth(
            faceAuthId, invocationHandler.getApiVersion(), faceAuthInput, antResp);
        return new FaceAuthResult(faceAuthId, antResp.getResponseData().getCertifyUrl());
      } else {
        String msg = antResp.getErrMessage();
        log.error("Ant chain block face auth failed : {}", msg);
        throw new FaceException(FaceStatusCode.FACE_FAILED_AUTHORIZATION_INITIALIZE);
      }
    } catch (Exception e) {
      // 请求异常
      log.error("Ant block chain got certify id failed: {}", e);
      throw new FaceException(FaceStatusCode.ERR_INNER);
    }
  }

  private AntFaceInitializationInput prepareCertificationInitializationInput(
      String faceAuthId, FaceAuthInput faceAuthInput) throws FaceException {
    AntFaceInitializationIdentity identity = prepareCertificationIdentity(faceAuthInput);
    return AntFaceInitializationInput.builder()
        .bizAppId(faceAuthInput.getAppId())
        .outerOrderNo(faceAuthId)
        .clientApplicationType(deduceAuthorizationType(faceAuthInput))
        .callbackUrl(faceAuthInput.getCallbackUrl())
        .returnUrl(faceAuthInput.getReturnUrl())
        .identity(identity)
        .build();
  }

  private AntFaceInitializationIdentity prepareCertificationIdentity(FaceAuthInput faceAuthInput)
      throws FaceException {
    return AntFaceInitializationIdentity.builder()
        .certName(faceAuthInput.getName())
        .certNumber(faceAuthInput.getIdNo())
        .certType(faceAuthInput.getCertType())
        .build();
  }

  private ClientApplicationType deduceAuthorizationType(FaceAuthInput faceAuthInput)
      throws FaceException {
    return FaceAuthBootFromEnum.BROWSER.equals(faceAuthInput.getFrom())
        ? ClientApplicationType.TYPE_BROWSER
        : ClientApplicationType.TYPE_APP;
  }

  @Override
  public QueryFaceAuthResResult queryFaceAuthResult(
      FaceAuthBaseInfo faceAuthBaseInfo, Boolean needFile) {

    String faceAuthId = faceAuthBaseInfo.getFaceAuthId();
    int faceAuthMode = faceAuthBaseInfo.getFaceAuthMode();

    QueryFaceAuthResResult resResult;

    // 人脸识别已经认证完成或认证任务过期，直接查询db
    if (config.isFaceTaskExpired(faceAuthBaseInfo.getCreateTime())
        || FaceAuthStatusEnum.COMPLETE.getStatus().equals(faceAuthBaseInfo.getAuthStatus())) {
      resResult = faceAuthExtService.getFaceAuthResResult(faceAuthId, faceAuthMode, needFile);
      resResult.setMsg(faceAuthBaseInfo.getMsg());

    } else {
      try {
        resResult = queryAuthResultGoAnt(faceAuthId);
      } catch (Exception e) {
        throw e;
      }
    }

    return resResult;
  }

  private QueryFaceAuthResResult queryAuthResultGoAnt(String faceAuthId) {
    QueryFaceAuthResResult resResult;
    FaceauthAntDO antDO = faceAuthExtService.getFaceAuthAntInfo(faceAuthId);
    String certifyId = antDO.getCertifyId();
    AntFaceInvocationHandler invocationHandler =
        handlerCoordinator.coordinateByApiVersion(antDO.getProviderApiVersion());
    try {

      boolean isOK = Integer.valueOf(3).equals(antDO.getStatus());
      FaceInfo faceInfo = FaceInfo
              .createBuilder()
              .setName(antDO.getName())
              .setIdNo(antDO.getIdNo())
              .setFaceId(faceAuthId)
              .setProvider(ConfigurableProviderService.PROVIDER_ANT_BLOCK_CHAIN)
              .setCreateTime(antDO.getCreateTime())
              .setOk(isOK)
              .setOid(antDO.getUserId())
              .setPhoto(null)
              .setBizId(antDO.getId())
              .setPhotoType(null)
              .build();
      AntFaceQueryResponse antResp =
          invocationHandler.queryResult(faceInfo,
              AntFaceQueryInput.builder().outerOrderNo(faceAuthId).certifyId(certifyId).build());
      if (antResp != null && antResp.isSuccess()) {
        resResult = new QueryFaceAuthResResult();
        resResult.setFaceAuthId(faceAuthId);

        int status = antResp.toRevokerData().getStatus();
        Boolean passed = (status == ANT_STATUS_PASS);

        String msg;
        if (passed) {
          msg = FaceAuthPassedEnum.PASSED_TRUE.getMsg();
        } else {
          msg = antResp.getErrMessage();
          log.warn("no completed face,{}", msg);
          // don't finish the process, return runtime exception
          NoCompleteFaceAuthException e =
              new NoCompleteFaceAuthException(FaceStatusCode.UNCOMPLETED_AUTHORIZATION);
          throw e;
        }

        resResult.setPassed(passed);
        resResult.setMsg(msg);
        resResult.setFaceAuthMode(FaceAuthModeEnum.ANT_BLOCK_CHAIN.getMode());

        faceAuthExtService.saveAntFaceAuthResult(faceAuthId, status, antResp.getErrMessage(), msg);

      } else {
        String msg = antResp.getErrMessage();
        log.error("Ant chain block face auth failed : {}", msg);

        throw new NoCompleteFaceAuthException(FaceStatusCode.UNCOMPLETED_AUTHORIZATION);
      }

    } catch (NoCompleteFaceAuthException e) {
      throw e;
    } catch (Exception e) {
      log.error("Ant block chain got status failed: {}", e);
      throw new ProviderException(FaceStatusCode.PROVIDER_FAILED_API);
    }

    return resResult;
  }

  @Override
  public FaceAuthorizationDetailResult getDetail(
      FaceAuthorizationDetailQueryInput input, FaceAuthBaseInfo faceInfo) {
    FaceauthAntDO entity;
    try {
      entity = faceauthAntDAO.getById(faceInfo.getFaceAuthId());
    } catch (Exception cause) {
      log.warn(
          "Fail read entity on ant block-chain face[" + faceInfo.getFaceAuthId() + "] .", cause);
      throw FaceException.valueOf(FaceStatusCode.RESOURCE_FAILED_DATASOURCE);
    }
    if (null == entity) {
      log.warn("Not found entity on ant block-chain face[" + faceInfo.getFaceAuthId() + "] .");
      throw FaceException.valueOf(FaceStatusCode.FACE_NOT_FOUND_AUTHORIZATION);
    }

    FaceAuthorizationDetailResult result = (new FaceAuthorizationDetailResult());
    configurationResult(result, entity, faceInfo);
    return result;
  }

  private void configurationResult(
      final FaceAuthorizationDetailResult result,
      final FaceauthAntDO entity,
      final FaceAuthBaseInfo faceInfo) {
    result.setFaceId(faceInfo.getFaceAuthId());
    result.setIdNo(entity.getIdNo());
    result.setIdNoType(FaceAuthorizationIdNoType.IDENTITY_CARD.name());
    result.setName(entity.getName());
    result.setRedirectURL(entity.getCallbackUrl());
    result.setOid(entity.getUserId());
    result.setAppId(faceInfo.getBizAppId());
    result.setBizCode(bizCodeMapper.getBizCode(faceInfo.getBizType()));
    result.setCreatedTimestamp(entity.getCreateTime().getTime());
    // 由于业务逻辑的特殊性，没有相关字段表述完成状态，只能根据 passed 字段来标识
    boolean passed = STATUS_PROVIDER_PASSED.equals(entity.getStatus());
    result.setCompleted(passed);
    result.setOk(passed);
    result.setFaceAuthMode(FaceAuthModeEnum.ANT_BLOCK_CHAIN.getMode());
    result.setProviderOrder(prepareOrder(passed, entity));
    // nothing for return , ignore .
    result.setProviderResponse(null);
  }

  private FaceAuthorizationProviderOrder prepareOrder(
      boolean isCompleted, final FaceauthAntDO entity) {
    FaceAuthorizationProviderOrder order = (new FaceAuthorizationProviderOrder());
    order.setCode(ConfigurableProviderService.PROVIDER_ANT_BLOCK_CHAIN);
    order.setName(ConfigurableProviderService.FULL_NAME_PROVIDER_ANT_BLOCK_CHAIN);
    order.setOrderNo(entity.getCertifyId());
    order.setCompleted(isCompleted);
    order.setCompletedTimestamp(isCompleted ? entity.getUpdateTime().getTime() : 0L);
    order.setPhoto(entity.getPhotoInput());
    return order;
  }
}
