package com.timevale.faceauth.service.domain.provider.support;

import com.timevale.faceauth.service.core.support.ArgumentUtil;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.exception.ProviderException;
import com.timevale.faceauth.service.utils.SpringUtil;
import com.timevale.mandarin.base.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/12/23 14
 */
public abstract class AbstractProviderFaceAuthorizationResult
    implements ProviderFaceAuthorizationResult {

  private final String provider;
  private final String faceId;
  private final String content;
  private final boolean completed;
  private final Boolean fee;
  private final boolean success;
  private final long timestamp;
  private final float liveRate;
  private final float similarity;
  private final String photo;
  private final List<String> photoAll;
  private final String photoType;
  private final String video;

  private final String trtcFlag;
  private final String idCardFront;

  private final String idCardBack;

  private ProviderException error;

  public void setError(ProviderException error) {
    this.error = error;
  }

  protected AbstractProviderFaceAuthorizationResult(
      @SuppressWarnings("rawtypes") ProviderFaceAuthorizationResultBuilder builder) {
    this.provider = builder.getProvider();
    this.faceId = builder.getFaceId();
    this.content = builder.getContent();
    this.completed = builder.isCompleted();
    this.success = builder.isSuccess();
    this.error = builder.getError();
    this.timestamp = builder.getTimestamp();
    this.liveRate = builder.getLiveRate();
    this.similarity = builder.getSimilarity();
    this.photo = builder.getPhoto();
    this.photoAll = builder.getPhotoAll();
    this.photoType = builder.getPhotoType();
    this.video = builder.getVideo();
    this.fee = builder.isFee();
    this.idCardFront = builder.getIdCardFront();
    this.idCardBack = builder.getIdCardBack();
    this.trtcFlag = builder.getTrtcFlag();
  }

  @Override
  public String getProvider() {
    return provider;
  }

  @Override
  public String getFaceId() {
    return faceId;
  }

  @Override
  public String getResultContent() {
    return content;
  }

  @Override
  public boolean isCompleted() {
    return completed;
  }

  @Override
  public boolean isSuccess() {
    return success;
  }

  @Override
  public ProviderException getError() {
    return error;
  }

  @Override
  public long getTimestamp() {
    return timestamp;
  }

  @Override
  public boolean isFee() {
    if(fee == null){
       return isCompleted();
    }
    return fee;
  }

  @Override
  public float getLiveRate() {
    return liveRate;
  }

  @Override
  public float getSimilarity() {
    return similarity;
  }

  @Override
  public String getPhoto() {
    return photo;
  }

  @Override
  public List<String> getPhotoAll() {
    if(CollectionUtils.isNotEmpty(photoAll)){
       return photoAll;
    }
    String photo = getPhoto();
    if(com.timevale.mandarin.base.util.StringUtils.isNotBlank(photo)){
      return Arrays.asList(photo);
    }

    return photoAll;
  }

  @Override
  public String getVideo() {
    return video;
  }

  @Override
  public String getIdCardFront() {
    return idCardFront;
  }

  @Override
  public String getIdCardBack() {
    return idCardBack;
  }

  @Override
  public String getTrtcFlag() {
    return trtcFlag;
  }

  @Override
  public String getPhotoType() {
    return photoType;
  }

  public void fillFaceError(ProviderException error){
      this.error = error;
  }

  @SuppressWarnings("rawtypes")
  public abstract static class ProviderFaceAuthorizationResultBuilder<
      T extends ProviderFaceAuthorizationResult, B extends ProviderFaceAuthorizationResultBuilder> {

    private String faceId;
    private String content = "";
    private boolean completed = false;
    private boolean success = false;
    private Boolean fee ;
    private ProviderException error;
    private long timestamp = System.currentTimeMillis();
    private float liveRate = 0;
    private float similarity = 0;
    private String photo;
    private List<String> photoAll;
    private String photoType;
    private String video;

    //Trtc 渠道刷脸则标识,是否实时检测模式.Y=是 。
    private String trtcFlag;
    private String idCardFront;

    private String idCardBack;

    private final String provider;

    protected ProviderFaceAuthorizationResultBuilder(String provider) {
      this.provider = provider;
    }

    protected void refreshContext() {
      //设置默认分值
      if(this.similarity < 1 && this.liveRate < 1){
        ConfigurableProperties properties = SpringUtil.getBean(ConfigurableProperties.class);
        if (null == properties) {
          this.liveRate = 0.00F;
          this.similarity = 0.00F;
        } else {
          this.liveRate = properties.getFaceRateDefault();
          this.similarity = properties.getFaceSimilarityDefault();
        }
      }
    }

    protected void refreshLiveRate() {
      // 设置默认分值
      if (this.liveRate < 1) {
        ConfigurableProperties properties = SpringUtil.getBean(ConfigurableProperties.class);
        if (null == properties) {
          this.liveRate = 0.00F;
        } else {
          this.liveRate = properties.getFaceRateDefault();
        }
      }
    }

    public abstract T build();

    protected B this0() {
      @SuppressWarnings("unchecked")
      B builder = (B) this;
      return builder;
    }

    public B setFaceId(String faceId) {
      ArgumentUtil.throwIfEmptyArgument(faceId, "faceId");
      this.faceId = faceId;
      return this0();
    }

    public B setContent(String content) {
      this.content = content;
      return this0();
    }

    public B setTrtcFlag(String trtcFlag) {
      this.trtcFlag = trtcFlag;
      return this0();
    }

    public B setCompleted(boolean completed) {
      this.completed = completed;
      return this0();
    }

    public B setSuccess(boolean success) {
      this.success = success;
      return this0();
    }

    public B setFee(boolean fee) {
      this.fee = fee;
      return this0();
    }

    public B setError(ProviderException error) {
      this.error = error;
      return this0();
    }

    public B setTimestamp(long timestamp) {
      this.timestamp = timestamp;
      return this0();
    }

    public B setLiveRate(float liveRate) {
      this.liveRate = liveRate;
      return this0();
    }

    public B setSimilarity(float similarity) {
      this.similarity = similarity;
      return this0();
    }

    public B setPhoto(String photo) {
      this.photo = photo;
      return this0();
    }

    public B setPhotoAll(List<String> photoList) {
      this.photoAll = photoList;
      return this0();
    }

    public B setPhotoType(String photoType) {
      this.photoType = photoType;
      return this0();
    }

    public B setVideo(String video) {
      this.video = video;
      return this0();
    }

    public B setIdCardFront(String idCardFront) {
      this.idCardFront = idCardFront;
      return this0();
    }

    public B setIdCardBack(String idCardBack) {
      this.idCardBack = idCardBack;
      return this0();
    }

    public String getFaceId() {
      return faceId;
    }

    public String getContent() {
      return content;
    }

    public boolean isCompleted() {
      return completed;
    }

    public boolean isSuccess() {
      return success;
    }

    public ProviderException getError() {
      return error;
    }

    public long getTimestamp() {
      return timestamp;
    }

    public float getLiveRate() {
      return liveRate;
    }

    public float getSimilarity() {
      return similarity;
    }

    public String getTrtcFlag() {
      return trtcFlag;
    }

    public String getPhoto() {
      return photo;
    }

    public List<String> getPhotoAll() {
      return photoAll;
    }

    public String getPhotoType() {
      return photoType;
    }

    public String getProvider() {
      return provider;
    }

    public String getVideo() {
      return video;
    }

    public Boolean isFee() {
      return fee;
    }

    public String getIdCardFront() {
      return idCardFront;
    }

    public String getIdCardBack() {
      return idCardBack;
    }
  }
}
