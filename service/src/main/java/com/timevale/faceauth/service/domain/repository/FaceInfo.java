package com.timevale.faceauth.service.domain.repository;

import com.timevale.faceauth.dal.pfs.face.support.FaceDO;
import com.timevale.mandarin.weaver.utils.RequestContext;

import java.util.Date;
import java.util.UUID;

/**
 * 刷脸上下文
 *
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/9/27 16
 */
public class FaceInfo {

  private long id;
  private final String tid;
  private final String faceId;
  private final String appId;
  private final String oid;
  private final String bizId;
  private final String bizCode;
  private final String bizScene;
  private final String clientType;
  private final String name;
  private final String idNo;
  private final String idType;
  private final String photo;
  private final String photoType;
  private final String provider;
  private final String providerApiVersion;
  private final String input;
  private final boolean ok;
  private final String resultCode;
  private final String resultMsg;
  private final String returnUrl;
  private final long returnTime;
  private final String returnErrMsg;
  private final String callbackUrl;
  private final long callbackTime;
  private final boolean doneCallback;
  private final long callbackDoneTime;
  private final boolean okCallback;
  private final String callbackErrMsg;
  private final Date createTime;

  private FaceInfo(FaceInfoBuilder builder) {
    this.id = builder.id;
    this.tid = builder.tid;
    this.faceId = builder.faceId;
    this.appId = builder.appId;
    this.oid = builder.oid;
    this.bizId = builder.bizId;
    this.bizCode = builder.bizCode;
    this.bizScene = builder.bizScene;
    this.clientType = builder.clientType;
    this.name = builder.name;
    this.idNo = builder.idNo;
    this.idType = builder.idType;
    this.photo = builder.photo;
    this.photoType = builder.photoType;
    this.provider = builder.provider;
    this.providerApiVersion = builder.providerApiVersion;
    this.input = builder.input;
    this.ok = builder.ok;
    this.resultCode = builder.resultCode;
    this.resultMsg = builder.resultMsg;
    this.returnUrl = builder.returnUrl;
    this.returnTime = builder.returnTime;
    this.returnErrMsg = builder.returnErrMsg;
    this.callbackUrl = builder.callbackUrl;
    this.callbackTime = builder.callbackTime;
    this.doneCallback = builder.doneCallback;
    this.callbackDoneTime = builder.callbackDoneTime;
    this.okCallback = builder.okCallback;
    this.callbackErrMsg = builder.callbackErrMsg;
    this.createTime = builder.createTime;
  }

  void setId(long id) {
    this.id = id;
  }

  public long getId() {
    return id;
  }

  public String getTid() {
    return tid;
  }

  public String getFaceId() {
    return faceId;
  }

  public String getAppId() {
    return appId;
  }

  public String getOid() {
    return oid;
  }

  public String getBizId() {
    return bizId;
  }

  public String getBizCode() {
    return bizCode;
  }

  public String getBizScene() {
    return bizScene;
  }

  public String getClientType() {
    return clientType;
  }

  public String getName() {
    return name;
  }

  public String getIdNo() {
    return idNo;
  }

  public String getIdType() {
    return idType;
  }

  public String getPhoto() {
    return photo;
  }

  public String getPhotoType() {
    return photoType;
  }

  public String getProvider() {
    return provider;
  }

  public String getProviderApiVersion() {
    return providerApiVersion;
  }

  public String getReturnUrl() {
    return returnUrl;
  }

  public String getCallbackUrl() {
    return callbackUrl;
  }

  public String getInput() {
    return input;
  }

  public boolean isOk() {
    return ok;
  }

  public String getResultCode() {
    return resultCode;
  }

  public String getResultMsg() {
    return resultMsg;
  }

  public long getReturnTime() {
    return returnTime;
  }

  public String getReturnErrMsg() {
    return returnErrMsg;
  }

  public long getCallbackTime() {
    return callbackTime;
  }

  public boolean isDoneCallback() {
    return doneCallback;
  }

  public long getCallbackDoneTime() {
    return callbackDoneTime;
  }

  public boolean isOkCallback() {
    return okCallback;
  }

  public String getCallbackErrMsg() {
    return callbackErrMsg;
  }

  public Date getCreateTime() {
    return createTime;
  }

  public static FaceInfoBuilder createBuilder() {
    return (new FaceInfoBuilder());
  }

 public static FaceInfo valueOf(FaceDO entity) {
    return FaceInfo.createBuilder()
        .setId(entity.id)
        .setTid(entity.tid)
        .setFaceId(entity.faceId)
        .setAppId(entity.appId)
        .setOid(entity.oid)
        .setBizId(entity.bizId)
        .setBizCode(entity.bizCode)
        .setBizScene(entity.bizScene)
        .setClientType(entity.clientType)
        .setName(entity.name)
        .setIdNo(entity.idNo)
        .setIdType(entity.idType)
        .setPhoto(entity.photo)
        .setPhotoType(entity.photoType)
        .setProvider(entity.provider)
        .setProviderApiVersion(entity.providerApiVersion)
        .setInput(entity.input)
        .setOk(1 == entity.isOk)
        .setResultCode(entity.resultCode)
        .setResultMsg(entity.resultMsg)
        .setReturnUrl(entity.returnUrl)
        .setReturnErrMsg(entity.returnErrMsg)
        .setReturnTime(entity.returnTime)
        .setCallbackUrl(entity.callbackUrl)
        .setCallbackTime(entity.callbackTime)
        .setDoneCallback(1 == entity.callbackIsDone)
        .setCallbackDoneTime(entity.callbackDoneTime)
        .setOkCallback(1 == entity.callbackIsOk)
        .setCallbackErrMsg(entity.callbackErrMsg)
        .setCreateTime(entity.createTime)
        .build();
  }

  public static final class FaceInfoBuilder {

    private long id;
    private String tid;
    private String faceId;
    private String appId;
    private String oid;
    private String bizId;
    private String bizCode;
    private String bizScene;
    private String clientType;
    private String name;
    private String idNo;
    private String idType;
    private String photo;
    private String photoType;
    private String provider;
    private String providerApiVersion;
    private String input;
    private boolean ok;
    private String resultCode;
    private String resultMsg;
    private String returnUrl;
    private long returnTime;
    private String returnErrMsg;
    private String callbackUrl;
    private long callbackTime;
    private boolean doneCallback;
    private long callbackDoneTime;
    private boolean okCallback;
    private String callbackErrMsg;
    private Date createTime;

    private FaceInfoBuilder() {
      this.id = 0L;
      this.faceId = UUID.randomUUID().toString();
      this.photo = "";
      this.photoType = "";
      this.returnTime = 0L;
      this.ok = false;
      this.callbackTime = 0L;
      this.doneCallback = false;
      this.callbackDoneTime = 0L;
      this.okCallback = false;
      this.createTime = (new Date());
      this.tid = RequestContext.getTransactionId();
    }

    public FaceInfo build() {
      return (new FaceInfo(this));
    }

    public FaceInfoBuilder setId(long id) {
      this.id = id;
      return this;
    }

    public FaceInfoBuilder setTid(String tid) {
      this.tid = tid;
      return this;
    }

    public FaceInfoBuilder setFaceId(String faceId) {
      this.faceId = faceId;
      return this;
    }

    public FaceInfoBuilder setAppId(String appId) {
      this.appId = appId;
      return this;
    }

    public FaceInfoBuilder setOid(String oid) {
      this.oid = oid;
      return this;
    }

    public FaceInfoBuilder setBizId(String bizId) {
      this.bizId = bizId;
      return this;
    }

    public FaceInfoBuilder setBizCode(String bizCode) {
      this.bizCode = bizCode;
      return this;
    }

    public FaceInfoBuilder setBizScene(String bizScene) {
      this.bizScene = bizScene;
      return this;
    }

    public FaceInfoBuilder setClientType(String clientType) {
      this.clientType = clientType;
      return this;
    }

    public FaceInfoBuilder setName(String name) {
      this.name = name;
      return this;
    }

    public FaceInfoBuilder setIdNo(String idNo) {
      this.idNo = idNo;
      return this;
    }

    public FaceInfoBuilder setIdType(String idType) {
      this.idType = idType;
      return this;
    }

    public FaceInfoBuilder setPhoto(String photo) {
      this.photo = photo;
      return this;
    }

    public FaceInfoBuilder setPhotoType(String photoType) {
      this.photoType = photoType;
      return this;
    }

    public FaceInfoBuilder setProvider(String provider) {
      this.provider = provider;
      return this;
    }

    public FaceInfoBuilder setProviderApiVersion(String providerApiVersion) {
      this.providerApiVersion = providerApiVersion;
      return this;
    }

    public FaceInfoBuilder setReturnUrl(String returnUrl) {
      this.returnUrl = returnUrl;
      return this;
    }

    public FaceInfoBuilder setCallbackUrl(String callbackUrl) {
      this.callbackUrl = callbackUrl;
      return this;
    }

    public FaceInfoBuilder setInput(String input) {
      this.input = input;
      return this;
    }

    public FaceInfoBuilder setOk(boolean ok) {
      this.ok = ok;
      return this;
    }

    public FaceInfoBuilder setResultCode(String resultCode) {
      this.resultCode = resultCode;
      return this;
    }

    public FaceInfoBuilder setResultMsg(String resultMsg) {
      this.resultMsg = resultMsg;
      return this;
    }

    public FaceInfoBuilder setReturnTime(long returnTime) {
      this.returnTime = returnTime;
      return this;
    }

    public FaceInfoBuilder setReturnErrMsg(String returnErrMsg) {
      this.returnErrMsg = returnErrMsg;
      return this;
    }

    public FaceInfoBuilder setCallbackTime(long callbackTime) {
      this.callbackTime = callbackTime;
      return this;
    }

    public FaceInfoBuilder setDoneCallback(boolean doneCallback) {
      this.doneCallback = doneCallback;
      return this;
    }

    public FaceInfoBuilder setCallbackDoneTime(long callbackDoneTime) {
      this.callbackDoneTime = callbackDoneTime;
      return this;
    }

    public FaceInfoBuilder setOkCallback(boolean okCallback) {
      this.okCallback = okCallback;
      return this;
    }

    public FaceInfoBuilder setCallbackErrMsg(String callbackErrMsg) {
      this.callbackErrMsg = callbackErrMsg;
      return this;
    }

    public FaceInfoBuilder setCreateTime(Date createTime) {
      this.createTime = createTime;
      return this;
    }
  }
}
