package com.timevale.faceauth.service.domain.support;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.*;
import com.timevale.faceauth.service.domain.processor.FaceRequestContext;
import com.timevale.faceauth.service.domain.repository.*;
import com.timevale.faceauth.service.enums.FaceAuthPhotoTypeEnum;
import com.timevale.faceauth.service.enums.FaceSwitchStatusEnum;
import com.timevale.faceauth.service.localphoto.LocalLibraryPhotoConstant;
import com.timevale.faceauth.service.tencentface.cache.FaceSwitchCache;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/28 14
 */
@Slf4j
@Component
public class SupportFaceAuthorizationResourceResolver implements FaceAuthorizationResourceResolver {

  private static final byte TYPE_WATER_WAVE = (byte) 0x01;
  private static final byte TYPE_HIGH_DEFINITION = (byte) 0x02;
  private static final Map<String, Byte> PHOTO_TYPE_MAPPER;

  static {
    PHOTO_TYPE_MAPPER = (new HashMap<>(4));
    PHOTO_TYPE_MAPPER.put(FaceAuthPhotoTypeEnum.WATER_WAVE.name(), TYPE_WATER_WAVE);
    PHOTO_TYPE_MAPPER.put(FaceAuthPhotoTypeEnum.HIGH_DEFINITION.name(), TYPE_HIGH_DEFINITION);
  }


  @Autowired
  private FaceSwitchRepository faceSwitchRepository;

  private final PhotoResourceStorageService photoResourceStorageService;
  private final VideoResourceStorageService videoResourceStorageService;
  private final FaceLocalLibraryPhotoRepository userPhotoRepository;
  private final FaceResourceRepository faceResourceRepository;

  @Autowired
  public SupportFaceAuthorizationResourceResolver(
      PhotoResourceStorageService photoResourceStorageService,
      VideoResourceStorageService videoResourceStorageService,
      FaceLocalLibraryPhotoRepository userPhotoRepository,
      FaceResourceRepository faceResourceRepository) {
    this.photoResourceStorageService = photoResourceStorageService;
    this.videoResourceStorageService = videoResourceStorageService;
    this.userPhotoRepository = userPhotoRepository;
    this.faceResourceRepository = faceResourceRepository;
  }

  /**
   * 尝试获取指定姓名和证件号码的用户照片。 若请求上下文中指定了照片数据，则返回的当前照片内容； 若请求上下文未指定照片数据，则从数据库中查询指定照片类型的照片；
   * 若请求上下文未指定照片类型，则从数据库中查询最后一次认证的认证照片； 否则，返回照片不存在的对象
   */
  @Override
  public UserPhoto resolvePhoto(FaceRequestContext context) throws FaceException {
    // 先从缓存中获取是否有失败的（某些特殊错误码）记录
    String faceSwitchModelStr =
            FaceSwitchCache.getFromRedis(
                    FaceSwitchCache.generateKey(context.getName(), context.getIdNo(), context.getProvider()));
    if(StringUtils.isNotBlank(faceSwitchModelStr)){
       return defaultUserPhoto(context);
    }
    if (StringUtils.isBlank(faceSwitchModelStr)) {
      // 缓存为空从数据库中获取
      FaceSwitchModel queryModel =
              FaceSwitchModel.builder()
                      .idNo(context.getIdNo())
                      .name(context.getName())
                      .faceMode(context.getProvider())
                      .build();
      if (!StringUtils.isEmpty(queryLatestUnSwitchedRecord(queryModel).getFaceId())) {
        return defaultUserPhoto(context);
      }
    }

    UserPhoto userPhoto = obtainSpecify(context);
    return (null == userPhoto ? obtainFromRepository(context) : userPhoto);
  }
  protected FaceSwitchModel queryLatestUnSwitchedRecord(FaceSwitchModel queryModel) {
    queryModel.setIsSwitched(FaceSwitchStatusEnum.UN_SWITCHED.getCode());
    return faceSwitchRepository.queryLatestRecord(queryModel);
  }

  /** 返回请求上下文中指定照片的对象。 若未指定，则返回 {@code null}。 */
  private UserPhoto obtainSpecify(FaceRequestContext context) throws FaceException {
    String photo = context.getPhoto();
    if (StringUtils.isEmpty(photo)) {
      return null;
    }

    return UserPhoto.createBuilder()
        .setPhotoType(context.getPhotoType())
        .setPhoto(photo)
        .setIdNo(context.getIdNo())
        .setName(context.getName())
        .build();
  }

  /** 尝试从数据仓库中获取请求上下文中指定的用户的照片内容 */
  private UserPhoto obtainFromRepository(FaceRequestContext context) throws FaceException {
    UserPhoto userPhoto = null;
    try {
      LocalLibraryUser localUser = LocalLibraryUser
              .builder()
              .bizAppId(context.getAppId())
              .provider(context.getProvider())
              .idNo(context.getIdNo())
              .name(context.getName())
              .build();
      userPhoto =
          userPhotoRepository.getPhotoByFactor2(localUser);
    } catch (Exception cause) {
      log.error("Not found user photo on user[" + context.getIdNo() + "] .", cause);
    }

    return (null == userPhoto ? defaultUserPhoto(context) : userPhoto);
  }

  private UserPhoto defaultUserPhoto(FaceRequestContext context) throws FaceException {
    return UserPhoto.createBuilder()
        .setPhotoType(context.getPhotoType())
        .setIdNo(context.getIdNo())
        .setName(context.getName())
        .build();
  }



  @Override
  public byte resolvePhotoType(String photoType) throws FaceException {
    if (StringUtils.isEmpty(photoType)) {
      return TYPE_WATER_WAVE;
    }

    Byte type = PHOTO_TYPE_MAPPER.get(photoType);
    return (null == type ? TYPE_WATER_WAVE : (byte) type);
  }

  @Override
  public String resolvePhotoData(String photoKey) throws FaceException {
    if (null == photoKey) {
      log.warn("Not found user photo key .");
      return null;
    }

    log.info("Success found user photo[" + photoKey + ", and try resolve photo content .");
    try {
      return photoResourceStorageService.get(photoKey);
    } catch (Exception cause) {
      log.warn(
          "Fail download photo on '"
              + photoKey
              + "' from '"
              + photoResourceStorageService.getName()
              + "' .",
          cause);
      return null;
    }
  }

  @Override
  public String savePhoto(String faceId, String photoBase64) throws FaceException {
    try {
      return photoResourceStorageService.save(faceId, photoBase64);
    } catch (Exception cause) {
      log.warn(
          "Fail storage photo on type[" + photoResourceStorageService.getName() + "] .", cause);
      return null;
    }
  }

  @Override
  public String saveVideo(String faceId, String videoBase64) throws FaceException {
    try {
      return videoResourceStorageService.save(faceId, videoBase64);
    } catch (Exception cause) {
      log.warn("Fail store video on type[" + videoResourceStorageService.getName() + "] .", cause);
      return null;
    }
  }

  @Override
  @Transactional
  public void saveFaceResource(FaceResource... resources) {
    for (FaceResource resource : resources) {
      if (null == resource) continue;

      FaceResourceInfo resourceInfo =
          FaceResourceInfo.createBuilder()
              .setResourceType(resource.getResourceType())
              .setContent(resource.getResourceContent())
              .setFaceId(resource.getFaceId())
              .setSaveType(resource.getSaveTyp())
              .build();
      faceResourceRepository.saveResource(resourceInfo);
    }
  }

  @Override
  public void saveUserPhoto(UserFacePhotoResource photoResource) throws FaceException {
    if (photoResource == null
        || com.timevale.mandarin.base.util.StringUtils.isBlank(
            photoResource.getResourceContent())) {
      log.warn("save user photo is null");
      return;
    }

    FaceLocalLibraryPhoto photoEntity = FaceLocalLibraryPhoto
            .createBuilder()
            .setProvider(photoResource.getProvider())
            .setIdNo(photoResource.getIdNo())
            .setName(photoResource.getName())
            .setPhoto(photoResource.getResourceContent())
            .setPhotoType(photoResource.isOriginPhoto())
            .setSourceId(photoResource.getFaceId())
            .setSourceType(LocalLibraryPhotoConstant.SOURCE_TYPE_FACE)
            .build();
    try {
      userPhotoRepository.saveUserPhoto(photoEntity);
    }catch (Exception e){
      log.error("save user local photo fail", e);
    }

//    UserPhoto photo =
//        UserPhoto.createBuilder()
//            .setPhoto(photoResource.getResourceContent())
//            .setPhotoType(photoResource.getMimeType())
//            .setIdNo(photoResource.getIdNo())
//            .setName(photoResource.getName())
//            .build();
//    userPhotoRepository.saveUserPhoto(photo);
  }
}
