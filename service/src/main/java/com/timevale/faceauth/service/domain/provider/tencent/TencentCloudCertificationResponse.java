package com.timevale.faceauth.service.domain.provider.tencent;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import lombok.Data;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/18 18
 */
@Data
class TencentCloudCertificationResponse<R> extends TencentCloudCertificationStatusResponse {

  private R result;

  R getResultIfAbsent() throws FaceException {
    if (success()) {
      return result;
    }

    throw FaceException.valueOf(
            FaceStatusCode.PROVIDER_ERROR_API_RESPONSE,
            "Fail response[" + getCode() + "," + getMsg() + "] .");
  }

 public R getResultIfAbsent(R defaultResult){
    return (null == result ? defaultResult : result);
  }
}
