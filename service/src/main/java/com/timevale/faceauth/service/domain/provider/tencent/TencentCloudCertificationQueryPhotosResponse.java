package com.timevale.faceauth.service.domain.provider.tencent;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/21 11
 */
@Data
public class TencentCloudCertificationQueryPhotosResponse {

  /** 0：成功 非0：失败 */
  private Integer code;

  /**
   * 请求结果描述
   */
  private String msg;
  /**
   * 请求业务流水号
   */
  private String bizSeqNo;


  private TencentCloudPhoto result;

  @Data
  public static class TencentCloudPhoto{

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 刷脸时间（yyyyMMddHHmmss）
     */
    private String occurredTime;

    //Base64 图像列表（返回1 - 3张照片），若照片不存在，则返回 null
    private List<String> photoList;
  }
}
