package com.timevale.faceauth.service.impl.support;

import com.timevale.faceauth.service.core.BizCodeService;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.core.support.FaceRequest;
import com.timevale.faceauth.service.core.support.FaceRequestBuilder;
import com.timevale.faceauth.service.core.support.SimpleFaceRequestBuilder;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import com.timevale.faceauth.service.enums.FaceAuthBizTypeEnum;
import com.timevale.faceauth.service.enums.FaceAuthModeEnum;
import com.timevale.faceauth.service.impl.FaceRequestResolver;
import com.timevale.faceauth.service.input.FaceAuthInput;
import com.timevale.faceauth.service.utils.providerLog.LogSupoort;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogConfiguration;
import com.timevale.faceauth.service.utils.providerLog.ProviderSupport;
import com.timevale.mandarin.weaver.utils.RequestContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/22 15
 */
@Component
@Slf4j
class SimpleFaceRequestResolver implements FaceRequestResolver {

  private static final String UNNAMED_APP_ID = "UNNAMED_APP_ID";
  private static final Map<FaceAuthModeEnum, String> PROVIDER_MAPPER;
  private static final Map<FaceAuthBizTypeEnum, String> BIZ_TYPE_MAPPER;
  @Autowired
  private ProviderSupport providerSupport;

  static {
    PROVIDER_MAPPER = (new HashMap<>(16));
    PROVIDER_MAPPER.put(
        FaceAuthModeEnum.ANT_BLOCK_CHAIN, ConfigurableProviderService.PROVIDER_ANT_BLOCK_CHAIN);
    PROVIDER_MAPPER.put(
        FaceAuthModeEnum.ALI_MINI_PROGRAM, ConfigurableProviderService.PROVIDER_ALI_MINI_PROG);
    PROVIDER_MAPPER.put(
        FaceAuthModeEnum.ALI_TENCENT_MINI_PROGR,
        ConfigurableProviderService.PROVIDER_ALI_TENCENT_MINI_PROG);
    PROVIDER_MAPPER.put(
        FaceAuthModeEnum.ZHIMA_XY, ConfigurableProviderService.PROVIDER_ANT_BLOCK_CHAIN);
    PROVIDER_MAPPER.put(
        FaceAuthModeEnum.ZHIMA_XY_XCX, ConfigurableProviderService.PROVIDER_ANT_BLOCK_CHAIN);
    PROVIDER_MAPPER.put(
        FaceAuthModeEnum.TECENT_CLOUD_H5, ConfigurableProviderService.PROVIDER_TENCENT_CLOUD);
    PROVIDER_MAPPER.put(
        FaceAuthModeEnum.TECENT_CLOUD_XCX, ConfigurableProviderService.PROVIDER_TENCENT_CLOUD);
    PROVIDER_MAPPER.put(FaceAuthModeEnum.DING_TALK, ConfigurableProviderService.PROVIDER_DING_TALK);
    PROVIDER_MAPPER.put(
        FaceAuthModeEnum.FACE_LIVENESS_RECOGNITION, ConfigurableProviderService.PROVIDER_LIVENESS);
    PROVIDER_MAPPER.put(
        FaceAuthModeEnum.AUDIO_VIDEO_DUAL, ConfigurableProviderService.PROVIDER_AUDIO_VIDEO_DUAL);
    PROVIDER_MAPPER.put(
        FaceAuthModeEnum.WE_CHAT_FACE, ConfigurableProviderService.PROVIDER_WE_CHAT_FACE);
    PROVIDER_MAPPER.put(
        FaceAuthModeEnum.WE_CHAT_VIDEO_DUAL,
        ConfigurableProviderService.PROVIDER_WE_CHAT_VIDEO_DUAL);
    PROVIDER_MAPPER.put(
            FaceAuthModeEnum.AUDIO_VIDEO_ESIGN,
            ConfigurableProviderService.PROVIDER_AUDIO_VIDEO_ESIGN);
    PROVIDER_MAPPER.put(
            FaceAuthModeEnum.BYTEDANCE, ConfigurableProviderService.PROVIDER_BYTEDANCE);

    //  //新增刷脸渠道配置 抖音刷脸
    PROVIDER_MAPPER.put(
            FaceAuthModeEnum.FACE_TIKTOK_MINI, ConfigurableProviderService.PROVIDER_FACE_TIKTOK_MINI);
    // 2024/8/12  mangcao     腾讯云SDK渠道
    PROVIDER_MAPPER.put(
            FaceAuthModeEnum.FACE_TECENT_SDK_BASIC, ConfigurableProviderService.PROVIDER_FACE_TENCENT_SDK_BASIC);
    PROVIDER_MAPPER.put(
            FaceAuthModeEnum.FACE_TECENT_SDK_PLUS, ConfigurableProviderService.PROVIDER_FACE_TENCENT_SDK_PLUS);

    BIZ_TYPE_MAPPER = (new HashMap<>(4));
    BIZ_TYPE_MAPPER.put(FaceAuthBizTypeEnum.REAL_NAME_AUTH, BizCodeService.BIZ_REALNAME);
    BIZ_TYPE_MAPPER.put(FaceAuthBizTypeEnum.WILL_AUTH, BizCodeService.BIZ_WILLNESS);
  }

  @Override
  public FaceRequest resolveRequest(FaceAuthInput faceAuthInput) throws FaceException {
    @SuppressWarnings("rawtypes")
    FaceRequestBuilder builder =
        (new SimpleFaceRequestBuilder())
            .setAppId(detectAppId(faceAuthInput))
            .setBizCode(detectBizCode(faceAuthInput))
            .setBizScene(faceAuthInput.getBizScene())
            .setBizContext(faceAuthInput.getBizContext())
            .setBizId(detectBizId(faceAuthInput))
            .setCallbackUrl(faceAuthInput.getCallbackUrl())
            .setIdNo(faceAuthInput.getIdNo())
            .setIdType(faceAuthInput.getCertType())
            .setName(faceAuthInput.getName())
            .setOid(faceAuthInput.getUserId())
            .setPhoto(faceAuthInput.getPhoto())
            .setPhotoType(faceAuthInput.getPhotoType())
            .setProvider(deduceProvider(faceAuthInput.getFaceAuthMode())).setDelayDoInit(faceAuthInput.isDelayDoInit())
            .setReturnUrl(faceAuthInput.getReturnUrl());
    return builder.build();
  }

  private String detectAppId(FaceAuthInput faceAuthInput) {
    String appId = faceAuthInput.getAppId();
    appId = (StringUtils.isEmpty(appId) ? RequestContext.getAppId() : appId);
    return (StringUtils.isEmpty(appId) ? UNNAMED_APP_ID : appId);
  }

  private String detectBizCode(FaceAuthInput faceAuthInput) {
    String bizCode = faceAuthInput.getBizCode();
    return (StringUtils.isEmpty(bizCode)
        ? BIZ_TYPE_MAPPER.get(faceAuthInput.getBizType())
        : bizCode);
  }

  @Override
  public String detectBizCode2(FaceAuthInput faceAuthInput) {
    return BIZ_TYPE_MAPPER.get(faceAuthInput.getBizType());
  }

  private String detectBizId(FaceAuthInput faceAuthInput) {
    String bizId = faceAuthInput.getBizId();
    return (StringUtils.isEmpty(bizId) ? UUID.randomUUID().toString().replace("-", "") : bizId);
  }

  private String deduceProvider(FaceAuthModeEnum faceAuthModeEnum) {
    String provider = PROVIDER_MAPPER.get(faceAuthModeEnum);
    provider = deduceProviderByStradegy(provider);
    if (null == provider) {
      throw FaceException.valueOf(
          FaceStatusCode.SERVICE_UNSUPPORTED,
          "Not found provider on face mode [" + faceAuthModeEnum.getMode() + "]");
    }
    return provider;
  }

  /**
   * 自研刷脸与腾讯云刷脸根据策略互相切换
   *
   * @param provider
   * @return
   */
  private String deduceProviderByStradegy(String provider) {
    if (!ProviderLogConfiguration.providerMonitorDegradationEnabled) return provider;
    String realProvider = providerSupport.deduceProviderByStradegy(provider);
    if (!provider.equalsIgnoreCase(realProvider)) {
      LogSupoort.providerLogInfo(log, "[" + provider + "] 切换备用通道 [" + realProvider + "]");
      return realProvider;
    }
    return provider;
  }
}
