package com.timevale.faceauth.service.utils.providerLog;

import com.timevale.faceauth.service.apispace.service.com.timevale.apispace.domain.manager.ManagerService;
import com.timevale.framework.puppeteer.model.ConfigChangeEvent;
import com.timevale.framework.puppeteer.spring.annotation.PuppeteerConfigChangeListener;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
@Component
@Slf4j
public class ProviderLogConfiguration {

  @Autowired private ManagerService managerService;

  public static final String
      ANTBLOCKCHAIN_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN =
          "antBlockChain.action.initialization.field.empty.mapping.stauts.unknown";
  public static final String ANTBLOCKCHAIN_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE =
      "antBlockChain.action.initialization.code.msg.mapping.stauts.failure";
  public static final String ANTBLOCKCHAIN_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS =
      "antBlockChain.action.initialization.code.msg.mapping.stauts.success";
  public static final String ANTBLOCKCHAIN_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN =
      "antBlockChain.action.query.field.empty.mapping.stauts.unknown";
  public static final String ANTBLOCKCHAIN_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE =
      "antBlockChain.action.query.code.msg.mapping.stauts.failure";
  public static final String ANTBLOCKCHAIN_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS =
      "antBlockChain.action.query.code.msg.mapping.stauts.success";

  public static final String LIVENESS_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN =
      "liveness.action.initialization.field.empty.mapping.stauts.unknown";
  public static final String LIVENESS_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN =
      "liveness.action.query.field.empty.mapping.stauts.unknown";
  public static final String LIVENESS_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE =
      "liveness.action.query.code.msg.mapping.stauts.failure";

  public static final String TENCENTCLOUD_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN =
      "tencentCloud.action.initialization.field.empty.mapping.stauts.unknown";
  public static final String TENCENTCLOUD_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE =
      "tencentCloud.action.initialization.code.msg.mapping.stauts.failure";
  public static final String TENCENTCLOUD_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS =
      "tencentCloud.action.initialization.code.msg.mapping.stauts.success";
  public static final String TENCENTCLOUD_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN =
      "tencentCloud.action.query.field.empty.mapping.stauts.unknown";
  public static final String TENCENTCLOUD_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE =
      "tencentCloud.action.query.code.msg.mapping.stauts.failure";
  public static final String TENCENTCLOUD_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS =
      "tencentCloud.action.query.code.msg.mapping.stauts.success";

  public static final String
      ALIMINIPROG_CERTIFYID_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN =
          "aliMiniProg.certifyId.action.initialization.field.empty.mapping.stauts.unknown";
  public static final String
      ALIMINIPROG_CERTIFYID_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE =
          "aliMiniProg.certifyId.action.initialization.code.msg.mapping.stauts.failure";
  public static final String
      ALIMINIPROG_CERTIFYID_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS =
          "aliMiniProg.certifyId.action.initialization.code.msg.mapping.stauts.success";
  public static final String
      ALIMINIPROG_CERTIFYURL_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN =
          "aliMiniProg.certifyUrl.action.initialization.field.empty.mapping.stauts.unknown";
  public static final String
      ALIMINIPROG_CERTIFYURL_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE =
          "aliMiniProg.certifyUrl.action.initialization.code.msg.mapping.stauts.failure";
  public static final String
      ALIMINIPROG_CERTIFYURL_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS =
          "aliMiniProg.certifyUrl.action.initialization.code.msg.mapping.stauts.success";
  public static final String ALIMINIPROG_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN =
      "aliMiniProg.action.query.field.empty.mapping.stauts.unknown";
  public static final String ALIMINIPROG_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE =
      "aliMiniProg.action.query.code.msg.mapping.stauts.failure";
  public static final String ALIMINIPROG_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS =
      "aliMiniProg.action.query.code.msg.mapping.stauts.success";

  public static final String WECHATFACE_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN =
      "weChatFace.action.initialization.field.empty.mapping.stauts.unknown";
  public static final String WECHATFACE_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN =
      "weChatFace.action.query.field.empty.mapping.stauts.unknown";
  public static final String WECHATFACE_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE =
      "weChatFace.action.query.code.msg.mapping.stauts.failure";
  public static final String WECHATFACE_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS =
      "weChatFace.action.query.code.msg.mapping.stauts.success";

  public static final String DINGTALK_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN =
      "dingTalk.action.query.field.empty.mapping.stauts.unknown";
  public static final String DINGTALK_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE =
      "dingTalk.action.query.code.msg.mapping.stauts.failure";
  public static final String DINGTALK_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS =
      "dingTalk.action.query.code.msg.mapping.stauts.success";

  public static final String
      AUDIOVIDEODUAL_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN =
          "audioVideoDual.action.initialization.field.empty.mapping.stauts.unknown";
  public static final String AUDIOVIDEODUAL_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN =
      "audioVideoDual.action.query.field.empty.mapping.stauts.unknown";
  public static final String AUDIOVIDEODUAL_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE =
      "audioVideoDual.action.query.code.msg.mapping.stauts.failure";
  public static final String AUDIOVIDEODUAL_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS =
      "audioVideoDual.action.query.code.msg.mapping.stauts.success";

  public static final String
      WECHATVIDEODUAL_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN =
          "weChatVideoDual.action.initialization.field.empty.mapping.stauts.unknown";
  public static final String WECHATVIDEODUAL_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN =
      "weChatVideoDual.action.query.field.empty.mapping.stauts.unknown";
  public static final String WECHATVIDEODUAL_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE =
      "weChatVideoDual.action.query.code.msg.mapping.stauts.failure";
  public static final String WECHATVIDEODUAL_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS =
      "weChatVideoDual.action.query.code.msg.mapping.stauts.success";

  public static final String
          BYTEDANCE_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN =
          "byteDance.action.initialization.field.empty.mapping.stauts.unknown";
  public static final String BYTEDANCE_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE =
          "byteDance.action.initialization.code.msg.mapping.stauts.failure";
  public static final String BYTEDANCE_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS =
          "byteDance.action.initialization.code.msg.mapping.stauts.success";
  public static final String BYTEDANCE_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN =
          "byteDance.action.query.field.empty.mapping.stauts.unknown";
  public static final String BYTEDANCE_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE =
          "byteDance.action.query.code.msg.mapping.stauts.failure";
  public static final String BYTEDANCE_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS =
          "byteDance.action.query.code.msg.mapping.stauts.success";

  public static final String PROVIDER_INVOKE_PRINT_LOG_ENABLED =
      "provider.invoke.print.log.enabled";

  public static final String PROVIDER_MONITOR_DEGRADATION_ENABLED =
      "provider.monitor.degradation.enabled";

  //#采集推送维度
  public static final String PROVIDER_MONITOR_STORE_CHANNEL =
          "provider.monitor.store.channel";

  public static final String ANTBLOCKCHAIN_DEGRADATION_STRADEGY_ENABLED =
      "antBlockChain.degradation.stradegy.enabled";

  public static final String LIVENESS_DEGRADATION_STRADEGY_ENABLED =
      "liveness.degradation.stradegy.enabled";

  public static final String TENCENTCLOUD_DEGRADATION_STRADEGY_ENABLED =
      "tencentCloud.degradation.stradegy.enabled";

  public static final String ANTBLOCKCHAIN_DEGRADATION_AUTO_RECOVERY_ENABLED =
      "antBlockChain.degradation.auto.recovery.enabled";

  public static final String ANTBLOCKCHAIN_DEGRADATION_AUTO_RECOVERY_COUNTDOWN =
      "antBlockChain.degradation.auto.recovery.countdown";

  public static final String PROVIDER_MONITOR_NOTIFY_URL = "provider.monitor.notify.url";

  public static final String PROVIDER_MONITOR_NOTIFY_FROM = "provider.monitor.notify.from";

  public static final String PROVIDER_MONITOR_ORANGE_KEYWORD_LIST =
      "provider.monitor.orange.keyword.list";
  public static final String PROVIDER_MONITOR_BALCK_KEYWORD_LIST =
      "provider.monitor.balck.keyword.list";

  // 支付宝区块链刷脸 初始化 响应信息中缺少必要字段，标记为未知状态
  public static List<String> antBlockChainActionInitializationFieldEmptyMappingStautsUnknown;
  // 支付宝区块链刷脸 初始化 响应信息中code+msg 标记为失败状态
  public static List<String> antBlockChainActionInitializationCodeMsgMappingStautsFailure;
  // 支付宝区块链刷脸 初始化 响应信息中code+msg 标记为成功状态
  public static List<String> antBlockChainActionInitializationCodeMsgMappingStautsSuccess;
  // 支付宝区块链刷脸 查询 响应信息中缺少必要字段，标记为未知状态
  public static List<String> antBlockChainActionQueryFieldEmptyMappingStautsUnknown;
  // 支付宝区块链刷脸 查询 响应信息中code+msg 标记为失败状态
  public static List<String> antBlockChainActionQueryCodeMsgMappingStautsFailure;
  // 支付宝区块链刷脸 查询 响应信息中code+msg 标记为成功状态
  public static List<String> antBlockChainActionQueryCodeMsgMappingStautsSuccess;

  // 自研刷脸 初始化 响应信息中缺少必要字段，标记为未知状态
  public static List<String> livenessActionInitializationFieldEmptyMappingStautsUnknown;
  // 自研刷脸 查询 响应信息中缺少必要字段，标记为未知状态
  public static List<String> livenessActionQueryFieldEmptyMappingStautsUnknown;
  // 自研刷脸 查询 响应信息中code+msg 标记为失败状态
  public static List<String> livenessActionQueryCodeMsgMappingStautsFailure;

  // 腾讯云刷脸 初始化 响应信息中缺少必要字段，标记为未知状态
  public static List<String> tencentcloudActionInitializationFieldEmptyMappingStautsUnknown;
  // 腾讯云刷脸 初始化 响应信息中code+msg 标记为失败状态
  public static List<String> tencentcloudActionInitializationCodeMsgMappingStautsFailure;
  // 腾讯云刷脸 初始化 响应信息中code+msg 标记为成功状态
  public static List<String> tencentcloudActionInitializationCodeMsgMappingStautsSuccess;
  // 腾讯云刷脸 查询 响应信息中缺少必要字段，标记为未知状态
  public static List<String> tencentcloudActionQueryFieldEmptyMappingStautsUnknown;
  // 腾讯云刷脸 查询 响应信息中code+msg 标记为失败状态
  public static List<String> tencentcloudActionQueryCodeMsgMappingStautsFailure;
  // 腾讯云刷脸 查询 响应信息中code+msg 标记为成功状态
  public static List<String> tencentcloudActionQueryCodeMsgMappingStautsSuccess;

  // 支付宝小程序刷脸 获取certifyId 响应信息中缺少必要字段，标记为未知状态
  public static List<String> aliMiniProgCertifyIdActionInitializationFieldEmptyMappingStautsUnknown;
  // 支付宝小程序刷脸 获取certifyId 响应信息中code+msg 标记为失败状态
  public static List<String> aliMiniProgCertifyIdActionInitializationCodeMsgMappingStautsFailure;
  // 支付宝小程序刷脸 获取certifyId 响应信息中code+msg 标记为成功状态
  public static List<String> aliMiniProgCertifyIdActionInitializationCodeMsgMappingStautsSuccess;
  // 支付宝小程序刷脸 获取certifyUrl 响应信息中缺少必要字段，标记为未知状态
  public static List<String>
      aliMiniProgCertifyUrlActionInitializationFieldEmptyMappingStautsUnknown;
  // 支付宝小程序刷脸 获取certifyUrl 响应信息中code+msg 标记为失败状态
  public static List<String> aliMiniProgCertifyUrlActionInitializationCodeMsgMappingStautsFailure;
  // 支付宝小程序刷脸 获取certifyUrl 响应信息中code+msg 标记为成功状态
  public static List<String> aliMiniProgCertifyUrlActionInitializationCodeMsgMappingStautsSuccess;
  // 支付宝小程序刷脸 查询 响应信息中缺少必要字段，标记为未知状态
  public static List<String> aliMiniProgActionQueryFieldEmptyMappingStautsUnknown;
  // 支付宝小程序刷脸 查询 响应信息中code+msg 标记为失败状态
  public static List<String> aliMiniProgActionQueryCodeMsgMappingStautsFailure;
  // 支付宝小程序刷脸 查询 响应信息中code+msg 标记为成功状态
  public static List<String> aliMiniProgActionQueryCodeMsgMappingStautsSuccess;

  // 微信小程序刷脸 初始化 响应信息中缺少必要字段，标记为未知状态
  public static List<String> weChatFaceActionInitializationFieldEmptyMappingStautsUnknown;
  // 微信小程序刷脸 查询 响应信息中缺少必要字段，标记为未知状态
  public static List<String> weChatFaceActionQueryFieldEmptyMappingStautsUnknown;
  // 微信小程序刷脸 查询 响应信息中code+msg 标记为失败状态
  public static List<String> weChatFaceActionQueryCodeMsgMappingStautsFailure;
  // 微信小程序刷脸 查询 响应信息中code+msg 标记为成功状态
  public static List<String> weChatFaceActionQueryCodeMsgMappingStautsSuccess;

  // 钉钉刷脸 查询 响应信息中缺少必要字段，标记为未知状态
  public static List<String> dingTalkActionQueryFieldEmptyMappingStautsUnknown;
  // 钉钉刷脸 查询 响应信息中code+msg 标记为失败状态
  public static List<String> dingTalkActionQueryCodeMsgMappingStautsFailure;
  // 钉钉刷脸 查询 响应信息中code+msg 标记为成功状态
  public static List<String> dingTalkActionQueryCodeMsgMappingStautsSuccess;

  // 支付宝视频双录 初始化 响应信息中缺少必要字段，标记为未知状态
  public static List<String> audioVideoDualActionInitializationFieldEmptyMappingStautsUnknown;
  // 支付宝视频双录 查询 响应信息中缺少必要字段，标记为未知状态
  public static List<String> audioVideoDualActionQueryFieldEmptyMappingStautsUnknown;
  // 支付宝视频双录 查询 响应信息中code+msg 标记为失败状态
  public static List<String> audioVideoDualActionQueryCodeMsgMappingStautsFailure;
  // 支付宝视频双录 查询 响应信息中code+msg 标记为成功状态
  public static List<String> audioVideoDualActionQueryCodeMsgMappingStautsSuccess;

  // 微信视频双录 初始化 响应信息中缺少必要字段，标记为未知状态
  public static List<String> weChatVideoDualActionInitializationFieldEmptyMappingStautsUnknown;
  // 微信视频双录 查询 响应信息中缺少必要字段，标记为未知状态
  public static List<String> weChatVideoDualActionQueryFieldEmptyMappingStautsUnknown;
  // 微信视频双录 查询 响应信息中code+msg 标记为失败状态
  public static List<String> weChatVideoDualActionQueryCodeMsgMappingStautsFailure;
  // 微信视频双录 查询 响应信息中code+msg 标记为成功状态
  public static List<String> weChatVideoDualActionQueryCodeMsgMappingStautsSuccess;

  // 字节火山刷脸 初始化 响应信息中缺少必要字段，标记为未知状态
  public static List<String> byteDanceActionInitializationFieldEmptyMappingStautsUnknown;
  // 字节火山刷脸 初始化 响应信息中code+msg 标记为失败状态
  public static List<String> byteDanceActionInitializationCodeMsgMappingStautsFailure;
  // 字节火山刷脸 初始化 响应信息中code+msg 标记为成功状态
  public static List<String> byteDanceActionInitializationCodeMsgMappingStautsSuccess;
  // 字节火山刷脸 查询 响应信息中缺少必要字段，标记为未知状态
  public static List<String> byteDanceActionQueryFieldEmptyMappingStautsUnknown;
  // 字节火山刷脸 查询 响应信息中code+msg 标记为失败状态
  public static List<String> byteDanceActionQueryCodeMsgMappingStautsFailure;
  // 字节火山刷脸 查询 响应信息中code+msg 标记为成功状态
  public static List<String> byteDanceActionQueryCodeMsgMappingStautsSuccess;


  // 日志开关
  public static boolean providerInvokePrintLogEnabled;
  // 总开关
  public static boolean providerMonitorDegradationEnabled;
  // #采集推送维度
  public static String providerMonitorStoreChannels;

  // 支付宝区块链刷脸 降级配置生效开关（页面操作）
  public static boolean antblockchainDegradationStradegyEnabled;
  // 自研刷脸 降级配置生效开关（页面操作）
  public static boolean livenessDegradationStradegyEnabled;
  // 腾讯云刷脸 降级配置生效开关（页面操作）
  public static boolean tencentCloudDegradationStradegyEnabled;

  // 降级配置自动恢复开关（页面操作）
  public static boolean antBlockChainDegradationAutoRecoveryEnabled;
  // 降级配置自动恢复时长（页面操作）
  public static int antBlockChainDegradationAutoRecoveryCountdown;

  // 监控告警通知地址
  public static String providerMonitorNotifyUrl;
  // 监控告警通知from
  public static String providerMonitorNotifyFrom;

  // 监控告警预警橙色关键字
  public static List<String> providerMonitorOrangeKeywordList;
  // 监控告警 供应商响应信息黑名单
  public static List<String> providerMonitorBalckKeywordList;

  @Value("${" + ANTBLOCKCHAIN_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN + "}")
  public void setAntblockchainActionInitializationFieldEmptyMappingStautsUnknown(
      String antBlockChainActionInitializationFieldEmptyMappingStautsUnknown) {
    ProviderLogConfiguration.antBlockChainActionInitializationFieldEmptyMappingStautsUnknown =
        stringSplitListByComma(antBlockChainActionInitializationFieldEmptyMappingStautsUnknown);
  }

  @Value("${" + ANTBLOCKCHAIN_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE + "}")
  public void setAntblockchainActionInitializationCodeMsgMappingStautsFailure(
      String antBlockChainActionInitializationCodeMsgMappingStautsFailure) {
    ProviderLogConfiguration.antBlockChainActionInitializationCodeMsgMappingStautsFailure =
        stringSplitListByComma(antBlockChainActionInitializationCodeMsgMappingStautsFailure);
  }

  @Value("${" + ANTBLOCKCHAIN_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS + "}")
  public void setAntblockchainActionInitializationCodeMsgMappingStautsSuccess(
      String antBlockChainActionInitializationCodeMsgMappingStautsSuccess) {
    ProviderLogConfiguration.antBlockChainActionInitializationCodeMsgMappingStautsSuccess =
        stringSplitListByComma(antBlockChainActionInitializationCodeMsgMappingStautsSuccess);
  }

  @Value("${" + ANTBLOCKCHAIN_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN + "}")
  public void setAntblockchainActionQueryFieldEmptyMappingStautsUnknown(
      String antBlockChainActionQueryFieldEmptyMappingStautsUnknown) {
    ProviderLogConfiguration.antBlockChainActionQueryFieldEmptyMappingStautsUnknown =
        stringSplitListByComma(antBlockChainActionQueryFieldEmptyMappingStautsUnknown);
  }

  @Value("${" + ANTBLOCKCHAIN_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE + "}")
  public void setAntblockchainActionQueryCodeMsgMappingStautsFailure(
      String antBlockChainActionQueryCodeMsgMappingStautsFailure) {
    ProviderLogConfiguration.antBlockChainActionQueryCodeMsgMappingStautsFailure =
        stringSplitListByComma(antBlockChainActionQueryCodeMsgMappingStautsFailure);
  }

  @Value("${" + ANTBLOCKCHAIN_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS + "}")
  public void setAntblockchainActionQueryCodeMsgMappingStautsSuccess(
      String antBlockChainActionQueryCodeMsgMappingStautsSuccess) {
    ProviderLogConfiguration.antBlockChainActionQueryCodeMsgMappingStautsSuccess =
        stringSplitListByComma(antBlockChainActionQueryCodeMsgMappingStautsSuccess);
  }

  @Value("${" + LIVENESS_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN + "}")
  public void setLivenessActionInitializationFieldEmptyMappingStautsUnknown(
      String livenessActionInitializationFieldEmptyMappingStautsUnknown) {
    ProviderLogConfiguration.livenessActionInitializationFieldEmptyMappingStautsUnknown =
        stringSplitListByComma(livenessActionInitializationFieldEmptyMappingStautsUnknown);
  }

  @Value("${" + LIVENESS_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN + "}")
  public void setLivenessActionQueryFieldEmptyMappingStautsUnknown(
      String livenessActionQueryFieldEmptyMappingStautsUnknown) {
    ProviderLogConfiguration.livenessActionQueryFieldEmptyMappingStautsUnknown =
        stringSplitListByComma(livenessActionQueryFieldEmptyMappingStautsUnknown);
  }

  @Value("${" + LIVENESS_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE + "}")
  public void setLivenessActionQueryCodeMsgMappingStautsFailure(
      String livenessActionQueryCodeMsgMappingStautsFailure) {
    ProviderLogConfiguration.livenessActionQueryCodeMsgMappingStautsFailure =
        stringSplitListByComma(livenessActionQueryCodeMsgMappingStautsFailure);
  }

  @Value("${" + TENCENTCLOUD_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN + "}")
  public void setTencentcloudActionInitializationFieldEmptyMappingStautsUnknown(
      String tencentcloudActionInitializationFieldEmptyMappingStautsUnknown) {
    ProviderLogConfiguration.tencentcloudActionInitializationFieldEmptyMappingStautsUnknown =
        stringSplitListByComma(tencentcloudActionInitializationFieldEmptyMappingStautsUnknown);
  }

  @Value("${" + TENCENTCLOUD_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE + "}")
  public void setTencentcloudActionInitializationCodeMsgMappingStautsFailure(
      String tencentcloudActionInitializationCodeMsgMappingStautsFailure) {
    ProviderLogConfiguration.tencentcloudActionInitializationCodeMsgMappingStautsFailure =
        stringSplitListByComma(tencentcloudActionInitializationCodeMsgMappingStautsFailure);
  }

  @Value("${" + TENCENTCLOUD_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS + "}")
  public void setTencentcloudActionInitializationCodeMsgMappingStautsSuccess(
      String tencentcloudActionInitializationCodeMsgMappingStautsSuccess) {
    ProviderLogConfiguration.tencentcloudActionInitializationCodeMsgMappingStautsSuccess =
        stringSplitListByComma(tencentcloudActionInitializationCodeMsgMappingStautsSuccess);
  }

  @Value("${" + TENCENTCLOUD_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN + "}")
  public void setTencentcloudActionQueryFieldEmptyMappingStautsUnknown(
      String tencentcloudFieldEmptyMappingStautsUnknown) {
    ProviderLogConfiguration.tencentcloudActionQueryFieldEmptyMappingStautsUnknown =
        stringSplitListByComma(tencentcloudFieldEmptyMappingStautsUnknown);
  }

  @Value("${" + TENCENTCLOUD_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE + "}")
  public void setTencentcloudActionQueryCodeMsgMappingStautsFailure(
      String tencentcloudCodeMsgMappingStautsFailure) {
    ProviderLogConfiguration.tencentcloudActionQueryCodeMsgMappingStautsFailure =
        stringSplitListByComma(tencentcloudCodeMsgMappingStautsFailure);
  }

  @Value("${" + TENCENTCLOUD_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS + "}")
  public void setTencentcloudActionQueryCodeMsgMappingStautsSuccess(
      String tencentcloudCodeMsgMappingStautsSuccess) {
    ProviderLogConfiguration.tencentcloudActionQueryCodeMsgMappingStautsSuccess =
        stringSplitListByComma(tencentcloudCodeMsgMappingStautsSuccess);
  }

  @Value(
      "${" + ALIMINIPROG_CERTIFYID_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN + "}")
  public void setAliminiprogCertifyIdActionInitializationFieldEmptyMappingStautsUnknown(
      String aliMiniProgCertifyIdActionInitializationFieldEmptyMappingStautsUnknown) {
    ProviderLogConfiguration
            .aliMiniProgCertifyIdActionInitializationFieldEmptyMappingStautsUnknown =
        stringSplitListByComma(
            aliMiniProgCertifyIdActionInitializationFieldEmptyMappingStautsUnknown);
  }

  @Value("${" + ALIMINIPROG_CERTIFYID_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE + "}")
  public void setAliminiprogCertifyIdActionInitializationCodeMsgMappingStautsFailure(
      String aliMiniProgCertifyIdActionInitializationCodeMsgMappingStautsFailure) {
    ProviderLogConfiguration.aliMiniProgCertifyIdActionInitializationCodeMsgMappingStautsFailure =
        stringSplitListByComma(aliMiniProgCertifyIdActionInitializationCodeMsgMappingStautsFailure);
  }

  @Value("${" + ALIMINIPROG_CERTIFYID_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS + "}")
  public void setAliminiprogCertifyIdActionInitializationCodeMsgMappingStautsSuccess(
      String aliMiniProgCertifyIdActionInitializationCodeMsgMappingStautsSuccess) {
    ProviderLogConfiguration.aliMiniProgCertifyIdActionInitializationCodeMsgMappingStautsSuccess =
        stringSplitListByComma(aliMiniProgCertifyIdActionInitializationCodeMsgMappingStautsSuccess);
  }

  @Value(
      "${" + ALIMINIPROG_CERTIFYURL_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN + "}")
  public void setAliminiprogCertifyUrlActionInitializationFieldEmptyMappingStautsUnknown(
      String aliMiniProgCertifyUrlActionInitializationFieldEmptyMappingStautsUnknown) {
    ProviderLogConfiguration
            .aliMiniProgCertifyUrlActionInitializationFieldEmptyMappingStautsUnknown =
        stringSplitListByComma(
            aliMiniProgCertifyUrlActionInitializationFieldEmptyMappingStautsUnknown);
  }

  @Value("${" + ALIMINIPROG_CERTIFYURL_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE + "}")
  public void setAliminiprogCertifyUrlActionInitializationCodeMsgMappingStautsFailure(
      String aliMiniProgCertifyUrlActionInitializationCodeMsgMappingStautsFailure) {
    ProviderLogConfiguration.aliMiniProgCertifyUrlActionInitializationCodeMsgMappingStautsFailure =
        stringSplitListByComma(
            aliMiniProgCertifyUrlActionInitializationCodeMsgMappingStautsFailure);
  }

  @Value("${" + ALIMINIPROG_CERTIFYURL_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS + "}")
  public void setAliminiprogCertifyUrlActionInitializationCodeMsgMappingStautsSuccess(
      String aliMiniProgCertifyUrlActionInitializationCodeMsgMappingStautsSuccess) {
    ProviderLogConfiguration.aliMiniProgCertifyUrlActionInitializationCodeMsgMappingStautsSuccess =
        stringSplitListByComma(
            aliMiniProgCertifyUrlActionInitializationCodeMsgMappingStautsSuccess);
  }

  @Value("${" + ALIMINIPROG_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN + "}")
  public void setAliminiprogActionQueryFieldEmptyMappingStautsUnknown(
      String aliMiniProgActionQueryFieldEmptyMappingStautsUnknown) {
    ProviderLogConfiguration.aliMiniProgActionQueryFieldEmptyMappingStautsUnknown =
        stringSplitListByComma(aliMiniProgActionQueryFieldEmptyMappingStautsUnknown);
  }

  @Value("${" + ALIMINIPROG_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE + "}")
  public void setAliminiprogActionQueryCodeMsgMappingStautsFailure(
      String aliMiniProgActionQueryCodeMsgMappingStautsFailure) {
    ProviderLogConfiguration.aliMiniProgActionQueryCodeMsgMappingStautsFailure =
        stringSplitListByComma(aliMiniProgActionQueryCodeMsgMappingStautsFailure);
  }

  @Value("${" + ALIMINIPROG_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS + "}")
  public void setAliminiprogActionQueryCodeMsgMappingStautsSuccess(
      String aliMiniProgActionQueryCodeMsgMappingStautsSuccess) {
    ProviderLogConfiguration.aliMiniProgActionQueryCodeMsgMappingStautsSuccess =
        stringSplitListByComma(aliMiniProgActionQueryCodeMsgMappingStautsSuccess);
  }

  @Value("${" + WECHATFACE_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN + "}")
  public void setWechatfaceActionInitializationFieldEmptyMappingStautsUnknown(
      String weChatFaceActionInitializationFieldEmptyMappingStautsUnknown) {
    ProviderLogConfiguration.weChatFaceActionInitializationFieldEmptyMappingStautsUnknown =
        stringSplitListByComma(weChatFaceActionInitializationFieldEmptyMappingStautsUnknown);
  }

  @Value("${" + WECHATFACE_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN + "}")
  public void setWechatfaceActionQueryFieldEmptyMappingStautsUnknown(
      String weChatFaceActionQueryFieldEmptyMappingStautsUnknown) {
    ProviderLogConfiguration.weChatFaceActionQueryFieldEmptyMappingStautsUnknown =
        stringSplitListByComma(weChatFaceActionQueryFieldEmptyMappingStautsUnknown);
  }

  @Value("${" + WECHATFACE_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE + "}")
  public void setWechatfaceActionQueryCodeMsgMappingStautsFailure(
      String weChatFaceActionQueryCodeMsgMappingStautsFailure) {
    ProviderLogConfiguration.weChatFaceActionQueryCodeMsgMappingStautsFailure =
        stringSplitListByComma(weChatFaceActionQueryCodeMsgMappingStautsFailure);
  }

  @Value("${" + WECHATFACE_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS + "}")
  public void setWechatfaceActionQueryCodeMsgMappingStautsSuccess(
      String weChatFaceActionQueryCodeMsgMappingStautsSuccess) {
    ProviderLogConfiguration.weChatFaceActionQueryCodeMsgMappingStautsSuccess =
        stringSplitListByComma(weChatFaceActionQueryCodeMsgMappingStautsSuccess);
  }

  @Value("${" + DINGTALK_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN + "}")
  public void setDingtalkActionQueryFieldEmptyMappingStautsUnknown(
      String dingTalkActionQueryFieldEmptyMappingStautsUnknown) {
    ProviderLogConfiguration.dingTalkActionQueryFieldEmptyMappingStautsUnknown =
        stringSplitListByComma(dingTalkActionQueryFieldEmptyMappingStautsUnknown);
  }

  @Value("${" + DINGTALK_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE + "}")
  public void setDingtalkActionQueryCodeMsgMappingStautsFailure(
      String dingTalkActionQueryCodeMsgMappingStautsFailure) {
    ProviderLogConfiguration.dingTalkActionQueryCodeMsgMappingStautsFailure =
        stringSplitListByComma(dingTalkActionQueryCodeMsgMappingStautsFailure);
  }

  @Value("${" + DINGTALK_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS + "}")
  public void setDingTalkActionQueryCodeMsgMappingStautsSuccess(
      String dingTalkActionQueryCodeMsgMappingStautsSuccess) {
    ProviderLogConfiguration.dingTalkActionQueryCodeMsgMappingStautsSuccess =
        stringSplitListByComma(dingTalkActionQueryCodeMsgMappingStautsSuccess);
  }

  @Value("${" + AUDIOVIDEODUAL_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN + "}")
  public void setAudiovideodualActionInitializationFieldEmptyMappingStautsUnknown(
      String audioVideoDualActionInitializationFieldEmptyMappingStautsUnknown) {
    ProviderLogConfiguration.audioVideoDualActionInitializationFieldEmptyMappingStautsUnknown =
        stringSplitListByComma(audioVideoDualActionInitializationFieldEmptyMappingStautsUnknown);
  }

  @Value("${" + AUDIOVIDEODUAL_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN + "}")
  public void setAudiovideodualActionQueryFieldEmptyMappingStautsUnknown(
      String audioVideoDualActionQueryFieldEmptyMappingStautsUnknown) {
    ProviderLogConfiguration.audioVideoDualActionQueryFieldEmptyMappingStautsUnknown =
        stringSplitListByComma(audioVideoDualActionQueryFieldEmptyMappingStautsUnknown);
  }

  @Value("${" + AUDIOVIDEODUAL_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE + "}")
  public void setAudiovideodualActionQueryCodeMsgMappingStautsFailure(
      String audioVideoDualActionQueryCodeMsgMappingStautsFailure) {
    ProviderLogConfiguration.audioVideoDualActionQueryCodeMsgMappingStautsFailure =
        stringSplitListByComma(audioVideoDualActionQueryCodeMsgMappingStautsFailure);
  }

  @Value("${" + AUDIOVIDEODUAL_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS + "}")
  public void setAudiovideodualActionQueryCodeMsgMappingStautsSuccess(
      String audioVideoDualActionQueryCodeMsgMappingStautsSuccess) {
    ProviderLogConfiguration.audioVideoDualActionQueryCodeMsgMappingStautsSuccess =
        stringSplitListByComma(audioVideoDualActionQueryCodeMsgMappingStautsSuccess);
  }

  @Value("${" + WECHATVIDEODUAL_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN + "}")
  public void setWechatvideodualActionInitializationFieldEmptyMappingStautsUnknown(
      String weChatVideoDualActionInitializationFieldEmptyMappingStautsUnknown) {
    ProviderLogConfiguration.weChatVideoDualActionInitializationFieldEmptyMappingStautsUnknown =
        stringSplitListByComma(weChatVideoDualActionInitializationFieldEmptyMappingStautsUnknown);
  }

  @Value("${" + WECHATVIDEODUAL_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN + "}")
  public void setWechatvideodualActionQueryFieldEmptyMappingStautsUnknown(
      String weChatVideoDualActionQueryFieldEmptyMappingStautsUnknown) {
    ProviderLogConfiguration.weChatVideoDualActionQueryFieldEmptyMappingStautsUnknown =
        stringSplitListByComma(weChatVideoDualActionQueryFieldEmptyMappingStautsUnknown);
  }

  @Value("${" + WECHATVIDEODUAL_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE + "}")
  public void setWechatvideodualActionQueryCodeMsgMappingStautsFailure(
      String weChatVideoDualActionQueryCodeMsgMappingStautsFailure) {
    ProviderLogConfiguration.weChatVideoDualActionQueryCodeMsgMappingStautsFailure =
        stringSplitListByComma(weChatVideoDualActionQueryCodeMsgMappingStautsFailure);
  }

  @Value("${" + WECHATVIDEODUAL_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS + "}")
  public void setWechatvideodualActionQueryCodeMsgMappingStautsSuccess(
      String weChatVideoDualActionQueryCodeMsgMappingStautsSuccess) {
    ProviderLogConfiguration.weChatVideoDualActionQueryCodeMsgMappingStautsSuccess =
        stringSplitListByComma(weChatVideoDualActionQueryCodeMsgMappingStautsSuccess);
  }

  @Value("${" + BYTEDANCE_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN + "}")
  public void setByteDanceActionInitializationFieldEmptyMappingStautsUnknown(
          String byteDanceActionInitializationFieldEmptyMappingStautsUnknown) {
    ProviderLogConfiguration.byteDanceActionInitializationFieldEmptyMappingStautsUnknown =
            stringSplitListByComma(byteDanceActionInitializationFieldEmptyMappingStautsUnknown);
  }

  @Value("${" + BYTEDANCE_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE + "}")
  public void setByteDanceActionInitializationCodeMsgMappingStautsFailure(
          String byteDanceActionInitializationCodeMsgMappingStautsFailure) {
    ProviderLogConfiguration.byteDanceActionInitializationCodeMsgMappingStautsFailure =
            stringSplitListByComma(byteDanceActionInitializationCodeMsgMappingStautsFailure);
  }

  @Value("${" + BYTEDANCE_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS + "}")
  public void setByteDanceActionInitializationCodeMsgMappingStautsSuccess(
          String byteDanceActionInitializationCodeMsgMappingStautsSuccess) {
    ProviderLogConfiguration.byteDanceActionInitializationCodeMsgMappingStautsSuccess =
            stringSplitListByComma(byteDanceActionInitializationCodeMsgMappingStautsSuccess);
  }

  @Value("${" + BYTEDANCE_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN + "}")
  public void setByteDanceActionQueryFieldEmptyMappingStautsUnknown(
          String byteDanceActionQueryFieldEmptyMappingStautsUnknown) {
    ProviderLogConfiguration.byteDanceActionQueryFieldEmptyMappingStautsUnknown =
            stringSplitListByComma(byteDanceActionQueryFieldEmptyMappingStautsUnknown);
  }

  @Value("${" + BYTEDANCE_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE + "}")
  public void setByteDanceActionQueryCodeMsgMappingStautsFailure(
          String byteDanceActionQueryCodeMsgMappingStautsFailure) {
    ProviderLogConfiguration.byteDanceActionQueryCodeMsgMappingStautsFailure =
            stringSplitListByComma(byteDanceActionQueryCodeMsgMappingStautsFailure);
  }

  @Value("${" + BYTEDANCE_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS + "}")
  public void setByteDanceActionQueryCodeMsgMappingStautsSuccess(
          String byteDanceActionQueryCodeMsgMappingStautsSuccess) {
    ProviderLogConfiguration.byteDanceActionQueryCodeMsgMappingStautsSuccess =
            stringSplitListByComma(byteDanceActionQueryCodeMsgMappingStautsSuccess);
  }

  @Value("${" + PROVIDER_INVOKE_PRINT_LOG_ENABLED + "}")
  public void setProviderInvokePrintLogEnabled(boolean providerInvokePrintLogEnabled) {
    ProviderLogConfiguration.providerInvokePrintLogEnabled = providerInvokePrintLogEnabled;
  }

  @Value("${" + PROVIDER_MONITOR_DEGRADATION_ENABLED + "}")
  public void setProviderMonitorDegradationEnabled(boolean providerMonitorDegradationEnabled) {
    ProviderLogConfiguration.providerMonitorDegradationEnabled = providerMonitorDegradationEnabled;
  }

  @Value("${" + PROVIDER_MONITOR_STORE_CHANNEL + "}")
  public void setProviderMonitorStoreChannels(String channels) {
    ProviderLogConfiguration.providerMonitorStoreChannels = channels;
  }

  @Value("${" + ANTBLOCKCHAIN_DEGRADATION_STRADEGY_ENABLED + "}")
  public void setAntblockchainDegradationStradegyEnabled(
      boolean antblockchainDegradationStradegyEnabled) {
    ProviderLogConfiguration.antblockchainDegradationStradegyEnabled =
        antblockchainDegradationStradegyEnabled;
  }

  @Value("${" + LIVENESS_DEGRADATION_STRADEGY_ENABLED + "}")
  public void setLivenessDegradationStradegyEnabled(boolean livenessDegradationStradegyEnabled) {
    ProviderLogConfiguration.livenessDegradationStradegyEnabled =
        livenessDegradationStradegyEnabled;
  }

  @Value("${" + TENCENTCLOUD_DEGRADATION_STRADEGY_ENABLED + "}")
  public void setTencentcloudDegradationStradegyEnabled(
      boolean tencentCloudDegradationStradegyEnabled) {
    ProviderLogConfiguration.tencentCloudDegradationStradegyEnabled =
        tencentCloudDegradationStradegyEnabled;
  }

  @Value("${" + ANTBLOCKCHAIN_DEGRADATION_AUTO_RECOVERY_ENABLED + "}")
  public void setAntblockchainDegradationAutoRecoveryEnabled(
      boolean antBlockChainDegradationAutoRecoveryEnabled) {
    ProviderLogConfiguration.antBlockChainDegradationAutoRecoveryEnabled =
        antBlockChainDegradationAutoRecoveryEnabled;
  }

  @Value("${" + ANTBLOCKCHAIN_DEGRADATION_AUTO_RECOVERY_COUNTDOWN + "}")
  public void setAntblockchainDegradationAutoRecoveryCountdown(
      int antBlockChainDegradationAutoRecoveryCountdown) {
    ProviderLogConfiguration.antBlockChainDegradationAutoRecoveryCountdown =
        antBlockChainDegradationAutoRecoveryCountdown;
  }

  @Value("${" + PROVIDER_MONITOR_NOTIFY_URL + "}")
  public void setProviderMonitorNotifyUrl(String providerMonitorNotifyUrl) {
    ProviderLogConfiguration.providerMonitorNotifyUrl = providerMonitorNotifyUrl;
  }

  @Value("${" + PROVIDER_MONITOR_NOTIFY_FROM + "}")
  public void setProviderMonitorNotifyFrom(String providerMonitorNotifyFrom) {
    ProviderLogConfiguration.providerMonitorNotifyFrom = providerMonitorNotifyFrom;
  }

  @Value("${" + PROVIDER_MONITOR_ORANGE_KEYWORD_LIST + "}")
  public void setProviderMonitorOrangeKeywordList(String providerMonitorOrangeKeywordList) {
    ProviderLogConfiguration.providerMonitorOrangeKeywordList =
        stringSplitListByComma(providerMonitorOrangeKeywordList);
  }

  @Value("${" + PROVIDER_MONITOR_BALCK_KEYWORD_LIST + "}")
  public void setProviderMonitorBalckKeywordList(String providerMonitorBalckKeywordList) {
    ProviderLogConfiguration.providerMonitorBalckKeywordList =
        stringSplitListByComma(providerMonitorBalckKeywordList);
  }

  @PuppeteerConfigChangeListener
  private void configListener(ConfigChangeEvent changeEvent) {
    if (changeEvent.isChanged(
        ANTBLOCKCHAIN_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)) {
      antBlockChainActionInitializationFieldEmptyMappingStautsUnknown =
          stringSplitListByComma(
              changeEvent
                  .getChange(ANTBLOCKCHAIN_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)
                  .getNewValue());
    }
    if (changeEvent.isChanged(
        ANTBLOCKCHAIN_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE)) {
      antBlockChainActionInitializationCodeMsgMappingStautsFailure =
          stringSplitListByComma(
              changeEvent
                  .getChange(ANTBLOCKCHAIN_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE)
                  .getNewValue());
    }
    if (changeEvent.isChanged(
        ANTBLOCKCHAIN_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS)) {
      antBlockChainActionInitializationCodeMsgMappingStautsSuccess =
          stringSplitListByComma(
              changeEvent
                  .getChange(ANTBLOCKCHAIN_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS)
                  .getNewValue());
    }
    if (changeEvent.isChanged(ANTBLOCKCHAIN_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)) {
      antBlockChainActionQueryFieldEmptyMappingStautsUnknown =
          stringSplitListByComma(
              changeEvent
                  .getChange(ANTBLOCKCHAIN_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)
                  .getNewValue());
    }
    if (changeEvent.isChanged(ANTBLOCKCHAIN_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE)) {
      antBlockChainActionQueryCodeMsgMappingStautsFailure =
          stringSplitListByComma(
              changeEvent
                  .getChange(ANTBLOCKCHAIN_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE)
                  .getNewValue());
    }
    if (changeEvent.isChanged(ANTBLOCKCHAIN_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS)) {
      antBlockChainActionQueryCodeMsgMappingStautsSuccess =
          stringSplitListByComma(
              changeEvent
                  .getChange(ANTBLOCKCHAIN_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS)
                  .getNewValue());
    }
    if (changeEvent.isChanged(LIVENESS_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)) {
      livenessActionInitializationFieldEmptyMappingStautsUnknown =
          stringSplitListByComma(
              changeEvent
                  .getChange(LIVENESS_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)
                  .getNewValue());
    }
    if (changeEvent.isChanged(LIVENESS_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)) {
      livenessActionQueryFieldEmptyMappingStautsUnknown =
          stringSplitListByComma(
              changeEvent
                  .getChange(LIVENESS_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)
                  .getNewValue());
    }
    if (changeEvent.isChanged(LIVENESS_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE)) {
      livenessActionQueryCodeMsgMappingStautsFailure =
          stringSplitListByComma(
              changeEvent
                  .getChange(LIVENESS_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE)
                  .getNewValue());
    }
    if (changeEvent.isChanged(
        TENCENTCLOUD_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)) {
      tencentcloudActionInitializationFieldEmptyMappingStautsUnknown =
          stringSplitListByComma(
              changeEvent
                  .getChange(TENCENTCLOUD_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)
                  .getNewValue());
    }
    if (changeEvent.isChanged(TENCENTCLOUD_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE)) {
      tencentcloudActionInitializationCodeMsgMappingStautsFailure =
          stringSplitListByComma(
              changeEvent
                  .getChange(TENCENTCLOUD_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE)
                  .getNewValue());
    }
    if (changeEvent.isChanged(TENCENTCLOUD_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS)) {
      tencentcloudActionInitializationCodeMsgMappingStautsSuccess =
          stringSplitListByComma(
              changeEvent
                  .getChange(TENCENTCLOUD_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS)
                  .getNewValue());
    }
    if (changeEvent.isChanged(TENCENTCLOUD_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)) {
      tencentcloudActionQueryFieldEmptyMappingStautsUnknown =
          stringSplitListByComma(
              changeEvent
                  .getChange(TENCENTCLOUD_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)
                  .getNewValue());
    }
    if (changeEvent.isChanged(TENCENTCLOUD_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE)) {
      tencentcloudActionQueryCodeMsgMappingStautsFailure =
          stringSplitListByComma(
              changeEvent
                  .getChange(TENCENTCLOUD_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE)
                  .getNewValue());
    }
    if (changeEvent.isChanged(TENCENTCLOUD_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS)) {
      tencentcloudActionQueryCodeMsgMappingStautsSuccess =
          stringSplitListByComma(
              changeEvent
                  .getChange(TENCENTCLOUD_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS)
                  .getNewValue());
    }
    if (changeEvent.isChanged(
        ALIMINIPROG_CERTIFYID_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)) {
      aliMiniProgCertifyIdActionInitializationFieldEmptyMappingStautsUnknown =
          stringSplitListByComma(
              changeEvent
                  .getChange(
                      ALIMINIPROG_CERTIFYID_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)
                  .getNewValue());
    }
    if (changeEvent.isChanged(
        ALIMINIPROG_CERTIFYID_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE)) {
      aliMiniProgCertifyIdActionInitializationCodeMsgMappingStautsFailure =
          stringSplitListByComma(
              changeEvent
                  .getChange(
                      ALIMINIPROG_CERTIFYID_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE)
                  .getNewValue());
    }
    if (changeEvent.isChanged(
        ALIMINIPROG_CERTIFYID_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS)) {
      aliMiniProgCertifyIdActionInitializationCodeMsgMappingStautsSuccess =
          stringSplitListByComma(
              changeEvent
                  .getChange(
                      ALIMINIPROG_CERTIFYID_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS)
                  .getNewValue());
    }
    if (changeEvent.isChanged(
        ALIMINIPROG_CERTIFYURL_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)) {
      aliMiniProgCertifyUrlActionInitializationFieldEmptyMappingStautsUnknown =
          stringSplitListByComma(
              changeEvent
                  .getChange(
                      ALIMINIPROG_CERTIFYURL_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)
                  .getNewValue());
    }
    if (changeEvent.isChanged(
        ALIMINIPROG_CERTIFYURL_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE)) {
      aliMiniProgCertifyUrlActionInitializationCodeMsgMappingStautsFailure =
          stringSplitListByComma(
              changeEvent
                  .getChange(
                      ALIMINIPROG_CERTIFYURL_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE)
                  .getNewValue());
    }
    if (changeEvent.isChanged(
        ALIMINIPROG_CERTIFYURL_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS)) {
      aliMiniProgCertifyUrlActionInitializationCodeMsgMappingStautsSuccess =
          stringSplitListByComma(
              changeEvent
                  .getChange(
                      ALIMINIPROG_CERTIFYURL_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS)
                  .getNewValue());
    }
    if (changeEvent.isChanged(ALIMINIPROG_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)) {
      aliMiniProgActionQueryFieldEmptyMappingStautsUnknown =
          stringSplitListByComma(
              changeEvent
                  .getChange(ALIMINIPROG_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)
                  .getNewValue());
    }
    if (changeEvent.isChanged(ALIMINIPROG_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE)) {
      aliMiniProgActionQueryCodeMsgMappingStautsFailure =
          stringSplitListByComma(
              changeEvent
                  .getChange(ALIMINIPROG_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE)
                  .getNewValue());
    }
    if (changeEvent.isChanged(ALIMINIPROG_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS)) {
      aliMiniProgActionQueryCodeMsgMappingStautsSuccess =
          stringSplitListByComma(
              changeEvent
                  .getChange(ALIMINIPROG_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS)
                  .getNewValue());
    }
    if (changeEvent.isChanged(
        WECHATFACE_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)) {
      weChatFaceActionInitializationFieldEmptyMappingStautsUnknown =
          stringSplitListByComma(
              changeEvent
                  .getChange(WECHATFACE_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)
                  .getNewValue());
    }
    if (changeEvent.isChanged(WECHATFACE_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)) {
      weChatFaceActionQueryFieldEmptyMappingStautsUnknown =
          stringSplitListByComma(
              changeEvent
                  .getChange(WECHATFACE_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)
                  .getNewValue());
    }
    if (changeEvent.isChanged(WECHATFACE_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE)) {
      weChatFaceActionQueryCodeMsgMappingStautsFailure =
          stringSplitListByComma(
              changeEvent
                  .getChange(WECHATFACE_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE)
                  .getNewValue());
    }
    if (changeEvent.isChanged(WECHATFACE_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS)) {
      weChatFaceActionQueryCodeMsgMappingStautsSuccess =
          stringSplitListByComma(
              changeEvent
                  .getChange(WECHATFACE_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS)
                  .getNewValue());
    }
    if (changeEvent.isChanged(DINGTALK_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)) {
      dingTalkActionQueryFieldEmptyMappingStautsUnknown =
          stringSplitListByComma(
              changeEvent
                  .getChange(DINGTALK_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)
                  .getNewValue());
    }
    if (changeEvent.isChanged(DINGTALK_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE)) {
      dingTalkActionQueryCodeMsgMappingStautsFailure =
          stringSplitListByComma(
              changeEvent
                  .getChange(DINGTALK_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE)
                  .getNewValue());
    }
    if (changeEvent.isChanged(DINGTALK_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS)) {
      dingTalkActionQueryCodeMsgMappingStautsSuccess =
          stringSplitListByComma(
              changeEvent
                  .getChange(DINGTALK_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS)
                  .getNewValue());
    }

    if (changeEvent.isChanged(
        AUDIOVIDEODUAL_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)) {
      audioVideoDualActionInitializationFieldEmptyMappingStautsUnknown =
          stringSplitListByComma(
              changeEvent
                  .getChange(
                      AUDIOVIDEODUAL_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)
                  .getNewValue());
    }
    if (changeEvent.isChanged(AUDIOVIDEODUAL_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)) {
      audioVideoDualActionQueryFieldEmptyMappingStautsUnknown =
          stringSplitListByComma(
              changeEvent
                  .getChange(AUDIOVIDEODUAL_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)
                  .getNewValue());
    }
    if (changeEvent.isChanged(AUDIOVIDEODUAL_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE)) {
      audioVideoDualActionQueryCodeMsgMappingStautsFailure =
          stringSplitListByComma(
              changeEvent
                  .getChange(AUDIOVIDEODUAL_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE)
                  .getNewValue());
    }
    if (changeEvent.isChanged(AUDIOVIDEODUAL_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS)) {
      audioVideoDualActionQueryCodeMsgMappingStautsSuccess =
          stringSplitListByComma(
              changeEvent
                  .getChange(AUDIOVIDEODUAL_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS)
                  .getNewValue());
    }

    if (changeEvent.isChanged(
        WECHATVIDEODUAL_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)) {
      weChatVideoDualActionInitializationFieldEmptyMappingStautsUnknown =
          stringSplitListByComma(
              changeEvent
                  .getChange(
                      WECHATVIDEODUAL_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)
                  .getNewValue());
    }
    if (changeEvent.isChanged(WECHATVIDEODUAL_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)) {
      weChatVideoDualActionQueryFieldEmptyMappingStautsUnknown =
          stringSplitListByComma(
              changeEvent
                  .getChange(WECHATVIDEODUAL_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)
                  .getNewValue());
    }
    if (changeEvent.isChanged(WECHATVIDEODUAL_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE)) {
      weChatVideoDualActionQueryCodeMsgMappingStautsFailure =
          stringSplitListByComma(
              changeEvent
                  .getChange(WECHATVIDEODUAL_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE)
                  .getNewValue());
    }
    if (changeEvent.isChanged(WECHATVIDEODUAL_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS)) {
      weChatVideoDualActionQueryCodeMsgMappingStautsSuccess =
          stringSplitListByComma(
              changeEvent
                  .getChange(WECHATVIDEODUAL_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS)
                  .getNewValue());
    }
    if (changeEvent.isChanged(
            BYTEDANCE_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)) {
      byteDanceActionInitializationFieldEmptyMappingStautsUnknown =
              stringSplitListByComma(
                      changeEvent
                              .getChange(BYTEDANCE_ACTION_INITIALIZATION_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)
                              .getNewValue());
    }
    if (changeEvent.isChanged(
            BYTEDANCE_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE)) {
      byteDanceActionInitializationCodeMsgMappingStautsFailure =
              stringSplitListByComma(
                      changeEvent
                              .getChange(BYTEDANCE_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_FAILURE)
                              .getNewValue());
    }
    if (changeEvent.isChanged(
            BYTEDANCE_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS)) {
      byteDanceActionInitializationCodeMsgMappingStautsSuccess =
              stringSplitListByComma(
                      changeEvent
                              .getChange(BYTEDANCE_ACTION_INITIALIZATION_CODE_MSG_MAPPING_STAUTS_SUCCESS)
                              .getNewValue());
    }
    if (changeEvent.isChanged(BYTEDANCE_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)) {
      byteDanceActionQueryFieldEmptyMappingStautsUnknown =
              stringSplitListByComma(
                      changeEvent
                              .getChange(BYTEDANCE_ACTION_QUERY_FIELD_EMPTY_MAPPING_STAUTS_UNKNOWN)
                              .getNewValue());
    }
    if (changeEvent.isChanged(BYTEDANCE_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE)) {
      byteDanceActionQueryCodeMsgMappingStautsFailure =
              stringSplitListByComma(
                      changeEvent
                              .getChange(BYTEDANCE_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_FAILURE)
                              .getNewValue());
    }
    if (changeEvent.isChanged(BYTEDANCE_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS)) {
      byteDanceActionQueryCodeMsgMappingStautsSuccess =
              stringSplitListByComma(
                      changeEvent
                              .getChange(BYTEDANCE_ACTION_QUERY_CODE_MSG_MAPPING_STAUTS_SUCCESS)
                              .getNewValue());
    }
    if (changeEvent.isChanged(PROVIDER_INVOKE_PRINT_LOG_ENABLED)) {
      providerInvokePrintLogEnabled =
          Boolean.parseBoolean(
              changeEvent.getChange(PROVIDER_INVOKE_PRINT_LOG_ENABLED).getNewValue());
    }
    if (changeEvent.isChanged(PROVIDER_MONITOR_DEGRADATION_ENABLED)) {
      providerMonitorDegradationEnabled =
          Boolean.parseBoolean(
              changeEvent.getChange(PROVIDER_MONITOR_DEGRADATION_ENABLED).getNewValue());
    }
    if (changeEvent.isChanged(PROVIDER_MONITOR_STORE_CHANNEL)) {
      providerMonitorStoreChannels = changeEvent.getChange(PROVIDER_MONITOR_STORE_CHANNEL).getNewValue();
    }
    if (changeEvent.isChanged(ANTBLOCKCHAIN_DEGRADATION_STRADEGY_ENABLED)) {
      antblockchainDegradationStradegyEnabled =
          Boolean.parseBoolean(
              changeEvent.getChange(ANTBLOCKCHAIN_DEGRADATION_STRADEGY_ENABLED).getNewValue());
      managerService.puppeteerSwitchEnabledRefreshDatabase(
          ProviderKeyIdEnum.AntBlockChain.getProviderId(), antblockchainDegradationStradegyEnabled);
    }
    if (changeEvent.isChanged(LIVENESS_DEGRADATION_STRADEGY_ENABLED)) {
      livenessDegradationStradegyEnabled =
          Boolean.parseBoolean(
              changeEvent.getChange(LIVENESS_DEGRADATION_STRADEGY_ENABLED).getNewValue());
      managerService.puppeteerSwitchEnabledRefreshDatabase(
          ProviderKeyIdEnum.Liveness.getProviderId(), livenessDegradationStradegyEnabled);
    }
    if (changeEvent.isChanged(TENCENTCLOUD_DEGRADATION_STRADEGY_ENABLED)) {
      tencentCloudDegradationStradegyEnabled =
          Boolean.parseBoolean(
              changeEvent.getChange(TENCENTCLOUD_DEGRADATION_STRADEGY_ENABLED).getNewValue());
      managerService.puppeteerSwitchEnabledRefreshDatabase(
          ProviderKeyIdEnum.TencentCloud.getProviderId(), tencentCloudDegradationStradegyEnabled);
    }
    if (changeEvent.isChanged(ANTBLOCKCHAIN_DEGRADATION_AUTO_RECOVERY_ENABLED)) {
      antBlockChainDegradationAutoRecoveryEnabled =
          Boolean.parseBoolean(
              changeEvent.getChange(ANTBLOCKCHAIN_DEGRADATION_AUTO_RECOVERY_ENABLED).getNewValue());
      managerService.puppeteerRecoveryhEnabledRefreshDatabase(
          ProviderKeyIdEnum.AntBlockChain.getProviderId(),
          antBlockChainDegradationAutoRecoveryEnabled ? 1 : 0);
    }
    if (changeEvent.isChanged(ANTBLOCKCHAIN_DEGRADATION_AUTO_RECOVERY_COUNTDOWN)) {
      antBlockChainDegradationAutoRecoveryCountdown =
          Integer.parseInt(
              changeEvent
                  .getChange(ANTBLOCKCHAIN_DEGRADATION_AUTO_RECOVERY_COUNTDOWN)
                  .getNewValue());
      managerService.puppeteerRecoveryhCountDownRefreshDatabase(
          ProviderKeyIdEnum.AntBlockChain.getProviderId(),
          antBlockChainDegradationAutoRecoveryCountdown);
    }
    if (changeEvent.isChanged(PROVIDER_MONITOR_NOTIFY_URL)) {
      providerMonitorNotifyUrl = changeEvent.getChange(PROVIDER_MONITOR_NOTIFY_URL).getNewValue();
    }
    if (changeEvent.isChanged(PROVIDER_MONITOR_NOTIFY_FROM)) {
      providerMonitorNotifyFrom = changeEvent.getChange(PROVIDER_MONITOR_NOTIFY_FROM).getNewValue();
    }
    if (changeEvent.isChanged(PROVIDER_MONITOR_ORANGE_KEYWORD_LIST)) {
      providerMonitorOrangeKeywordList =
          stringSplitListByComma(
              changeEvent.getChange(PROVIDER_MONITOR_ORANGE_KEYWORD_LIST).getNewValue());
    }
    if (changeEvent.isChanged(PROVIDER_MONITOR_BALCK_KEYWORD_LIST)) {
      providerMonitorBalckKeywordList =
          stringSplitListByComma(
              changeEvent.getChange(PROVIDER_MONITOR_BALCK_KEYWORD_LIST).getNewValue());
    }
  }

  private List<String> stringSplitListByComma(String value) {
    return stringSplitList(value, ",");
  }

  private List<String> stringSplitList(String value, String symbol) {
    return StringUtils.isBlank(value)
        ? Collections.emptyList()
        : Arrays.asList(value.split(symbol));
  }
}
