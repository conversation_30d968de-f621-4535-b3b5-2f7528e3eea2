package com.timevale.faceauth.service.utils.providerLog;

import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
public enum ProviderKeyIdEnum {
  AntBlockChain(
      ConfigurableProviderService.PROVIDER_ANT_BLOCK_CHAIN,
      "AntBlockChain",
      "08cbfefad80940909d6cd17263c619de",
      "218903b4b382466bb51cbb74274b14c5",
      "3773932a580445ca9f0907642d732c0a",
      null),
  Liveness(
      ConfigurableProviderService.PROVIDER_LIVENESS,
      "Liveness",
      "43e2c3576f454e71bc525b49fb5d4a9f",
      "218903b4b382466bb51cbb74274b14c5",
      "3773932a580445ca9f0907642d732c0a",
      null),
  TencentCloud(
      ConfigurableProviderService.PROVIDER_TENCENT_CLOUD,
      "TencentCloud",
      "eb39dd5a0d6d4c05b18b577640243c15",
      "218903b4b382466bb51cbb74274b14c5",
      "3773932a580445ca9f0907642d732c0a",
      null),
  AliMiniProg(
      ConfigurableProviderService.PROVIDER_ALIPAY,
      "AliMiniProg",
      "771327e0018a456f8ef87de30b35c809",
      "218903b4b382466bb51cbb74274b14c5",
      "3773932a580445ca9f0907642d732c0a",
      null),
  WeChatFace(
      ConfigurableProviderService.PROVIDER_WE_CHAT_FACE,
      "WeChatFace",
      "5c8c7caf757741a79cca09adb36e48ba",
      "218903b4b382466bb51cbb74274b14c5",
      "3773932a580445ca9f0907642d732c0a",
      null),
  DingTalk(
      ConfigurableProviderService.PROVIDER_DING_TALK,
      "DingTalk",
      "5420e59bf8e0478fa5b6223744eb8e97",
      "218903b4b382466bb51cbb74274b14c5",
      "3773932a580445ca9f0907642d732c0a",
      null),
  AudioVideoDual(
      ConfigurableProviderService.PROVIDER_AUDIO_VIDEO_DUAL,
      "AudioVideoDual",
      "63f1fa4ff7154f5ca675419b8b8e6709",
      "218903b4b382466bb51cbb74274b14c5",
      "3773932a580445ca9f0907642d732c0a",
      null),
  WeChatVideoDual(
      ConfigurableProviderService.PROVIDER_WE_CHAT_VIDEO_DUAL,
      "WeChatVideoDual",
      "8597385e987f42db92f6111d088fd143",
      "218903b4b382466bb51cbb74274b14c5",
      "3773932a580445ca9f0907642d732c0a",
      null),
  ByteDance(
      ConfigurableProviderService.PROVIDER_BYTEDANCE,
      "ByteDance",
      "d1c83f28e2314764a3183885ac279492",
      "218903b4b382466bb51cbb74274b14c5",
      "3773932a580445ca9f0907642d732c0a",
      null),
  Mock(
          ConfigurableProviderService.PROVIDER_MOCK,
          "Mock",
          "d1c83f28e2314764a3183885ac279492",
          "218903b4b382466bb51cbb74274b14c5",
          "3773932a580445ca9f0907642d732c0a",
          null),
  ;

  private String providerName;
  /** 供应商唯一标识 */
  private String providerKey;
  /** 供应商全局唯一ID */
  private String providerId;
  /** 供应商所属一级空间唯一ID */
  private String firstSpaceId;
  /** 供应商所属二级空间唯一ID */
  private String secondSpaceId;
  /** 供应商所属三级空间唯一ID */
  private String thirdSpaceId;

  ProviderKeyIdEnum(
      String providerName,
      String providerKey,
      String providerId,
      String firstSpaceId,
      String secondSpaceId,
      String thirdSpaceId) {
    this.providerName = providerName;
    this.providerKey = providerKey;
    this.providerId = providerId;
    this.firstSpaceId = firstSpaceId;
    this.secondSpaceId = secondSpaceId;
    this.thirdSpaceId = thirdSpaceId;
  }

  public String getProviderName() {
    return providerName;
  }

  public String getProviderKey() {
    return providerKey;
  }

  public String getProviderId() {
    return providerId;
  }

  public String getFirstSpaceId() {
    return firstSpaceId;
  }

  public String getSecondSpaceId() {
    return secondSpaceId;
  }

  public static ProviderKeyIdEnum getByProviderName(String provider) {
    for (ProviderKeyIdEnum providerKeyIdEnum : ProviderKeyIdEnum.values()) {
      if (providerKeyIdEnum.getProviderName().equalsIgnoreCase(provider)) {
        return providerKeyIdEnum;
      }
    }
    return null;
  }

  public static ProviderKeyIdEnum getByProviderId(String providerId) {
    for (ProviderKeyIdEnum providerKeyIdEnum : ProviderKeyIdEnum.values()) {
      if (providerKeyIdEnum.getProviderId().equalsIgnoreCase(providerId)) {
        return providerKeyIdEnum;
      }
    }
    return null;
  }
}
