package com.timevale.faceauth.service.utils.providerLog.result;

import cn.com.antcloud.api.realperson.v1_0_0.response.CreateFacevrfServerResponse;
import com.timevale.faceauth.service.utils.providerLog.AbstractProviderLogResultResolver;
import com.timevale.faceauth.service.utils.providerLog.ProviderActionEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderKeyIdEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogConfiguration;
import java.util.List;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
public class AntBlockChainInitResult
    extends AbstractProviderLogResultResolver<CreateFacevrfServerResponse> {

  public AntBlockChainInitResult(String bizId, long responseTime) {
    super(bizId, responseTime);
  }

  @Override
  public ProviderKeyIdEnum getProviderKeyId() {
    return ProviderKeyIdEnum.AntBlockChain;
  }

  @Override
  public ProviderActionEnum getAction() {
    return ProviderActionEnum.INITIALIZATION;
  }

  @Override
  public String getCode(CreateFacevrfServerResponse response) {
    return response.getResultCode();
  }

  @Override
  public String getMsg(CreateFacevrfServerResponse response) {
    return response.getResultMsg();
  }

  @Override
  public String getResult(CreateFacevrfServerResponse response) {
    return null;
  }

  @Override
  public List<String> getFieldEmptyMappingStautsUnknown() {
    return ProviderLogConfiguration.antBlockChainActionInitializationFieldEmptyMappingStautsUnknown;
  }

  @Override
  public List<String> getCodeMsgMappingStautsFailure() {
    return ProviderLogConfiguration.antBlockChainActionInitializationCodeMsgMappingStautsFailure;
  }

  @Override
  public List<String> getCodeMsgMappingStautsSuccess() {
    return ProviderLogConfiguration.antBlockChainActionInitializationCodeMsgMappingStautsSuccess;
  }
}
