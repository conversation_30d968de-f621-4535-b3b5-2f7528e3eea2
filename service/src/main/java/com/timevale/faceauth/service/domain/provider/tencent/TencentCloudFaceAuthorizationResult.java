package com.timevale.faceauth.service.domain.provider.tencent;

import com.timevale.faceauth.service.core.support.ArgumentUtil;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.support.AbstractProviderFaceAuthorizationResult;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/18 19
 */
public class TencentCloudFaceAuthorizationResult extends AbstractProviderFaceAuthorizationResult
    implements ProviderFaceAuthorizationResult {

  private TencentCloudFaceAuthorizationResult(TencentCloudFaceAuthorizationResultBuilder builder) {
    super(builder);
  }

 public static TencentCloudFaceAuthorizationResultBuilder createBuilder() {
    return (new TencentCloudFaceAuthorizationResultBuilder());
  }

  public static class TencentCloudFaceAuthorizationResultBuilder
      extends ProviderFaceAuthorizationResultBuilder<
          TencentCloudFaceAuthorizationResult, TencentCloudFaceAuthorizationResultBuilder> {

    private TencentCloudFaceAuthorizationResultBuilder() {
      super((String) null);
    }

    /**
     * @see com.timevale.faceauth.service.domain.provider.ali.wechat.AliTencentProgAuthorizationResult.AliTencentMinProgBuilder#provider
     */
    private String provider = TencentCloudService.PROVIDER;

    public TencentCloudFaceAuthorizationResult.TencentCloudFaceAuthorizationResultBuilder setProvider(String provider) {
      ArgumentUtil.throwIfEmptyArgument(provider, "provider");
      this.provider = provider;
      return this;
    }
    @Override
    public String getProvider() {
      ArgumentUtil.throwIfEmptyArgument(provider, "provider");
      return this.provider;
    }

    @Override
    public TencentCloudFaceAuthorizationResult build() {
      return (new TencentCloudFaceAuthorizationResult(this));
    }
  }
}
