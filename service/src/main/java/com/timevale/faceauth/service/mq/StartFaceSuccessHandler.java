package com.timevale.faceauth.service.mq;

import com.alibaba.fastjson.JSON;
import com.timevale.faceauth.service.integration.SpringTool;
import com.timevale.faceauth.service.impl.api.DelegateFaceAuthorizationService;
import com.timevale.faceauth.service.input.FaceAuthResInput;
import com.timevale.faceauth.service.result.QueryFaceAuthResResult;
import com.timevale.faceauth.service.result.SupportResult;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

/**
 * 发起刷脸MQ消息处理
 */
@Slf4j
public class StartFaceSuccessHandler extends AbstractMessageHandler {

  private MQManager mqManager;
  private DelegateFaceAuthorizationService delegateFaceAuthorizationService;

  public StartFaceSuccessHandler() {
    super(MQManager.FACE_START_SUCCESS_TASK);
    this.mqManager = SpringTool.getBean(MQManager.class);
    this.delegateFaceAuthorizationService = SpringTool.getBean(DelegateFaceAuthorizationService.class);
    log.info("StartFaceSuccessHandler init mqManager :{} delegateFaceAuthorizationService : {}",mqManager, delegateFaceAuthorizationService);

  }

  @Override
  public void execute(String msg) {
    try {
      if (StringUtils.isBlank(msg)) {
        log.info("StartFaceSuccessMqHandle msg empty : {}", JsonUtils.obj2json(msg));
        return;
      }
      StartFaceSuccessTask faceSuccessTask = JsonUtils.json2pojo(msg, StartFaceSuccessTask.class);
      log.info("StartFaceSuccessMqHandle, msg :{}", msg);

      // 查询刷脸结果
      FaceAuthResInput queryRequest = new FaceAuthResInput();
      queryRequest.setFaceAuthId(faceSuccessTask.getFaceAuthId());
      queryRequest.setVersion(faceSuccessTask.getVersion());
      SupportResult<QueryFaceAuthResResult> faceAuthResult = delegateFaceAuthorizationService.queryFaceAuthResult(queryRequest);
      log.info("StartFaceSuccessMqHandle, faceAuthResult :{}", JSON.toJSONString(faceAuthResult));

      if (faceAuthResult.ifSuccess() && faceAuthResult.getData().isCompleted()){
        CompletedFaceTask completedFaceTask = new CompletedFaceTask();
        BeanUtils.copyProperties(faceAuthResult.getData(), completedFaceTask);
        completedFaceTask.setBizCode(faceSuccessTask.getBizCode());
        completedFaceTask.setBizId(faceSuccessTask.getBizId());
        mqManager.sendFaceCompleteTask(completedFaceTask);
      }
    } catch (Exception e) {
        log.error("topic face_start_success_task error, msg:{}, e:{}", msg, e);
    }
  }

}
