package com.timevale.faceauth.service.domain.provider.tencent;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.exception.ProviderException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/18 18
 */
@Slf4j
@Component
public class TencentCloudCertificationReturnInvocationHandler {

  private final Field[] resultTypes;

  public TencentCloudCertificationReturnInvocationHandler() {
    this.resultTypes = loadCertificationResultFields();
  }

  private Field[] loadCertificationResultFields() {
    Field[] fields = CertificationResult.class.getDeclaredFields();
    Arrays.stream(fields)
        .forEach(
            f -> {
              if (!f.isAccessible()) {
                f.setAccessible(true);
              }
            });
    return fields;
  }

  ProviderFaceAuthorizationResult invoke(String faceId, HttpServletRequest request, String providerName)
      throws FaceException {
    CertificationResult result = detectCertificationResult(request);
    TencentCloudFaceResultStatus resultStatus = deduceActualProviderCode(result);
    ProviderException error = null;
    if (result.ifFail()) {
      error = new ProviderException(resultStatus.getFaceCode(), resultStatus);
    }
    return TencentCloudFaceAuthorizationResult.createBuilder().setProvider(providerName)
        .setFaceId(faceId)
        .setCompleted(resultStatus.isCompleted())
        .setFee(resultStatus.isFee())
        .setLiveRate(deduceActualLiveRate(result))
        .setSimilarity(deduceActualSimilarity(result))
        .setContent(request.getQueryString())
        .setSuccess(result.ifSuccess())
        .setError(error)
        .build();
  }

  private CertificationResult detectCertificationResult(HttpServletRequest request)
      throws FaceException {
    CertificationResult result = (new CertificationResult());
    for (Field field : resultTypes) {
      try {
        field.set(result, request.getParameter(field.getName()));
      } catch (Exception cause) {
        throw FaceException.valueOf(
            FaceStatusCode.PROVIDER_ERROR_API_RESULT_SYNTAX,
            "Not found field[" + field.getName() + "] .",
            cause);
      }
    }
    return result;
  }

  private float deduceActualLiveRate(CertificationResult result) {
    String liveRate = result.liveRate;
    if (StringUtils.isEmpty(liveRate)) {
      return 0.00F;
    }

    try {
      return Float.valueOf(liveRate);
    } catch (Exception cause) {
      log.warn("Fail instance type of '" + float.class + "' on value[" + liveRate + "] .", cause);
      return 0.00F;
    }
  }

  private float deduceActualSimilarity(CertificationResult result) {
    String similarity = result.similarity;
    if (StringUtils.isEmpty(similarity)) {
      return 0.00F;
    }

    try {
      return Float.valueOf(similarity);
    } catch (Exception cause) {
      log.warn("Fail instance type of '" + float.class + "' on value[" + similarity + "] .", cause);
      return 0.00F;
    }
  }

  private TencentCloudFaceResultStatus deduceActualProviderCode(CertificationResult result) {
    String code = result.code;
    try {
      int c = Integer.parseInt(code);
      return TencentCloudFaceResultStatus.getStatusByCode(c);
    } catch (NumberFormatException e) {
    }
    return TencentCloudFaceResultStatus.SYSTEM_ERROR;
  }

  private static final class CertificationResult {

    private String code;
    private String orderNo;
    private String h5faceId;
    private String newSignature;
    private String liveRate;
    private String similarity;

    public boolean ifSuccess() {
      return (String.valueOf(TencentCloudFaceResultStatus.SUCCESS.getCode())).equals(code);
    }

    public boolean ifFail() {
      return !ifSuccess();
    }
  }
}
