package com.timevale.faceauth.service.utils;

import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDate;
import java.time.Period;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 证件处理工具
 *
 * <AUTHOR>
 * @since 2020/1/21 下午3:54
 */
@Slf4j
public class CardUtils {

    // 身份证长度
    public static final int ID_CARD_BASE_LENGTH_15 = 15;
    // 身份证长度
    public static final int ID_CARD_BASE_LENGTH = 18;

    private static final int SHOW_NUM_DEFAULT = 2;
    private static final String HIDE_ID_NO_MARK = "********";
    private static final String HIDE_NAME_MARK = "**";
  private static final String ID_CARD_NAME_REG =
      "^([\u2E80-\uFE4F+\\·?\u2E80-\uFE4F+]{1,100}$)";
    private static final Pattern ID_CARD_NAME_PAT = Pattern.compile(ID_CARD_NAME_REG);


    /**
     * 身份证号 正则验证规则包含18位以及15位身份证，包含规则： 18位的身份证正则： [1-9]\d{5} 前六位地区，非0打头 (18|19|([23]\d))\d{2}
     * 出身年份，覆盖范围为 1800-3999 年 ((0[1-9])|(10|11|12)) 月份，01-12月 (([0-2][1-9])|10|20|30|31) 日期，01-31天
     * \d{3}[0-9Xx]： 顺序码三位 + 一位校验码
     *
     * <p>15位的身份证： [1-9]\d{5} 前六位地区，非0打头 \d{2} 出生年份后两位00-99 ((0[1-9])|(10|11|12)) 月份，01-12月
     * (([0-2][1-9])|10|20|30|31) 日期，01-31天 \d{3} 顺序码三位，没有校验码
     */
    public static final String PARAMETER_IDCARD_DEFAULT_VALUE =
            "(^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}$)";


    // 身份证默认正则表达式
    private static final String ID_CARD_BASE = PARAMETER_IDCARD_DEFAULT_VALUE;

    private static final int ID_CARD_BASE_LENGTH_PRE = 17;
    // 校验码: 1 0 X 9 8 7 6 5 4 3 2
    private static final String[] ID_CARD_BASE_VALID_CODE = {
            "1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"
    };
    // 加权因子
    private static final int[] ID_CARD_BASE_WEIGHT = {
            7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2
    };
    private static final Pattern ID_CARD_PATTERN = Pattern.compile(ID_CARD_BASE);

    public static String formatName(String name){
        if(StringUtils.isBlank(name)){
            return name;
        }
        return name.replace(".","·");
    }

    public static boolean validName(String name){
        if(StringUtils.isBlank(name)){
           return false;
        }
        Matcher matcher = ID_CARD_NAME_PAT.matcher(name);
        boolean isMatch = matcher.matches();
        return isMatch;
    }


    // 核对身份证格式
    public static boolean checkIdCardFormat(String idCard) {
        return ID_CARD_PATTERN.matcher(idCard).matches() && checkIdentityCode(idCard);
    }



    /** 严格版本身份证号校验 （支持18位）+15位 */
    private static boolean checkIdentityCode(String identityCode) {
        if (StringUtils.isBlank(identityCode)) {
            return false;
        }

        if (!ID_CARD_PATTERN.matcher(identityCode).matches()) {
            return false;
        }
        if (identityCode.length() != ID_CARD_BASE_LENGTH) {
            return true;
        }

        // 校验第18位
        // S = Sum(Ai * Wi), i = 0, ... , 16 ，先对前17位数字的权求和
        // Ai:表示第i位置上的身份证号码数字值
        // Wi:表示第i位置上的加权因子
        // Wi: 7 9 10 5 8 4 2 1 6 3 7 9 10 5 8 4 2
        String[] tempA = identityCode.split("|");
        int[] a = new int[ID_CARD_BASE_LENGTH];
        for (int i = 0; i < ID_CARD_BASE_LENGTH_PRE; i++) {
            a[i] = Integer.parseInt(tempA[i]);
        }

        int sum = 0;
        for (int i = 0; i < ID_CARD_BASE_LENGTH_PRE; i++) {
            sum = sum + a[i] * ID_CARD_BASE_WEIGHT[i];
        }
        // Y = mod(S, 11)
        // 通过模得到对应的校验码
        // Y: 0 1 2 3 4 5 6 7 8 9 10
        int y = sum % 11;
        if (!ID_CARD_BASE_VALID_CODE[y].equalsIgnoreCase(identityCode.substring(17))) { // 第18位校验码错误
            return false;
        }
        return true;
    }

    /**
     * 隐藏数据
     *
     * @param text
     * @return
     */
    public static String hideName(String text) {
        if (StringUtils.isBlank(text)) {
            return text;
        }

        if (text.length() < 1) {
            return text;
        }

        String newText = HIDE_NAME_MARK+text.substring(text.length()-1, text.length()) ;
        return newText;
    }


    /**
     * 隐藏数据
     *
     * @param text
     * @return
     */
    public static String hideIdNo(String text) {
        return hideIdNo(text, SHOW_NUM_DEFAULT);
    }

    /**
     * 隐藏数据
     *
     * @param text    内容
     * @param showNum 显示位数
     * @return
     */
    public static String hideIdNo(String text, int showNum) {
        if (StringUtils.isBlank(text)) {
            return text;
        }

        //前后各保留 SHOW_NUM
        if (text.length() <= showNum * 2) {
            return text;
        }

        String newText = text.substring(0, 2) + HIDE_ID_NO_MARK + text.substring(text.length() - 2);
        return newText;
    }

    /**
     * 根据15位的身份证号码获得18位身份证号码
     * @param fifteenIDCard 15位的身份证号码
     * @return 升级后的18位身份证号码
     * @throws Exception 如果不是15位的身份证号码，则抛出异常
     */
    public static String getEighteenIDCard(String fifteenIDCard){
        if(fifteenIDCard == null || fifteenIDCard.length() != ID_CARD_BASE_LENGTH_15){
             return fifteenIDCard;
        }

        try {
            StringBuilder sb = new StringBuilder();
            sb.append(fifteenIDCard.substring(0, 6))
                    .append("19")
                    .append(fifteenIDCard.substring(6));
            sb.append(getVerifyCode(sb.toString()));
            return sb.toString();
        } catch (Exception e) {
            log.warn("parse idcard 15 to 18 fail , the idcard = "+ fifteenIDCard,e);
        }
        return fifteenIDCard;
    }

    /**
     * 获取校验码
     * @param idCardNumber 不带校验位的身份证号码（17位）
     * @return 校验码
     * @throws Exception 如果身份证没有加上19，则抛出异常
     */
    public static String getVerifyCode(String idCardNumber) throws Exception{
        if(idCardNumber == null || idCardNumber.length() < 17) {
            throw new Exception("不合法的身份证号码");
        }
        char[] Ai = idCardNumber.toCharArray();
        // Wi:表示第i位置上的加权因子
        // Wi: 7 9 10 5 8 4 2 1 6 3 7 9 10 5 8 4 2
        int[] Wi = ID_CARD_BASE_WEIGHT;
//        int[] Wi = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};

        int S = 0;
        int Y;
        for(int i = 0; i < Wi.length; i++){
            S += (Ai[i] - '0') * Wi[i];
        }
        Y = S % 11;
        return ID_CARD_BASE_VALID_CODE[Y];
    }

    public static Pair<Boolean, Integer> parserPersonAge(String certNo) {
        // 忽略异常数据
        if (StringUtils.isBlank(certNo)) {
            return Pair.of(false,-1);
        }

        if (certNo.length() != ID_CARD_BASE_LENGTH) {
            return Pair.of(false,-1);
        }

        try {
            int year = Integer.parseInt(certNo.substring(6, 10));
            int month = Integer.parseInt(certNo.substring(10, 12));
            int day = Integer.parseInt(certNo.substring(12, 14));
            LocalDate now = LocalDate.now();
            LocalDate birthday = LocalDate.of(year, month, day);
            Period p = Period.between(birthday, now);
            int age = p.getYears();
            return Pair.of(true,age);
        }catch (Exception e) {
            log.error("parse person age fail, certNo=" + certNo, e);
        }

        return Pair.of(false,-1);
    }

}
