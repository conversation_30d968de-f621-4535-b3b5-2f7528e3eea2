package com.timevale.faceauth.service.domain.provider.wechat.face;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tencentcloudapi.common.AbstractModel;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.faceid.v20180301.models.DetectAuthRequest;
import com.tencentcloudapi.faceid.v20180301.models.DetectAuthResponse;
import com.tencentcloudapi.faceid.v20180301.models.GetDetectInfoEnhancedRequest;
import com.tencentcloudapi.faceid.v20180301.models.GetDetectInfoRequest;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStartResult;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.core.support.FaceQueryableExtend;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.FaceAuthorizationPhotoResolver;
import com.timevale.faceauth.service.domain.FaceAuthorizationResourceResolver;
import com.timevale.faceauth.service.domain.processor.FaceRequestContext;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderApplyResultResolver;
import com.timevale.faceauth.service.domain.provider.ConfigurableProviderService;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationData;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.support.*;
import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudClientConf;
import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudFaceResultStatus;
import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudIdentityTypeEnum;
import com.timevale.faceauth.service.domain.repository.*;
import com.timevale.faceauth.service.domain.support.DnsResolver;
import com.timevale.faceauth.service.enums.FaceBizContextConstants;
import com.timevale.faceauth.service.enums.FaceInnerConstants;
import com.timevale.faceauth.service.exception.ProviderException;
import com.timevale.faceauth.service.liveness.provider.support.ProviderApiResponse;
import com.timevale.faceauth.service.liveness.provider.tencent.QCloudApiInvocationHandler;
import com.timevale.faceauth.service.result.FaceAuthResult;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import com.timevale.mandarin.weaver.utils.RequestContext;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.*;

/**
 * 微信小程序刷脸对接
 * 发起接口
 *    https://cloud.tencent.com/document/api/1007/31816
 * 查询接口
 *   https://cloud.tencent.com/document/api/1007/41957
 * <AUTHOR>
 * @since 2021-04-25 20:45
 */
@Slf4j
@Component
public class WeChatFaceService extends AbstractProviderService
    implements ConfigurableProviderApplyResultResolver {


  @Autowired private FaceResourcesInvocationHandler resourcesInvocationHandler;
  @Autowired private ConfigurableWeChatAuthorizationProperty weChatAuthorizationProperty;
  @Autowired private FaceRepository faceRepository;
  @Autowired private FaceAuthorizationPhotoResolver faceAuthorizationPhotoResolver;
  @Autowired private TencentCloudClientConf clientConf;

  private static final List<String> HISTORY_WECHAT_API_VERSION = Arrays.asList("1.0", "2.0", "3.0");

  @Autowired
  public WeChatFaceService(
      FaceRepository faceRepository,
      ProviderFaceRepository providerFaceRepository,
      DnsResolver dnsResolver,
      ConfigurableProperties configurableProperties,
      FaceSwitchRepository faceSwitchRepository) {
    super(
        faceRepository,
        providerFaceRepository,
        dnsResolver,
        configurableProperties,
        faceSwitchRepository);

  }

  @Override
  public FaceRequestContext refreshRequest(FaceRequestContext context) {
    if (context instanceof WeChatFaceAuthRequestContext) {
      return context;
    }
    // 尝试推断用户的认证照片数据，并构建新刷脸认证请求上下文
    UserPhoto userPhoto = faceAuthorizationPhotoResolver.resolvePhoto(context);
    boolean photoSwitch = weChatAuthorizationProperty.isWeChatWithPhotoSwitch();
    if (!photoSwitch){
      userPhoto = UserPhoto.createBuilder()
              .setPhotoType(context.getPhotoType())
              .setIdNo(context.getIdNo())
              .setName(context.getName())
              .build();
    }
    return (new WeChatFaceAuthRequestContext(context, userPhoto));
  }

  @SneakyThrows
  @Override
  protected AbstractProviderFaceAuthorizationResult resolveDoneAuthorizationResult(
      ProviderReturnInfo providerReturn,
      FaceQueryableExtend extend,
      FaceInfo faceInfo,
      ProviderFaceInfo providerFaceInfo)
      throws FaceException {

    String faceId = faceInfo.getFaceId();
    boolean completed = providerFaceInfo.isDone();
    boolean success = faceInfo.isOk();
    ProviderException error = null;
    if (completed && !success) {
      String errCode = providerFaceInfo.errCode;
      String errMsg = providerFaceInfo.errMsg;
      TencentCloudFaceResultStatus resultStatus = deduceActualProviderCode(faceId,errCode,errMsg);
      resultStatus.setDesc(errMsg);
      error = new ProviderException(resultStatus.getFaceCode(), resultStatus);
    }

    ExecutorService executor = Executors.newCachedThreadPool();

    Future<FaceOSSResources> submit = executor.submit(new Callable<FaceOSSResources>() {
      @Override
      public FaceOSSResources call() throws Exception {
        return getFaceResources(faceInfo.getFaceId());
      }
    });

    FaceOSSResources faceOSSResources = submit.get();

    return WeChatFaceAuthorizationResult.createBuilder()
        .setCompleted(completed)
        .setSuccess(success)
        .setFaceId(faceInfo.getFaceId())
        .setPhoto(faceOSSResources.getPhoto())
        .setPhotoType(faceOSSResources.getPhotoType())
        .setIdCardFront(faceOSSResources.getIdCardFront())
        .setIdCardBack(faceOSSResources.getIdCardBack())
        .setError(error)
        .setSimilarity(getSimilarityByProvideReturn(providerReturn))
        .build();
  }

  @Override
  protected ProviderFaceAuthorizationResult doQueryAuthorizeResult(
      FaceQueryableExtend extend, FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo)
      throws FaceException {
    if(this.checkTasKExpired(faceInfo)){
      throw FaceException.valueOf(FaceStatusCode.FACE_TASK_EXPIRED);
    }
    String faceId = faceInfo.getFaceId();
    String orderNo = providerFaceInfo.getOrderNo();

    String ruleId;
    Boolean withPhoto = StringUtils.isNotBlank(faceInfo.getPhoto());
    String providerApiVersion = faceInfo.getProviderApiVersion();
    // 平滑发布，发布完成后开启
    if (HISTORY_WECHAT_API_VERSION.contains(providerApiVersion)) {
      WeChatApiVersion apiVersion = WeChatApiVersion.getVersion(faceInfo.getProviderApiVersion(),
          withPhoto);
      ruleId = apiVersion.getRuleId();
    } else if (StringUtils.isNotBlank(providerApiVersion)) {
      ruleId = providerApiVersion;
    } else {
      ruleId = WeChatApiVersion.getDefault(withPhoto).getRuleId();
    }

    QcloudV1IdentityAccessHolder accessHolder = new QcloudV1IdentityAccessHolder();
    Credential cred =
        new Credential(
            accessHolder.secretId(), accessHolder.secretKey());

    QCloudApiInvocationHandler.InnerQCloudClient client =
        (new QCloudApiInvocationHandler.InnerQCloudClient(cred, WeChatApiConstant.API_REGION));
    client.setClientProfile(clientConf.getClientProfile());
    AbstractModel requestModel = resolveGetDetectInfoRequestModel(orderNo, ruleId);

    RequestContext.put(FaceInnerConstants.FLOW_FACE_ID, faceInfo.getFaceId());
    long startTime = System.currentTimeMillis();
    String response = client.execute(requestModel, "GetDetectInfoEnhanced");
    long endTime = System.currentTimeMillis();
    log.info("wechat face query faceId={},result={}, cost:{} ms", faceInfo.getFaceId(), response,
        (endTime - startTime));
    JSONObject queryJson = JSON.parseObject(response).getJSONObject("Response");
    JSONObject textJson = queryJson.getJSONObject("Text");
    String errCode = textJson.getString("ErrCode");
    String errMsg = textJson.getString("ErrMsg");
    TencentCloudFaceResultStatus resultStatus = deduceActualProviderCode(faceId, errCode, errMsg);
    boolean completed = resultStatus.isCompleted();
    boolean success = WeChatApiResult.SUCCESS_CODE.equals(errCode);
    String photoBase64 = this.revokerFacePhoto(queryJson);
    String idCardFrontBase64 = this.revokerIdCardFront(queryJson);
    String idCardBackBase64 = this.revokerIdCardBack(queryJson);
    ProviderException error = null;
//    if (completed && !success) {
      resultStatus.setDesc(errMsg);
      error = new ProviderException(resultStatus.getFaceCode(), resultStatus);
//    }

    String photo = null;
    String photoType = null;
    String idCardFront = null;
    String idCardBack = null;
    if (success) {
      FaceOSSResources ossResources =
          resourcesInvocationHandler.persistentFaceResources(
              new FaceBase64Resources(photoBase64, null, idCardFrontBase64, idCardBackBase64), faceInfo);
      resourcesInvocationHandler.saveUserPhoto(faceInfo, ossResources);
      photo = ossResources.getPhoto();
      photoType = ossResources.getPhotoType();
      idCardFront = ossResources.getIdCardFront();
      idCardBack = ossResources.getIdCardBack();
    }

    return WeChatFaceAuthorizationResult.createBuilder()
        .setCompleted(completed)
        .setSuccess(success)
        .setFaceId(faceInfo.getFaceId())
        .setError(error)
        .setPhoto(photo)
        .setIdCardFront(idCardFront)
        .setIdCardBack(idCardBack)
        .setPhotoType(photoType)
        .setContent(response)
        .setSimilarity(getSimilarityByProvideReturn(response))
        .build();
  }

  /**
   * 微信小程序 主动查询 扫脸结果中提取 本次流程活体一比一的分数
   */
  private float getSimilarityByProvideReturn(ProviderReturnInfo providerReturn) {
    if (providerReturn == null) {
      return 0.0f;
    }
    return getSimilarityByProvideReturn(providerReturn.getData());
  }

  /**
   * 微信小程序 主动查询 扫脸结果中提取 本次流程活体一比一的分数
   *
   * @param response 微信小程序主动查询扫脸结果
   * @return 本次流程活体一比一的分数
   */
  private float getSimilarityByProvideReturn(String response) {
    return Optional.ofNullable(response)
        .filter(StringUtils::isNotBlank)
        .map(JSON::parseObject)
        .map(jsonObject -> jsonObject.getJSONObject("Response"))
        .map(responseJson -> responseJson.getJSONObject("Text"))
        .map(textJson -> textJson.getString("Sim"))
        .filter(StringUtils::isNotBlank)
        .flatMap(this::safeFloatParse)
        .orElse(0.0f);
  }

  /**
   * 安全地尝试将字符串转换为float，如果转换失败则返回一个空的Optional。
   */
  private Optional<Float> safeFloatParse(String value) {
    try {
      return Optional.of(Float.parseFloat(value));
    } catch (NumberFormatException e) {
      return Optional.empty();
    }
  }

  /**
   * 获取微信小程序人脸核身 最佳帧 照片
   * @param queryJson 人脸核身数据
   * @return 最佳帧 照片base64
   */
  private String revokerFacePhoto(JSONObject queryJson) {
    JSONObject bestFrames = queryJson.getJSONObject("BestFrame");
    if (bestFrames == null) {
      return null;
    }
    return bestFrames.getString("BestFrame");
  }

  /**
   * 获取微信小程序人脸核身 身份证照片（正面）
   * @param queryJson 人脸核身数据
   * @return 身份证照片（正面）base64
   */
  private String revokerIdCardFront(JSONObject queryJson) {
    JSONObject IdCardData = queryJson.getJSONObject("IdCardData");
    if (Objects.isNull(IdCardData)) {
      return null;
    }
    return IdCardData.getString("ProcessedFrontImage");
  }

  /**
   * 获取微信小程序人脸核身 身份证照片（反面）
   * @param queryJson 人脸核身数据
   * @return 身份证照片（反面）base64
   */
  private String revokerIdCardBack(JSONObject queryJson) {
    JSONObject IdCardData = queryJson.getJSONObject("IdCardData");
    if (Objects.isNull(IdCardData)) {
      return null;
    }
    return IdCardData.getString("ProcessedBackImage");
  }

  private TencentCloudFaceResultStatus deduceActualProviderCode(String faceId, String errCode, String errMsg) {
    if (StringUtils.isBlank(errCode)) {
      return TencentCloudFaceResultStatus.OTHER_66660011;
    }

    if(weChatAuthorizationProperty.getWeChatNotCompletedQueryErrCode().contains(errCode)){
      log.warn("wechat face find not completed query errCode:faceId={},errCode={},errMsg={}", faceId, errCode, errMsg);
      return TencentCloudFaceResultStatus.OTHER_66660011;
    }

    try {
      int c = Integer.parseInt(errCode);
      return TencentCloudFaceResultStatus.getStatusByCode(c);
    } catch (NumberFormatException e) {
    }
    return TencentCloudFaceResultStatus.SYSTEM_ERROR;
  }

  @Override
  protected ProviderFaceAuthorizationResult detectFaceAuthorizationResultOnReturn(
      String faceId, HttpServletRequest request) throws FaceException {
    throw FaceException.valueOf(FaceStatusCode.SERVICE_UNSUPPORTED, "Unsupported api");
  }

  @Override
  protected ProviderFaceAuthorizationResult detectFaceAuthorizationResultOnCallback(
      String faceId, byte[] dataBuffer, HttpServletRequest request) throws FaceException {
    throw FaceException.valueOf(FaceStatusCode.SERVICE_UNSUPPORTED, "Unsupported api");
  }

  @Override
  protected ProviderFaceAuthorizationData doInitialize(
      FaceAuthorizationInitializingContext initializingContext) throws FaceException {

    String appId = initializingContext.getRequest().getAppId();
    String faceCertType = initializingContext.getRequest().getIdType();
    String idType = TencentCloudIdentityTypeEnum.codeOfIdType(faceCertType);
    String bizCode = initializingContext.getRequestContext().getBizCode();
    boolean userPhoto = StringUtils.isNotBlank(initializingContext.getPhotoKey());
    DetectAuthInput authInput = new DetectAuthInput();
    authInput.setRuleId(weChatAuthorizationProperty.getWeChatApiRuleId(appId, userPhoto, bizCode));
    authInput.setIdNo(initializingContext.getRequest().getIdNo());
    authInput.setIdType(idType);
    authInput.setName(initializingContext.getRequest().getName());
    if (userPhoto) {
      authInput.setImageBase64(
          faceAuthorizationPhotoResolver.resolvePhotoData(initializingContext.getPhotoKey()));
    }
    QcloudV1IdentityAccessHolder accessHolder = new QcloudV1IdentityAccessHolder();
    Credential cred =
        new Credential(
            accessHolder.secretId(), accessHolder.secretKey());
    QCloudApiInvocationHandler.InnerQCloudClient client =
        (new QCloudApiInvocationHandler.InnerQCloudClient(cred, WeChatApiConstant.API_REGION));
    client.setClientProfile(clientConf.getClientProfile());

    long startTime = System.currentTimeMillis();
    AbstractModel requestModel = resolveDetectAuthRequestModel(authInput);
    RequestContext.put(FaceInnerConstants.FLOW_FACE_ID, initializingContext.getFaceId());
    String response = client.execute(requestModel, "DetectAuth");
    long endTime = System.currentTimeMillis();
    log.info(
        "wechat face init faceId={},result={}, ruleId={}, cost:{} ms",
        initializingContext.getFaceId(),
        response,
        authInput.getRuleId(),
        (endTime - startTime));
    ProviderApiResponse createResponse =
        ProviderApiResponse.createBuilder()
            .setDataResolver(client)
            .setOk(true)
            .setContent(response)
            .build();
    String createJson = JSON.parseObject(createResponse.getContent()).getString("Response");
    DetectAuthResponse authResponse = JSON.parseObject(createJson, DetectAuthResponse.class);

    // 平滑发布，发布完成后开启
    if (weChatAuthorizationProperty.faceWechatVersionSwitchEnabled) {
      // ruleId
      faceRepository.faceApiVersion(initializingContext.getFaceId(), authInput.getRuleId());
    } else {
      // version
      faceRepository.faceApiVersion(initializingContext.getFaceId(),
          weChatAuthorizationProperty.getWeChatApiVersion(appId, userPhoto));
    }
    // authInput 数据将被落库，此时，authInput 上下文中的照片数据是以 base64 形式存在的，这个数据的长度不可控，可能导致长度超出 DB 的长度限制，
    // 为了解决这个问题，将照片保存的 key 设置为当前值，后续根据当前的 key 从数据仓库中获得实际的照片的 base64 数据。
    authInput.setImageBase64(initializingContext.getPhotoKey());
    return ProviderFaceAuthorizationData.createBuilder()
        .setProvider(getProviderName())
//        .setProviderVersion(apiVersion.getVersion())
        .setProviderName(getFullName())
        .setData(authResponse.getUrl())
        .setOrderNo(authResponse.getBizToken())
        .setExpireMinutes(weChatAuthorizationProperty.getWeChatTaskExpire())
        .setAuthorizeInput(resolveRequestContent(authInput, authResponse))
        .build();
  }

  @Override
  public String getProviderName() {
    return ConfigurableProviderService.PROVIDER_WE_CHAT_FACE;
  }

  @Override
  public String getFullName() {
    return ConfigurableProviderService.FULL_NAME_PROVIDER_WE_CHAT_FACE;
  }

  @Override
  public FaceAuthResult resolveApplyResult(String clientType, FaceStartResult result) {
    FaceAuthResult faceAuthResult = (new FaceAuthResult());
    faceAuthResult.setFaceH5Url(result.getData());
    faceAuthResult.setFaceValue(result.getOrderNo());
    faceAuthResult.setFaceAuthId(result.getFaceId());
    faceAuthResult.setExpiredTimestamp(result.getExpiredTimestamp());
    return faceAuthResult;
  }

  protected static AbstractModel resolveDetectAuthRequestModel(DetectAuthInput authInput) {
    DetectAuthExtendRequest authRequest = (new DetectAuthExtendRequest());

    authRequest.setRuleId(authInput.getRuleId());
    authRequest.setIdCard(authInput.getIdNo());
    authRequest.setIdType(authInput.getIdType());
    authRequest.setName(authInput.getName());
    authRequest.setRedirectUrl(authInput.getRedirectUrl());
    authRequest.setImageBase64(authInput.getImageBase64());
    return authRequest;
  }

  protected static AbstractModel resolveGetDetectInfoRequestModel(
      String BizToken, String ruleId) {
    GetDetectInfoEnhancedRequest infoRequest = (new GetDetectInfoEnhancedRequest());
    infoRequest.setRuleId(ruleId);
    infoRequest.setInfoType("0");
    infoRequest.setBizToken(BizToken);
    infoRequest.setIsCutIdCardImage(Boolean.TRUE);

    return infoRequest;
  }

  private String resolveRequestContent(DetectAuthInput authInput, DetectAuthResponse authResponse) {

    try {
      return JsonUtils.obj2json(authInput);
    } catch (Exception cause) {
      try {
        log.warn("Fail to serialization on request .", cause);
      } catch (Exception ignore) {
        // do nothing ...
      }
      return authResponse.getBizToken();
    }
  }

  private static final class WeChatFaceAuthRequestContext implements FaceRequestContext {

    private final String photo;
    private final String photoType;
    private final FaceRequestContext request;

    private WeChatFaceAuthRequestContext(FaceRequestContext request, UserPhoto userPhoto) {
      this.request = request;
      this.photo = userPhoto.getPhoto();
      this.photoType = userPhoto.getPhotoType();
    }

    @Override
    public Map<String, String> bizContext() {
      return request.bizContext();
    }

    @Override
    public String getAppId() {
      return request.getAppId();
    }

    @Override
    public String getOid() {
      return request.getOid();
    }

    @Override
    public String getBizId() {
      return request.getBizId();
    }

    @Override
    public String getBizScene() {
      return request.getBizScene();
    }

    @Override
    public String getBizCode() {
      return request.getBizCode();
    }

    @Override
    public String getClientType() {
      return request.getClientType();
    }

    @Override
    public String getName() {
      return request.getName();
    }

    @Override
    public String getIdNo() {
      return request.getIdNo();
    }

    @Override
    public String getIdType() {
      return request.getIdType();
    }

    @Override
    public String getProvider() {
      return request.getProvider();
    }

    @Override
    public String getPhoto() {
      return photo;
    }

    @Override
    public String getPhotoType() {
      return photoType;
    }

    @Override
    public String getReturnUrl() {
      return request.getReturnUrl();
    }

    @Override
    public String getCallbackUrl() {
      return request.getCallbackUrl();
    }

    @Override
    public long getTimestamp() {
      return request.getTimestamp();
    }

    @Override
    public String getInput() {
      return request.getInput();
    }
  }

}
