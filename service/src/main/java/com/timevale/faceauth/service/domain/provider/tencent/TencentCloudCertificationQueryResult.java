package com.timevale.faceauth.service.domain.provider.tencent;

import lombok.Data;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/21 11
 */
@Data
public class TencentCloudCertificationQueryResult {

  // 业务序号
  private String bizSeqNo;
  // 订单
  private String orderNo;
//  // 证件号码
//  private String idNo;
//  // 姓名
//  private String name;
//  // 证件类型
//  private String idType;
  // 视频地址
  private String video;
  // 相片地址
  private String photo;
  // 活体比率
  private float liveRate;
  // 相似度
  private float similarity;
  // 交易时间
  private long transactionTime;
  // 发生时间,供应商是格式=20231124150546 的字符串，但是历史代码已经转为数字，只能兼容转换
  private long occurredTime;
  // 成功状态
  private boolean success;
  //Trtc 渠道刷脸则标识"Y"
  private String trtcFlag;

  private int code;
  private String msg;


  public boolean faceOk() {
    return code == TencentCloudCertificationStatusResponse.CODE_SUCCESS;
  }
}
