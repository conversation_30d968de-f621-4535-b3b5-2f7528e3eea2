package com.timevale.faceauth.service.domain.provider.tencent;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.processor.ClientApplicationType;
import com.timevale.faceauth.service.domain.processor.ConfigurableFaceProcessor;
import com.timevale.faceauth.service.domain.processor.ConfigurableFaceProcessors;
import com.timevale.faceauth.service.domain.provider.support.FaceAuthorizationInitializingContext;
import com.timevale.faceauth.service.domain.provider.support.RestTemplateRequestResolver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/18 18
 */
@Component
public class TencentCloudCertificationInitializationBeginningInvocationHandler
    implements TencentCloudCertificationSignatureResolver<
        TencentCloudCertificationInitializationBeginningRequest> {

  private static final Map<ClientApplicationType, String> CLIENT_APPLICATION_TYPE_STRING_MAPPER;

  static {
    CLIENT_APPLICATION_TYPE_STRING_MAPPER = (new HashMap<>(4));
    CLIENT_APPLICATION_TYPE_STRING_MAPPER.put(
        ClientApplicationType.TYPE_BROWSER,
        TencentCloudCertificationInitializationBeginningRequest.TYPE_FROM_BROWSER);
    CLIENT_APPLICATION_TYPE_STRING_MAPPER.put(
        ClientApplicationType.TYPE_APP,
        TencentCloudCertificationInitializationBeginningRequest.TYPE_FROM_APP);
  }

  private final ConfigurableProperties properties;
  private final RestTemplateRequestResolver requestResolver;

  @Autowired
  public TencentCloudCertificationInitializationBeginningInvocationHandler(
      ConfigurableProperties properties, RestTemplateRequestResolver requestResolver) {
    this.properties = properties;
    this.requestResolver = requestResolver;
  }

  @Override
  public String resolveSignature(TencentCloudCertificationInitializationBeginningRequest request)
      throws FaceException {
    return TencentCloudUtil.signature(
        request.getAppId(),
        request.getOrderNo(),
        request.getUserId(),
        request.getVersion(),
        request.getFaceId(),
        request.obtainTicket());
  }

  String invoke(
      TencentCloudCertificationInitializationUploadResult uploadResult,
      FaceAuthorizationInitializingContext initializingContext,
      TencentWebAppIdVersion appIdVersion,
      String webThemeColor)
      throws FaceException {
    String bizAppId = initializingContext.getRequest().getAppId();
    TencentCloudCertificationInitializationBeginningRequest request =
        TencentCloudCertificationInitializationBeginningRequest.createBuilder()
            .setFaceId(uploadResult.getH5faceId())
            .setUserId(initializingContext.getFaceId())
            .setTheme(webThemeColor)
            .setOrderNo(uploadResult.getOrderNo())
            .setSignatureResolver(this)
            .setAppId(appIdVersion.getAccessHolder().getWebAppId())
            .setTicket(
                TencentCloudUtil.getNonceTicket(initializingContext.getFaceId(), appIdVersion))
            .build();
    request.signatureRequest();
    String queryString = requestResolver.resolveQueryString(request);
    String uriStr = properties.getTencentCloudCertificationBeginApi(bizAppId) + "?" + queryString;
    // 此处做校验
    requestResolver.resolveURI(uriStr);
    return uriStr;
  }

//  public static void main(String[] args) {
//    RestTemplateRequestResolver requestResolver  = new RestTemplateRequestResolver();
//    TencentCloudCertificationInitializationBeginningRequest request =
//            TencentCloudCertificationInitializationBeginningRequest.createBuilder()
//                    .setFrom("sss")
//                    .setH5faceId("sdsdsdsd")
//                    .setTheme("0")
//                    .setOrderNo("null")
//                    .build();
//    String q = requestResolver.resolveQueryString(request);
//    System.out.println(q);
//  }

  private String getFrom(FaceAuthorizationInitializingContext initializingContext)
      throws FaceException {
    ConfigurableFaceProcessor processor =
        ConfigurableFaceProcessors.getProcessor(initializingContext.getRequest().getClientType());
    ClientApplicationType clientApplicationType = processor.getClientApplicationType();
    String from = CLIENT_APPLICATION_TYPE_STRING_MAPPER.get(clientApplicationType);
    if (null == from) {
      from = CLIENT_APPLICATION_TYPE_STRING_MAPPER.get(ClientApplicationType.TYPE_BROWSER);
    }
    return from;
  }

  private String getUrl(FaceAuthorizationInitializingContext initializingContext)
      throws FaceException {
    try {
      return URLEncoder.encode(initializingContext.getReturnUrl(), StandardCharsets.UTF_8.name());
    } catch (Exception cause) {
      throw FaceException.valueOf(FaceStatusCode.RESOURCE_ERROR_ENCODE_URL, cause);
    }
  }
}
