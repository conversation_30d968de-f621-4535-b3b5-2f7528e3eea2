package com.timevale.faceauth.service.domain.provider.tencent;

import com.timevale.faceauth.service.component.OpenPlatformClient;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.FaceAuthorizationPhotoResolver;
import com.timevale.faceauth.service.domain.provider.support.FaceAuthorizationInitializingContext;
import com.timevale.faceauth.service.domain.provider.support.RestTemplateRequestResolver;
import com.timevale.faceauth.service.enums.FaceAuthModeEnum;
import com.timevale.faceauth.service.thirdparty.audiovideodual.utils.AlipayUtils;
import com.timevale.faceauth.service.utils.UUIDUtil;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogService;
import com.timevale.faceauth.service.utils.providerLog.result.TencentCloudInitResult;
import com.timevale.mandarin.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.List;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/18 18
 */
@Component
@Slf4j
public class TencentCloudCertificationInitializationUploadInvocationHandler
    extends TencentCloudFaceInvocationErrorHandler
    implements TencentCloudCertificationSignatureResolver<
        TencentCloudCertificationInitializationUploadRequest> {

  private final ConfigurableProperties properties;
  private final FaceAuthorizationPhotoResolver faceAuthorizationPhotoResolver;
  private final RestTemplateRequestResolver requestResolver;

  @Autowired private ProviderLogService providerLogService;
  @Autowired private OpenPlatformClient openPlatformClient;

  @Autowired
  public TencentCloudCertificationInitializationUploadInvocationHandler(
      ConfigurableProperties properties,
      FaceAuthorizationPhotoResolver faceAuthorizationPhotoResolver,
      RestTemplateRequestResolver requestResolver) {
    this.properties = properties;
    this.faceAuthorizationPhotoResolver = faceAuthorizationPhotoResolver;
    this.requestResolver = requestResolver;
  }

  @Override
  public String resolveSignature(TencentCloudCertificationInitializationUploadRequest request)
      throws FaceException {
    return TencentCloudUtil.signature(
        request.getAppId(),
        request.getOrderNo(),
        request.getName(),
        request.getIdNo(),
        request.getUserId(),
        request.getVersion(),
        request.getTicket());
  }

 public TencentCloudCertificationInitializationUploadResultWrap invoke(
      FaceAuthorizationInitializingContext initializingContext, TencentWebAppIdVersion appIdVersion)
      throws FaceException {
    TencentCloudCertificationInitializationUploadRequest request =
        prepareRequest(initializingContext, appIdVersion);
    URI uri = requestResolver.resolveURI(appIdVersion.getAccessHolder().getTencentCloudCertificationIdAscIframeApi(initializingContext.getFaceId()));
    RequestEntity<TencentCloudCertificationInitializationUploadRequest> requestRequestEntity =
        (new RequestEntity<>(request, HttpMethod.POST, uri));
    TencentCloudInitResult tencentCloudInitResult = new TencentCloudInitResult(initializingContext.getFaceId(), 0);
    ResponseEntity<String> responseEntity =
        requestResolver.resolveResponse(
            initializingContext.getFaceId(),
            appIdVersion.getProviderName(),
            requestRequestEntity,
            String.class,
            tencentCloudInitResult,
            (x, y, z) -> providerLogService.logTencentCloudInit((TencentCloudInitResult) x, y, z),
            (x, y, z) -> providerLogService.logTencentCloudInitWithException(
                (TencentCloudInitResult) x, y, z)
        );
    TencentCloudCertificationInitializationUploadResponse response;
    try {
      response =
          JsonUtils.json2pojo(
              responseEntity.getBody(),
              TencentCloudCertificationInitializationUploadResponse.class);
    } catch (Exception cause) {
      log.error(
          "Data["
              + responseEntity.getBody()
              + "] could not instantiate type["
              + TencentCloudCertificationInitializationUploadResponse.class
              + "] .",
          cause);
      throw FaceException.valueOf(FaceStatusCode.PROVIDER_ERROR_API_RESULT_SYNTAX);
    }
    this.handlerError(response);

    TencentCloudCertificationInitializationUploadResult result = response.getResultIfAbsent();
    String inputStr;
    try {
      // inputStr 数据将被落库，此时，request 上下文中的照片数据是以 base64 形式存在的，这个数据的长度不可控，可能导致长度超出 DB 的长度限制，
      // 为了解决这个问题，将照片保存的 key 设置为当前值，后续根据当前的 key 从数据仓库中获得实际的照片的 base64 数据。
      request.setSourcePhotoStr(initializingContext.getPhotoKey());
      inputStr = JsonUtils.obj2json(request);
    } catch (Exception cause) {
      log.error("Error serialize request .", cause);
      throw FaceException.valueOf(FaceStatusCode.PROVIDER_ERROR_API_RESULT_SYNTAX);
    }
    return (new TencentCloudCertificationInitializationUploadResultWrap(inputStr, result));
  }

  private TencentCloudCertificationInitializationUploadRequest prepareRequest(
      FaceAuthorizationInitializingContext initializingContext, TencentWebAppIdVersion appIdVersion)
      throws FaceException {
    String faceCertType = initializingContext.getRequest().getIdType();
    String idType = TencentCloudIdentityTypeEnum.codeOfIdType(faceCertType);

    // 计算该appId是否能自动降级,默认可以
    boolean autoDegradation = true;
    List<Integer> h5DisableAutoDegradation = openPlatformClient.getDisableRecordType(initializingContext.getRequest().getAppId());
    if (h5DisableAutoDegradation.contains(FaceAuthModeEnum.TECENT_CLOUD_H5.getMode())) {
      log.info("appId:{} face_tecent_cloud_h5 disable auto degradation", initializingContext.getRequest().getAppId());
      autoDegradation = false;
    }

    TencentCloudCertificationInitializationUploadRequest request =
        TencentCloudCertificationInitializationUploadRequest.createBuilder()
            .setIdNo(initializingContext.getRequest().getIdNo())
            .setIdType(idType)
            .setName(initializingContext.getRequest().getName())
            .setSourcePhotoStr(
                faceAuthorizationPhotoResolver.resolvePhotoData(initializingContext.getPhotoKey()))
            .setSourcePhotoType(
                TencentCloudCertificationInitializationUploadRequest.PHOTO_TYPE_HIGH_DEFINITION)
            .setLiveInterType(autoDegradation ? null : "1")
            .setUserId(initializingContext.getFaceId())
            .setOrderNo(initializingContext.getFaceId())
            .setSignatureResolver(this)
            .setTicket(
                TencentCloudUtil.getTicketCache(initializingContext.getFaceId(), appIdVersion))
            .setAppId(appIdVersion.getAccessHolder().getWebAppId())
            .setUrl(AlipayUtils.urlEncode(initializingContext.getReturnUrl()))
            .setNonce(UUIDUtil.generateUUID())
            .build();
    request.signatureRequest();
    return request;
  }
}
