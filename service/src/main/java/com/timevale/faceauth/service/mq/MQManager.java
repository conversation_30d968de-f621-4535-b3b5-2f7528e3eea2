package com.timevale.faceauth.service.mq;

import com.alibaba.fastjson.JSON;
import com.timevale.component.identity.sensors.SensorsData;
import com.timevale.faceauth.service.apispace.service.com.timevale.apispace.request.ProviderMetric;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.resource.ResourceSystemHolder;
import com.timevale.faceauth.service.domain.resource.task.ResourceDownloadMsg;
import com.timevale.faceauth.service.domain.resource.task.ResourceDownloadTaskHandler;
import com.timevale.faceauth.service.inner.impl.control.FaceUserResourceControlConsumer;
import com.timevale.framework.mq.client.Group;
import com.timevale.framework.mq.client.Topic;
import com.timevale.framework.mq.client.all.MixMQBootstrap;
import com.timevale.framework.mq.client.all.config.MixMQConfig;
import com.timevale.framework.mq.client.all.restart.RestartConsumerCreateInstanceHook;
import com.timevale.framework.mq.client.all.restart.RestartProducerCreateInstanceHook;
import com.timevale.framework.mq.client.consumer.Consumer;
import com.timevale.framework.mq.client.consumer.ConsumerFactory;
import com.timevale.framework.mq.client.consumer.Listener;
import com.timevale.framework.mq.client.producer.DelayMsgLevel;
import com.timevale.framework.mq.client.producer.Msg;
import com.timevale.framework.mq.client.producer.OfferResult;
import com.timevale.framework.mq.client.producer.Producer;
import com.timevale.framework.mq.client.producer.ProducerFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @since 2019/8/12 下午8:59
 */
@Slf4j
@Component
public class MQManager implements ApplicationListener<ContextRefreshedEvent> {

  private static final String TOPIC_TAG = "face_auth";

  private static final String LOG_GROUP = "identity";
  private static final String LOG_TOPIC = "identity_operation_record";

  private static final String GROUP_ = "face_auth_";
  /** 刷脸发起成功 */
  public static final String FACE_START_SUCCESS_TASK = "face_start_success_task";
  /** 刷脸完成 */
  private final String FACE_COMPLETED_TASK = "face_completed_task";

  /** 刷脸供应商调用记录 */
  private final String TOPIC_PROVIDER_CALL = "provider_call";


  /** 刷脸发起成功 */
  public static final String TOPIC_FACE_RESOURCE_CONTROL = "topic_face_resource_control";

  @Autowired private AutowireCapableBeanFactory beanFactory;
  @Autowired private DefaultListableBeanFactory defaultListableBeanFactory;
  @Autowired private ConfigurableProperties configurableProperties;

  private MixMQConfig config = MixMQConfig.getLatestConfigFromPuppeteer("application");

  private MixMQBootstrap mixMQBootstrap = new MixMQBootstrap();

  private ProducerFactory producerFactory;
  private ConsumerFactory consumerFactory;
  private Map<String, Producer> producerMap = new ConcurrentHashMap<>();
  private Map<String, Consumer> consumerMap = new ConcurrentHashMap<>();

  @PostConstruct
  public void init() {
    initProducerFactory();
//    initConsumerFactory();

  }

  @PreDestroy
  public void destroy() {
    for (Producer producer : producerMap.values()) {
      try {
        producer.shutdown();
      } catch (Exception e) {
        log.error("producer shutdown error ", e);
      }
    }
    for (Consumer consumer : consumerMap.values()) {
      try {
        consumer.shutdown();
      } catch (Exception e) {
        log.error("producer shutdown error ", e);
      }
    }
  }

  @Override
  public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
    try{
      getProducer(LOG_GROUP, LOG_TOPIC);
    }
    catch (Exception cause){
      log.warn("Fail initialize mq producers .");
    }
  }

  private Producer getProducer(String groupName, String topicName) throws Exception {
    String key = groupName + ":" + topicName;
    Producer producer = producerMap.get(key);
    if (producer == null) {
      producer = producerFactory.createProducer(new Topic(topicName), new Group(groupName));
      producer.start();
      producerMap.put(key, producer);
    }
    return producer;
  }

  private Consumer startConsumer(String groupName, String topicName, Listener listener)
          throws Exception {
    String key = groupName + ":" + topicName;
    Consumer consumer = consumerMap.get(key);
    if (consumer == null) {
      // 注意，一个 group 只能订阅一个 topic，且 tag 一致，也就是说：每个 group 里的所有实例所订阅的内容必须是一致的。
      consumer = consumerFactory.createConsumer(new Group(groupName));
      consumer.subscribe(new Topic(topicName));
      consumer.registerListener(listener);
      consumer.start();
      consumerMap.put(key, consumer);
    }
    return consumer;
  }


  private void send(String groupName, String topicName, String tag, String msg) {
    // 模拟发送消息.
    try {
      OfferResult hello =
          getProducer(groupName, topicName).offer(new Msg(topicName, tag, msg.getBytes()));
      log.info(
          "send groupName = {}, topicName = {}, msgId = {} , msg = {}",
          groupName,
          topicName,
          hello.getMsgId(),
          msg);
    } catch (Exception e) {
      log.error("topicName："+ topicName+",发送消息队列异常,消息的内容为:"+msg, e);
    }
  }

  private void send(String groupName, String topicName, String tag, String json, DelayMsgLevel delayMsgLevel) {
    try {
      Msg msg = new Msg(topicName, tag, json.getBytes());
      msg.setDelayMsgLevel(delayMsgLevel);
      OfferResult hello =
              getProducer(groupName, topicName).offer(msg);
      log.info(
              "send groupName={},topicName={},msgId={}, msg={}",
              groupName,
              topicName,
              hello.getMsgId(),
              json);
    } catch (Exception e) {
      log.error("tag：{},发送消息队列异常,topicName={},消息的内容为:{}", tag, topicName, json);
    }
  }


  public void sendFaceStartSuccessTask(StartFaceSuccessTask startFaceSuccessTask) {
    DelayMsgLevel delayMsgLevel = DelayMsgLevel.getPossibleLevel(configurableProperties.getStartFaceSucMqDelayMsgLevel() * 1000);

    String json = JSON.toJSONString(startFaceSuccessTask);
    String groupName = GROUP_ + FACE_START_SUCCESS_TASK;
    String topicName = FACE_START_SUCCESS_TASK;
    log.info(
            "send mq init groupName={},topicName={},DelayMsgLevel={},msg={}",
            groupName,
            topicName,
            delayMsgLevel.getDesc(),
            json);

    this.send(groupName, topicName, startFaceSuccessTask.getBizCode(), JSON.toJSONString(startFaceSuccessTask), delayMsgLevel);
  }

  public void sendFaceCompleteTask(CompletedFaceTask completedFaceTask) {
    String json = JSON.toJSONString(completedFaceTask);
    String groupName = GROUP_ + FACE_COMPLETED_TASK;
    String topicName = FACE_COMPLETED_TASK;

    this.send(groupName, topicName, completedFaceTask.getBizCode(), json);
  }

  public void sendResourceDownloadMsg(ResourceDownloadMsg downloadMsg) {
    DelayMsgLevel delayMsgLevel = DelayMsgLevelSupport.codeOf(ResourceSystemHolder.getInstance().getResourceDownloadMqDelayLevel());
    String json = JSON.toJSONString(downloadMsg);
    String ResourceDownloadExpTopic = ResourceSystemHolder.getInstance().getResourceDownloadMqTopic();
    String groupName = GROUP_ + ResourceDownloadExpTopic;
    String topicName = ResourceDownloadExpTopic;

    this.send(groupName, topicName, null, json,delayMsgLevel);
  }


  /**
   * 发送供应商调用记录
   *
   * @param data
   */
  public void sendProviderCall(ProviderMetric data) {
    if (data == null) {
      return;
    }

    send(LOG_GROUP+TOPIC_PROVIDER_CALL, TOPIC_PROVIDER_CALL, TOPIC_TAG, JSON.toJSONString(data));
  }

  /**
   * 发送操作记录
   *
   * @param data
   */
  public void sendOperationLog(SensorsData data) {
    if (data == null || data.getRecordMessage() == null) {
      return;
    }
    send(LOG_GROUP, LOG_TOPIC, TOPIC_TAG, JSON.toJSONString(data));
  }

  public void receiveMsg() throws Exception {
    startConsumer(
            GROUP_ + FACE_START_SUCCESS_TASK,
            FACE_START_SUCCESS_TASK,
            new StartFaceSuccessHandler());

    String ResourceDownloadTopic = ResourceSystemHolder.getInstance().getResourceDownloadMqTopic();
    startConsumer(
            GROUP_ + ResourceDownloadTopic,
            ResourceDownloadTopic,
            new ResourceDownloadTaskHandler(ResourceDownloadTopic));

    // 2025/7/7
    startConsumer(GROUP_ + TOPIC_FACE_RESOURCE_CONTROL, TOPIC_FACE_RESOURCE_CONTROL, new FaceUserResourceControlConsumer(TOPIC_FACE_RESOURCE_CONTROL));

  }

  private void initProducerFactory() {

    ProducerFactory factory = mixMQBootstrap.createProducerFactory();
    factory.setConfig(config);
    factory.addCreateInstanceHook(new RestartProducerCreateInstanceHook());

    injectSpring(factory);
    this.producerFactory = factory;
  }

  public void initConsumerFactory() {

    ConsumerFactory factory = mixMQBootstrap.createConsumerFactory();
    factory.setConfig(config);
    factory.addCreateInstanceHook(new RestartConsumerCreateInstanceHook());

    injectSpring(factory);
    this.consumerFactory = factory;
    log.info("init consumerFactory:{}", consumerFactory.getClass());
    try {
      receiveMsg();
    } catch (Exception e) {
      e.printStackTrace();
    }
    log.info("【start consume messages:{}】", consumerFactory.getClass());
  }

  /** 注入到 Spring 容器. */
  private void injectSpring(Object object) {
    if (!defaultListableBeanFactory.containsBean(object.getClass().getName())) {
      defaultListableBeanFactory.registerSingleton(object.getClass().getName(), object);
    }
    beanFactory.autowireBean(object);
  }
}
