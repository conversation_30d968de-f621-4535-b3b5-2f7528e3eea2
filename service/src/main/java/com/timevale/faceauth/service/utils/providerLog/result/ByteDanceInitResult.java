package com.timevale.faceauth.service.utils.providerLog.result;

import com.timevale.faceauth.service.domain.provider.bytedance.domain.FaceCertTokenResponse;
import com.timevale.faceauth.service.utils.providerLog.AbstractProviderLogResultResolver;
import com.timevale.faceauth.service.utils.providerLog.ProviderActionEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderKeyIdEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogConfiguration;

import java.util.List;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
public class ByteDanceInitResult
    extends AbstractProviderLogResultResolver<FaceCertTokenResponse> {

  public ByteDanceInitResult(String bizId, long responseTime) {
    super(bizId, responseTime);
  }

  @Override
  public ProviderKeyIdEnum getProviderKeyId() {
    return ProviderKeyIdEnum.ByteDance;
  }

  @Override
  public ProviderActionEnum getAction() {
    return ProviderActionEnum.INITIALIZATION;
  }

  @Override
  public String getCode(FaceCertTokenResponse response) {
    return String.valueOf(response.getCode());
  }

  @Override
  public String getMsg(FaceCertTokenResponse response) {
    return response.getMessage();
  }

  @Override
  public String getResult(FaceCertTokenResponse response) {
    return null;
  }

  @Override
  public List<String> getFieldEmptyMappingStautsUnknown() {
    return ProviderLogConfiguration.byteDanceActionInitializationFieldEmptyMappingStautsUnknown;
  }

  @Override
  public List<String> getCodeMsgMappingStautsFailure() {
    return ProviderLogConfiguration.byteDanceActionInitializationCodeMsgMappingStautsFailure;
  }

  @Override
  public List<String> getCodeMsgMappingStautsSuccess() {
    return ProviderLogConfiguration.byteDanceActionInitializationCodeMsgMappingStautsSuccess;
  }
}
