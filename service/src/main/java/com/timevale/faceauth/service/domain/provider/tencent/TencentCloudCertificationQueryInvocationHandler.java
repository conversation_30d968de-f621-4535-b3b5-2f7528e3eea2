package com.timevale.faceauth.service.domain.provider.tencent;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.FaceVideoResource;
import com.timevale.faceauth.service.domain.UserFacePhotoResource;
import com.timevale.faceauth.service.domain.event.FaceEventPublisher;
import com.timevale.faceauth.service.domain.event.FaceResourceDownloadedEvent;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.query.ApiInvocationQueryHandlerEngine;
import com.timevale.faceauth.service.domain.provider.query.ProviderResponse;
import com.timevale.faceauth.service.domain.provider.query.TencentCloudApiInvocationQueryHandler;
import com.timevale.faceauth.service.domain.provider.support.AbstractProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.repository.FaceInfo;
import com.timevale.faceauth.service.domain.repository.FaceOSSResources;
import com.timevale.faceauth.service.domain.repository.FaceResourceRepository;
import com.timevale.faceauth.service.domain.repository.ProviderFaceInfo;
import com.timevale.faceauth.service.domain.resource.ProviderTencentCloudH5RemotePhotoAllResourceLoader;
import com.timevale.faceauth.service.domain.resource.ProviderTencentCloudQueryContext;
import com.timevale.faceauth.service.enums.FaceAuthTrtcFlagEnum;
import com.timevale.faceauth.service.enums.FaceCertTypeEnum;
import com.timevale.faceauth.service.exception.ProviderException;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/18 18
 */
@Slf4j
@Component
public class TencentCloudCertificationQueryInvocationHandler
        extends TencentCloudCertificationQueryInvocationBaseHandler {


    @Autowired
    private FaceResourceRepository faceResourceRepository;
    private final ConfigurableProperties properties;


    @Autowired private ApiInvocationQueryHandlerEngine queryHandlerEngine;
    @Autowired private TencentCloudApiInvocationQueryHandler invocationQueryHandler;
    @Autowired private ProviderTencentCloudH5RemotePhotoAllResourceLoader photoAllResourceLoader;

    @Autowired
    public TencentCloudCertificationQueryInvocationHandler(
            ConfigurableProperties properties) {
        this.properties = properties;
    }

    public ProviderFaceAuthorizationResult invoke(
            FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo, TencentWebAppIdVersion appIdVersion)
            throws FaceException {
        String faceId = faceInfo.getFaceId();
        TencentCloudCertificationQueryRequest queryBody = prepareRequestQueryBoby(faceId, providerFaceInfo.getOrderNo(),appIdVersion);

        ProviderTencentCloudQueryContext queryContext = new ProviderTencentCloudQueryContext();
        queryContext.setFaceId(faceId);
        queryContext.setFaceInfo(faceInfo);
        queryContext.setProviderOrderNo(providerFaceInfo.getOrderNo());
        queryContext.setQueryRequest(queryBody);
        queryContext.setAppIdVersion(appIdVersion);
        ProviderResponse<TencentCloudCertificationQueryResponse> providerResponse =
                queryHandlerEngine.invoker(queryContext, invocationQueryHandler);


        return detectAuthorizationResult(
                providerResponse, queryContext, faceInfo, providerFaceInfo, appIdVersion);
    }




    private ProviderFaceAuthorizationResult detectAuthorizationResult(
            ProviderResponse<TencentCloudCertificationQueryResponse> providerResponse,
            ProviderTencentCloudQueryContext queryContext,
            FaceInfo faceInfo,
            ProviderFaceInfo providerFaceInfo,
            TencentWebAppIdVersion appIdVersion)
            throws FaceException {
        TencentCloudCertificationQueryResponse response = providerResponse.getOriginData();
        TencentCloudCertificationQueryResult queryResult =
                response.getResultIfAbsent(new TencentCloudCertificationQueryResult());
        queryResult.setCode(response.getCode());
        queryResult.setMsg(response.getMsg());

        // 解析供应商结果
        this.onCertificationQueried(queryResult, faceInfo);

        // 持久化用户照片和视频
        FaceAuthorizationResourceWrap resourceWrap =
                this.revokerAuthorizationResources(providerResponse,faceInfo, providerFaceInfo);
        String actualContent = this.deduceResponseContent(queryResult, resourceWrap);
        if (response.success()) {
            boolean faceAuthorizationState = this.isSuccessAuthorization(queryResult, faceInfo.getIdType());
            // 保存用户的刷脸照片
            if (faceAuthorizationState) {
                photoAllResourceLoader.execute(queryContext);
            }
            return TencentCloudFaceAuthorizationResult.createBuilder()
                    .setProvider(appIdVersion.getProviderName())
                    .setTimestamp(TencentCloudUtil.revokerDoneTime(queryResult, providerFaceInfo))
                    .setSuccess(faceAuthorizationState)
                    .setContent(actualContent)
                    .setCompleted(true)
                    .setSuccess(faceAuthorizationState)
                    .setFaceId(providerFaceInfo.getFaceId())
                    .setLiveRate(queryResult.getLiveRate())
                    .setSimilarity(queryResult.getSimilarity())
                    .setPhoto(resourceWrap.getPhoto())
                    .setPhotoAll(resourceWrap.getPhotoAll())
                    .setPhotoType(resourceWrap.getPhotoType())
                    .setVideo(resourceWrap.getVideo())
                    .setTrtcFlag(FaceAuthTrtcFlagEnum.codeOf(queryResult.getTrtcFlag()))
                    .build();
        }

        // 未能够得到正确的认证结果
        log.info(
                "Not found validation authorize result on face["
                        + faceInfo.getFaceId()
                        + "], and response is '"
                        + actualContent
                        + "' .");
        int errorCode = response.getCode();
        String errorMsg = response.getMsg();
        TencentCloudFaceResultStatus resultStatus =
                TencentCloudFaceResultStatus.getStatusByCode(errorCode);
        resultStatus.setDesc(errorMsg);
        ProviderException providerEx = new ProviderException(resultStatus.getFaceCode(), resultStatus);
        return TencentCloudFaceAuthorizationResult.createBuilder()
                .setProvider(appIdVersion.getProviderName())
                .setTimestamp(providerFaceInfo.getCreateTime().getTime())
                .setSuccess(false)
                .setFee(resultStatus.isFee())
                .setContent(actualContent)
                .setCompleted(resultStatus.isCompleted())
                .setFaceId(providerFaceInfo.getFaceId())
                .setLiveRate(queryResult.getLiveRate())
                .setSimilarity(queryResult.getSimilarity())
                .setPhoto(resourceWrap.getPhoto())
                .setPhotoType(resourceWrap.getPhotoType())
                .setVideo(resourceWrap.getVideo())
                .setError(providerEx)
                .build();
    }


    private void onCertificationQueried(
            TencentCloudCertificationQueryResult result, FaceInfo faceInfo) {
        log.info(
                "tencentCloud query result,status={},occurredTime={},faceId={},photo size ={}",
                result.getCode(),
                result.getOccurredTime(),
                faceInfo.getFaceId(),
                result.getPhoto() != null ? result.getPhoto().length() : 0);
        FaceResourceDownloadedEvent event =
                (new FaceResourceDownloadedEvent(
                        faceInfo.getFaceId(),
                        faceInfo.getIdNo(),
                        result.getPhoto(),
                        faceInfo.getPhotoType(),
                        result.getVideo()));
        FaceEventPublisher.publishEvent(event);
    }

    private FaceAuthorizationResourceWrap revokerAuthorizationResources(
            ProviderResponse providerResponse,
            FaceInfo face,
            ProviderFaceInfo providerFace) {
        String photo = providerResponse.getPhotoKey();
        String video = providerResponse.getVideoKey();

        FaceVideoResource videoResource = null;
        UserFacePhotoResource photoResource = null;
        try {
            if(StringUtils.isNotBlank(video)){
                videoResource = FaceVideoResource.createBuilder()
                        .setProvider(providerFace.getProvider())
                        .setFaceId(providerFace.getFaceId())
                        .setResourceContent(video)
                        .build();
            }
           if(StringUtils.isNotBlank(photo)){
               photoResource = UserFacePhotoResource.createBuilder()
                       .setOriginPhoto(face.getPhoto())
                       .setIdNo(face.getIdNo())
                       .setName(face.getName())
                       .setProvider(face.getProvider())
                       .setFaceId(face.getFaceId())
                       .setResourceContent(photo)
                       .setMimeType(face.getPhotoType())
                       .build();
           }

        } catch (Exception cause) {
            throw FaceException.valueOf(FaceStatusCode.RESOURCE_FAILED_DATASOURCE, cause);
        }
        return (new FaceAuthorizationResourceWrap(face, videoResource, photoResource));
    }



    private String deduceResponseContent(
            TencentCloudCertificationQueryResult result,
            FaceAuthorizationResourceWrap authorizationResourceWrap) {
        try {
            TencentCloudCertificationQueryResult contextResult =
                    (new TencentCloudCertificationQueryResult());
            contextResult.setVideo(authorizationResourceWrap.getVideo());
            contextResult.setPhoto(authorizationResourceWrap.getPhoto());
            contextResult.setBizSeqNo(result.getBizSeqNo());
            contextResult.setLiveRate(result.getLiveRate());
            contextResult.setOccurredTime(result.getOccurredTime());
            contextResult.setOrderNo(result.getOrderNo());
            contextResult.setSimilarity(result.getSimilarity());
            contextResult.setSuccess(result.isSuccess());
            contextResult.setTransactionTime(result.getTransactionTime());
            contextResult.setTrtcFlag(result.getTrtcFlag());
            contextResult.setCode(result.getCode());
            contextResult.setMsg(result.getMsg());
            return JsonUtils.obj2json(contextResult);
        } catch (Exception cause) {
            log.warn("Error serialize result .", cause);
            return JsonUtils.obj2json(result);
        }
    }

    private boolean isSuccessAuthorization(TencentCloudCertificationQueryResult queryResult, String certType) {
        //非大陆只有活体率分值
        if (FaceCertTypeEnum.notCHIDCard(certType)) {
            log.info("tencentCloud notCHIDCard isSuccessAuthorization , faceId = {} ", queryResult.getOrderNo());
            return (queryResult.getLiveRate() >= properties.getTencentCloudCertificationLiveRateMin()) && queryResult.faceOk();
        }

        //大陆有活体率分值和相似度分值
        return (queryResult.getSimilarity() >= properties.getTencentCloudCertificationSimilarityMin()
                && queryResult.getLiveRate() >= properties.getTencentCloudCertificationLiveRateMin());
    }

    AbstractProviderFaceAuthorizationResult resolveDoneQueryResult(
            String returnContent, FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo, String providerName)
            throws FaceException {
        TencentCloudCertificationQueryResult result;
        try {
            result = JsonUtils.json2pojo(returnContent, TencentCloudCertificationQueryResult.class);
        } catch (Exception cause) {
            log.error(
                    "Fail deserialize bean of type["
                            + TencentCloudCertificationQueryResult.class
                            + "] on data["
                            + returnContent
                            + "] .",
                    cause);
            throw FaceException.valueOf(FaceStatusCode.PROVIDER_ERROR_API_RESULT_SYNTAX);
        }


        boolean isOK = isSuccessAuthorization(result, faceInfo.getIdType())
                && faceInfo.isOk();
        TencentCloudFaceAuthorizationResult.TencentCloudFaceAuthorizationResultBuilder resultBuilder = TencentCloudFaceAuthorizationResult.createBuilder()
                .setProvider(providerName)
                .setTimestamp(TencentCloudUtil.revokerDoneTime(result, providerFaceInfo))
                .setSuccess(isOK)
                .setContent(returnContent)
                .setCompleted(providerFaceInfo.isDone())
                .setFaceId(providerFaceInfo.getFaceId())
                .setLiveRate(result.getLiveRate())
                .setSimilarity(result.getSimilarity())
                .setPhoto(result.getPhoto())
                .setPhotoType(faceInfo.getPhotoType())
                .setVideo(result.getVideo());


        if (isOK) {
            resultBuilder.setTrtcFlag(FaceAuthTrtcFlagEnum.codeOf(result.getTrtcFlag()));
            resultBuilder.setPhotoAll(this.loadFacePhotos(faceInfo.getFaceId()));
        }
        return resultBuilder.build();
    }

    private List<String> loadFacePhotos(String faceId) {
        FaceOSSResources ossResources = faceResourceRepository.getFaceResources(faceId);
        return ossResources.getPhotoAll();
    }



    private static final class FaceAuthorizationResourceWrap {

        private final FaceInfo faceInfo;
        private final FaceVideoResource videoResource;
        private UserFacePhotoResource photoBestResource = null;
        private List<UserFacePhotoResource> photoResourceList = null;

        private FaceAuthorizationResourceWrap(
                FaceInfo faceInfo,
                FaceVideoResource videoResource,
                UserFacePhotoResource photoResource) {
            this.faceInfo = faceInfo;
            this.videoResource = videoResource;
            this.photoBestResource = photoResource;
            if (photoResource != null) {
                photoResourceList = Arrays.asList(photoResource);
            }
        }

        private FaceAuthorizationResourceWrap(
                FaceInfo faceInfo,
                FaceVideoResource videoResource,
                List<UserFacePhotoResource> photoResources) {
            this.faceInfo = faceInfo;
            this.videoResource = videoResource;
            if (CollectionUtils.isNotEmpty(photoResources)) {
                photoBestResource = photoResources.get(0);
                photoResourceList = photoResources;
            }
        }

        public FaceVideoResource getVideoResource() {
            return videoResource;
        }

        public UserFacePhotoResource getPhotoBestResource() {
            return photoBestResource;
        }

        public List<UserFacePhotoResource> getPhotoResourceList() {
            return photoResourceList;
        }

        String getPhotoType() {
            return faceInfo.getPhotoType();
        }

        String getPhoto() {
            return (null == photoBestResource ? "" : photoBestResource.getResourceContent());
        }

        List<String> getPhotoAll() {
            if (CollectionUtils.isEmpty(photoResourceList)) {
                return null;
            }
            List<String> photoList = new ArrayList<>();
            for (UserFacePhotoResource photoResource : photoResourceList) {
                photoList.add(photoResource.getResourceContent());
            }
            return photoList;
        }

        String getVideo() {
            return (null == videoResource ? null : videoResource.getResourceContent());
        }
    }
}
