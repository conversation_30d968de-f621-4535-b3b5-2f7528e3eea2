package com.timevale.faceauth.service.thirdparty.audiovideodual.context;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 智能视频认证认证查询请求结果
 * <AUTHOR>
 * @since 2020-06-09 21:59
 */
@Data
@Builder
public class AudioVideoDualQueryResult {

    public static final String EXTEND_ALIPAY_USER_ID = "alipayUserId";
    public static final String EXTEND_ALIPAY_USER_MOBILE = "alipayUserMobile";
    public static final String EXTEND_VOICE_TEMPLTE = "voiceTemplate";
    public static final String EXTEND_VOICE_RESULT = "voiceResult";

    private int result;
    private boolean fee;
    private boolean providerFee;
    private boolean completed;
    private String errorCode;
    private String message;
    private String bizId;
    private long completedTimeMillis;
    private String photo;
    private String video;
    private String token;
    private Map<String, String> extend;
}
