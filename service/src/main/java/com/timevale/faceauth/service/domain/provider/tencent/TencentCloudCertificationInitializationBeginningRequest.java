package com.timevale.faceauth.service.domain.provider.tencent;

import com.timevale.faceauth.service.core.support.ArgumentUtil;

import java.util.UUID;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/18 18
 */
class TencentCloudCertificationInitializationBeginningRequest
    extends TencentCloudCertificationSignableRequest {

  static final String TYPE_FROM_APP = "App";
  static final String TYPE_FROM_BROWSER = "browser";

  private final String appId;

  private final String version;

  private final String orderNo;

  /** h5/geth5faceid 接口返回的唯一标识 */
  private final String faceId;

  /** 用户 ID，用户的唯一标识（不要带有特殊字符） */
  private final String userId;

  /** 页面主题颜色 */
  private final String theme;

  private final String ticket;

  private final String sign;

  private TencentCloudCertificationInitializationBeginningRequest(
      TencentCloudCertificationInitializationBeginningRequestBuilder builder) {
    super(builder);
    this.faceId = builder.faceId;
    this.version = super.getVersion();
    this.sign = super.getSign();
    this.userId = builder.userId;
    this.theme = builder.theme;
    this.appId = builder.appId;
    this.orderNo = builder.orderNo;
    this.ticket = builder.ticket;
  }

  public String getFaceId() {
    return faceId;
  }

  public String getUserId() {
    return userId;
  }

  @Override
  public String getSign() {
    return sign;
  }

  public String getTheme() {
    return theme;
  }

  @Override
  public String getVersion() {
    return version;
  }

  public String getAppId() {
    return appId;
  }


  public String getOrderNo() {
    return orderNo;
  }

  String obtainTicket() {
    return ticket;
  }

  static TencentCloudCertificationInitializationBeginningRequestBuilder createBuilder() {
    return (new TencentCloudCertificationInitializationBeginningRequestBuilder());
  }

  static class TencentCloudCertificationInitializationBeginningRequestBuilder
      extends TencentCloudCertificationSignableRequestBuilder<
          TencentCloudCertificationInitializationBeginningRequest,
          TencentCloudCertificationInitializationBeginningRequestBuilder> {

    private String faceId;
    private String userId;
    private String theme;
    private String appId;
    private String orderNo;
    private String ticket;

    @Override
    TencentCloudCertificationInitializationBeginningRequest build() {
      return (new TencentCloudCertificationInitializationBeginningRequest(this));
    }

    @Override
    protected TencentCloudCertificationInitializationBeginningRequestBuilder self() {
      return this;
    }

    TencentCloudCertificationInitializationBeginningRequestBuilder setFaceId(String faceId) {
      ArgumentUtil.throwIfEmptyArgument(faceId, "h5faceId");
      this.faceId = faceId;
      return this;
    }

    TencentCloudCertificationInitializationBeginningRequestBuilder setUserId(String userId) {
      ArgumentUtil.throwIfEmptyArgument(userId, "userId");
      this.userId = userId;
      return this;
    }

    TencentCloudCertificationInitializationBeginningRequestBuilder setTheme(String theme) {
      //可以为空
      this.theme = theme;
      return this;
    }

    TencentCloudCertificationInitializationBeginningRequestBuilder setAppId(String appId) {
      ArgumentUtil.throwIfEmptyArgument(appId, "appId");
      this.appId = appId;
      return this;
    }

    TencentCloudCertificationInitializationBeginningRequestBuilder setOrderNo(String orderNo) {
      ArgumentUtil.throwIfEmptyArgument(orderNo, "orderNo");
      this.orderNo = orderNo;
      return this;
    }

    TencentCloudCertificationInitializationBeginningRequestBuilder setTicket(String ticket) {
      ArgumentUtil.throwIfEmptyArgument(ticket, "ticket");
      this.ticket = ticket;
      return this;
    }
  }
}
