package com.timevale.faceauth.service.utils.providerLog.result;

import com.timevale.faceauth.service.apispace.facade.com.timevale.apispace.request.ApiResponseBaseMetric;
import com.timevale.faceauth.service.liveness.facade.LivenessRecognition;
import com.timevale.faceauth.service.utils.providerLog.AbstractProviderLogResultResolver;
import com.timevale.faceauth.service.utils.providerLog.ProviderActionEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderKeyIdEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogConfiguration;
import java.util.List;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
public class LivenessQueryResult extends AbstractProviderLogResultResolver<LivenessRecognition> {

  private LivenessRecognition response;

  public LivenessQueryResult(String bizId, long responseTime, LivenessRecognition response) {
    super(bizId, responseTime);
    this.response = response;
  }

  @Override
  public ProviderKeyIdEnum getProviderKeyId() {
    return ProviderKeyIdEnum.Liveness;
  }

  @Override
  public ProviderActionEnum getAction() {
    return ProviderActionEnum.QUERY;
  }

  @Override
  public String getCode(LivenessRecognition response) {
    return response.getResultCode();
  }

  @Override
  public String getMsg(LivenessRecognition response) {
    return response.getResultMsg();
  }

  @Override
  public String getResult(LivenessRecognition response) {
    return null;
  }

  @Override
  public List<String> getFieldEmptyMappingStautsUnknown() {
    return ProviderLogConfiguration.livenessActionQueryFieldEmptyMappingStautsUnknown;
  }

  @Override
  public List<String> getCodeMsgMappingStautsFailure() {
    return ProviderLogConfiguration.livenessActionQueryCodeMsgMappingStautsFailure;
  }

  @Override
  public List<String> getCodeMsgMappingStautsSuccess() {
    return null;
  }

  @Override
  public ApiResponseBaseMetric resolver(LivenessRecognition response) {
    ApiResponseBaseMetric apiResponseBaseMetric = super.resolver(response);
    long tagAttribute = 0;
    // 1010过期(5) 0100取消(2) 0010失败(4) 0011成功(12)
    if (response.isExpired()) {
      tagAttribute += ATTRIBUTE_1;
    }
    if (response.isCancelled()) {
      tagAttribute += ATTRIBUTE_2;
    }
    if (response.isCompleted()) {
      tagAttribute += ATTRIBUTE_3;
    }
    if (response.getResult().isSuccess()) {
      tagAttribute += ATTRIBUTE_4;
    }
    apiResponseBaseMetric.setTag(String.valueOf(tagAttribute));
    return apiResponseBaseMetric;
  }
}
