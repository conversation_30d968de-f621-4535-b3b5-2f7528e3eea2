package com.timevale.faceauth.service.domain.support;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.domain.ConfigurableFaceAuthorizationCompletedInvocationHandler;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.FaceAuthorizationFinishedResolver;
import com.timevale.faceauth.service.domain.FaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.support.AbstractProviderService;
import com.timevale.faceauth.service.domain.repository.*;
import com.timevale.faceauth.service.enums.FaceSwitchStatusEnum;
import com.timevale.faceauth.service.tencentface.cache.FaceSwitchCache;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 主要目的：实现刷脸认证完成业务操作
 *
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/9 10
 */
@Component
public class SupportFaceAuthorizationFinishedResolver implements FaceAuthorizationFinishedResolver {

  private final FaceRepository faceRepository;
  private final FaceSwitchRepository faceSwitchRepository;
  private final ProviderFaceRepository providerFaceRepository;
  private final ConfigurableProperties configurableProperties;

  @Autowired
  public SupportFaceAuthorizationFinishedResolver(
      FaceRepository faceRepository,
      FaceSwitchRepository faceSwitchRepository,
      ProviderFaceRepository providerFaceRepository,
      ConfigurableProperties configurableProperties) {
    this.faceRepository = faceRepository;
    this.faceSwitchRepository = faceSwitchRepository;
    this.providerFaceRepository = providerFaceRepository;
    this.configurableProperties = configurableProperties;
  }

  @Transactional(rollbackFor = Throwable.class)
  @Override
  public FaceAuthorizationResult resolveFinishedFaceAuthorization(
      String finishedType, ProviderFaceAuthorizationResult providerFaceAuthorizationResult)
      throws FaceException {
    // 1. 保存供应商调用记录
    saveProviderReturn(finishedType, providerFaceAuthorizationResult);
    // 2. 更新刷脸供应商完成状态
    completedProviderFace(providerFaceAuthorizationResult);
    // 3. 更新刷脸成功状态
    FaceInfo faceInfo = completedFaceAuthorization(providerFaceAuthorizationResult);
    if (ConfigurableFaceAuthorizationCompletedInvocationHandler.TYPE_COMPLETED_RETURN.equals(
        finishedType)) {
      updateSwitchFlagTentatively(faceInfo);
      saveSpecifiedFaceError(faceInfo, providerFaceAuthorizationResult);
    }
    return FaceAuthorizationResult.createBuilder()
        .setProvider(providerFaceAuthorizationResult.getProvider())
        .setFaceId(providerFaceAuthorizationResult.getFaceId())
        .setBizId(faceInfo.getBizId())
        .setSuccess(providerFaceAuthorizationResult.isSuccess())
        .setCompleted(providerFaceAuthorizationResult.isCompleted())
        .setLiveRate(providerFaceAuthorizationResult.getLiveRate())
        .setSimilarity(providerFaceAuthorizationResult.getSimilarity())
        .setPhoto(providerFaceAuthorizationResult.getPhoto())
        .setPhotoAll(providerFaceAuthorizationResult.getPhotoAll())
        .setPhotoType(providerFaceAuthorizationResult.getPhotoType())
        .setVideo(providerFaceAuthorizationResult.getVideo())
        .setAuthorizationTimestamp(
            0 >= providerFaceAuthorizationResult.getTimestamp()
                ? faceInfo.getCreateTime().getTime()
                : providerFaceAuthorizationResult.getTimestamp())
        .setFaceErrorCode(providerFaceAuthorizationResult.getFaceErrorCode())
        .setFaceErrorMsg(providerFaceAuthorizationResult.getFaceErrorMsg())
        .setProviderErrorMsg(providerFaceAuthorizationResult.getFaceErrorMsg())
        .setProviderErrorCode(providerFaceAuthorizationResult.getFaceErrorCode())
        .build();
  }

  public void saveProviderReturn(
      String finishedType, ProviderFaceAuthorizationResult providerFaceAuthorizationResult)
      throws FaceException {
    ProviderReturnInfo returnInfo =
        ProviderReturnInfo.createBuilder()
            .setFaceId(providerFaceAuthorizationResult.getFaceId())
            .setProvider(providerFaceAuthorizationResult.getProvider())
            .setReturnType(finishedType)
            .setData(providerFaceAuthorizationResult.getResultContent())
            .setCompleted(providerFaceAuthorizationResult.isCompleted())
            .setErrorMsg(providerFaceAuthorizationResult.getProviderErrorMsg())
            .setErrorCode(providerFaceAuthorizationResult.getProviderErrorCode())
            .setTimestamp(providerFaceAuthorizationResult.getTimestamp())
            .build();
    providerFaceRepository.saveProviderReturnInfo(returnInfo);
  }

  private ProviderFaceInfo completedProviderFace(
      ProviderFaceAuthorizationResult providerFaceAuthorizationResult) throws FaceException {
    ProviderFaceInfo info =
        providerFaceRepository.getByFaceId(providerFaceAuthorizationResult.getFaceId());
    if (!info.isDone()) {
      ProviderFaceCompletion completion =
          ProviderFaceCompletion.createBuilder()
              .setFaceId(providerFaceAuthorizationResult.getFaceId())
              .setCompleted(providerFaceAuthorizationResult.isCompleted())
              .setTimestamp(providerFaceAuthorizationResult.getTimestamp())
              .setErrCode(providerFaceAuthorizationResult.getProviderErrorCode())
              .setErrMsg(providerFaceAuthorizationResult.getProviderErrorMsg())
              .build();
      providerFaceRepository.completedProviderFace(completion);
    }
    return info;
  }

  private FaceInfo completedFaceAuthorization(
      ProviderFaceAuthorizationResult providerFaceAuthorizationResult) throws FaceException {
    FaceInfo faceInfo =
        faceRepository.getFaceInfoByFaceId(providerFaceAuthorizationResult.getFaceId());
    if (!faceInfo.isOk()) {
      FaceCompletion completion =
          FaceCompletion.createBuilder()
              .setFaceId(providerFaceAuthorizationResult.getFaceId())
              .setSuccess(providerFaceAuthorizationResult.isSuccess())
              .build();
      faceRepository.completedFace(completion);
    }
    if (providerFaceAuthorizationResult.isCompleted()) {
      FaceErrorResult errorResult =
          FaceErrorResult.builder()
              .faceId(faceInfo.getFaceId())
              .resultCode(providerFaceAuthorizationResult.getFaceErrorCode())
              .resultMsg(providerFaceAuthorizationResult.getFaceErrorMsg())
              .build();
      faceRepository.errorResultFace(errorResult);
    }
    return faceInfo;
  }

  /**
   * 将某些特定错误的刷脸保存至DB 目前只保存比对源照片有问题的刷脸记录
   *
   * @param faceInfo
   * @param providerFaceAuthorizationResult
   */
  private void saveSpecifiedFaceError(
      FaceInfo faceInfo, ProviderFaceAuthorizationResult providerFaceAuthorizationResult) {
    if(StringUtils.isBlank(faceInfo.getPhoto())){
      return;
    }
    if (StringUtils.isBlank(providerFaceAuthorizationResult.getProviderErrorCode())) {
      return;
    }
    String provider = faceInfo.getProvider();
    //腾讯云刷脸失败判定
    boolean tencentFaceFail = Objects.equals(provider, AbstractProviderService.PROVIDER_TENCENT_CLOUD)
            && configurableProperties
            .getTenCentCloudIndependentH5ErrorCode()
            .contains(providerFaceAuthorizationResult.getProviderErrorCode());
    //非腾讯云刷脸失败判定
    boolean faceOtherFail = !Objects.equals(provider, AbstractProviderService.PROVIDER_TENCENT_CLOUD)
            && providerFaceAuthorizationResult.isCompleted()
            && !providerFaceAuthorizationResult.isSuccess();
    if (tencentFaceFail || faceOtherFail) {
      // 双写至DB 和 Redis
      FaceSwitchModel model =
          FaceSwitchModel.builder()
              .name(faceInfo.getName())
              .idNo(faceInfo.getIdNo())
              .faceId(faceInfo.getFaceId())
              .errMsg(providerFaceAuthorizationResult.getProviderErrorCode())
              .faceMode(faceInfo.getProvider())
              .isSwitched(FaceSwitchStatusEnum.UN_SWITCHED.getCode())
              .build();
      faceSwitchRepository.saveFaceSwitchInfo(model);
      // 放缓存里面
      FaceSwitchCache.set2Redis(
          FaceSwitchCache.generateKey(
              model.getName(), model.getIdNo(), faceInfo.getProvider()),
          JsonUtils.obj2json(model));
    }
  }

  /**
   * 试探性地更新切换标志
   *
   * @param faceInfo
   */
  private void updateSwitchFlagTentatively(FaceInfo faceInfo) {
    // 双删DB 和 Redis，此处的数据库删除实际上是逻辑删除（更新）
    FaceSwitchModel model =
        FaceSwitchModel.builder()
            .name(faceInfo.getName())
            .idNo(faceInfo.getIdNo())
            .faceMode(faceInfo.getProvider())
            .isSwitched(FaceSwitchStatusEnum.SWITCHED.getCode())
            .build();
    if ((faceSwitchRepository.updateSwitchFlag(model)) > 0) {
      // 删除缓存
      FaceSwitchCache.deleteFromRedis(
          FaceSwitchCache.generateKey(
              faceInfo.getName(),
              faceInfo.getIdNo(),
                  faceInfo.getProvider()));
    }
  }
}
