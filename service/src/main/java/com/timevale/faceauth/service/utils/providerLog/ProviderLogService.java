package com.timevale.faceauth.service.utils.providerLog;

import cn.com.antcloud.api.realperson.v1_0_0.request.CreateFacevrfServerRequest;
import cn.com.antcloud.api.realperson.v1_0_0.request.QueryFacevrfServerRequest;
import cn.com.antcloud.api.realperson.v1_0_0.response.CreateFacevrfServerResponse;
import cn.com.antcloud.api.realperson.v1_0_0.response.QueryFacevrfServerResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.request.AlipayUserCertifyOpenCertifyRequest;
import com.alipay.api.request.AlipayUserCertifyOpenInitializeRequest;
import com.alipay.api.request.AlipayUserCertifyOpenQueryRequest;
import com.alipay.api.response.AlipayUserCertifyOpenCertifyResponse;
import com.alipay.api.response.AlipayUserCertifyOpenInitializeResponse;
import com.alipay.api.response.AlipayUserCertifyOpenQueryResponse;
import com.tencentcloudapi.common.AbstractModel;
import com.timevale.faceauth.service.bean.ApiResponseLogMetric;
import com.timevale.faceauth.service.domain.event.FaceEventPublisher;
import com.timevale.faceauth.service.domain.provider.bytedance.domain.FaceCertTokenRequest;
import com.timevale.faceauth.service.domain.provider.bytedance.domain.FaceCertTokenResponse;
import com.timevale.faceauth.service.domain.provider.bytedance.domain.FaceQueryRequest;
import com.timevale.faceauth.service.domain.provider.bytedance.domain.FaceQueryResponse;
import com.timevale.faceauth.service.liveness.facade.LivenessApplyResult;
import com.timevale.faceauth.service.liveness.facade.LivenessRecognition;
import com.timevale.faceauth.service.liveness.support.LivenessAuthorizationApplyRequest;
import com.timevale.faceauth.service.utils.providerLog.result.AliMiniProgInitCertifyIdResult;
import com.timevale.faceauth.service.utils.providerLog.result.AliMiniProgInitCertifyUrlResult;
import com.timevale.faceauth.service.utils.providerLog.result.AliMiniProgQueryResult;
import com.timevale.faceauth.service.utils.providerLog.result.AntBlockChainInitResult;
import com.timevale.faceauth.service.utils.providerLog.result.AntBlockChainQueryResult;
import com.timevale.faceauth.service.utils.providerLog.result.AudioVideoDualInitResult;
import com.timevale.faceauth.service.utils.providerLog.result.AudioVideoDualQueryResult;
import com.timevale.faceauth.service.utils.providerLog.result.ByteDanceInitResult;
import com.timevale.faceauth.service.utils.providerLog.result.ByteDanceQueryResult;
import com.timevale.faceauth.service.utils.providerLog.result.DingTalkInitResult;
import com.timevale.faceauth.service.utils.providerLog.result.DingTalkQueryResult;
import com.timevale.faceauth.service.utils.providerLog.result.LivenessInitResult;
import com.timevale.faceauth.service.utils.providerLog.result.LivenessQueryResult;
import com.timevale.faceauth.service.utils.providerLog.result.TencentCloudInitResult;
import com.timevale.faceauth.service.utils.providerLog.result.TencentCloudQueryResult;
import com.timevale.faceauth.service.utils.providerLog.result.WeChatFaceInitResult;
import com.timevale.faceauth.service.utils.providerLog.result.WeChatFaceQueryResult;
import com.timevale.faceauth.service.utils.providerLog.result.WeChatVideoDualInitResult;
import com.timevale.faceauth.service.utils.providerLog.result.WeChatVideoDualQueryResult;
import com.timevale.mediaauth.facade.dto.request.AudioVideoDualInitializeRequest;
import com.timevale.mediaauth.facade.dto.request.AudioVideoDualQueryRequest;
import com.timevale.mediaauth.facade.dto.response.AudioVideoDualInitializeResponse;
import com.timevale.mediaauth.facade.dto.response.AudioVideoDualQueryResponse;
import com.timevale.mediaauth.facade.enums.VideoClientType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.context.event.EventListener;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> href="mailto:<EMAIL>">jiuchen</a>
 *     <p>log开头的方法走AOP
 * @see com.timevale.faceauth.service.utils.providerLog.ThirdpartApiInvokeAspect
 */
@Slf4j
@Component
public class ProviderLogService implements ProviderLogConstants {

  public void logAntBlockChainInit(
      AntBlockChainInitResult metric,
      CreateFacevrfServerRequest request,
      CreateFacevrfServerResponse response) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, response, x -> metric.completed(response))));
    } catch (Exception e) {
      //
    }
  }

  public void logAntBlockChainQuery(
      AntBlockChainQueryResult metric,
      QueryFacevrfServerRequest request,
      QueryFacevrfServerResponse response) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, response, x -> metric.completed(response))));
    } catch (Exception e) {
      //
    }
  }

  public void logLivenessInit(
      LivenessInitResult metric,
      LivenessAuthorizationApplyRequest request,
      LivenessApplyResult response) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, response, x -> metric.completed(response))));
    } catch (Exception e) {
      //
    }
  }

  public void logLivenessQuery(
      LivenessQueryResult metric, String request, LivenessRecognition response) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, response, x -> metric.completed(response))));
    } catch (Exception e) {
      //
    }
  }

  public void logTencentCloudInit(
      TencentCloudInitResult metric, RequestEntity<?> request, ResponseEntity<String> response) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, response, x -> metric.completed(response))));
    } catch (Exception e) {
      //
    }
  }

  public void logTencentCloudQuery(
      TencentCloudQueryResult metric, RequestEntity<?> request, ResponseEntity<String> response) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, response, x -> metric.completed(response))));
    } catch (Exception e) {
      //
    }
  }

  public void logAliMiniProgInitCertifyId(
      AliMiniProgInitCertifyIdResult metric,
      AlipayUserCertifyOpenInitializeRequest request,
      AlipayUserCertifyOpenInitializeResponse response) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, response, x -> metric.completed(response))));
    } catch (Exception e) {
      //
    }
  }

  public void logAliMiniProgInitCertifyUrl(
      AliMiniProgInitCertifyUrlResult metric,
      AlipayUserCertifyOpenCertifyRequest request,
      AlipayUserCertifyOpenCertifyResponse response) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, response, x -> metric.completed(response))));
    } catch (Exception e) {
      //
    }
  }

  public void logAliMiniProgQuery(
      AliMiniProgQueryResult metric,
      AlipayUserCertifyOpenQueryRequest request,
      AlipayUserCertifyOpenQueryResponse response) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, response, x -> metric.completed(response))));
    } catch (Exception e) {
      //
    }
  }

  public void logWeChatFaceInit(
      WeChatFaceInitResult metric, AbstractModel request, JSONObject response) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, response, x -> metric.completed(response))));
    } catch (Exception e) {
      //
    }
  }

  public void logWeChatFaceQuery(
      WeChatFaceQueryResult metric, AbstractModel request, JSONObject response) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, response, x -> metric.completed(response))));
    } catch (Exception e) {
      //
    }
  }

  public void logDingTalkInit(DingTalkInitResult metric) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, null, x -> metric.completed(null))));
    } catch (Exception e) {
      //
    }
  }

  public void logDingTalkQuery(
      DingTalkQueryResult metric, RequestEntity<?> request, ResponseEntity<String> response) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, response, x -> metric.completed(response))));
    } catch (Exception e) {
      //
    }
  }

  public void logAudioVideoDualInit(
      AudioVideoDualInitResult metric,
      AudioVideoDualInitializeRequest request,
      AudioVideoDualInitializeResponse response) {

    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, response, x -> metric.completed(response))));
    } catch (Exception e) {
      //
    }
  }

  public void logWeChatVideoDualInit(
      WeChatVideoDualInitResult metric,
      AudioVideoDualInitializeRequest request,
      AudioVideoDualInitializeResponse response) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, response, x -> metric.completed(response))));
    } catch (Exception e) {
      //
    }
  }

  public void logAudioVideoDualQuery(
      AudioVideoDualQueryResult metric,
      AudioVideoDualQueryRequest request,
      AudioVideoDualQueryResponse response) {

    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, response, x -> metric.completed(response))));
    } catch (Exception e) {
      //
    }
  }

  public void logWeChatVideoDualQuery(
      WeChatVideoDualQueryResult metric,
      AudioVideoDualQueryRequest request,
      AudioVideoDualQueryResponse response) {

    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, response, x -> metric.completed(response))));
    } catch (Exception e) {
      //
    }
  }

  public void logByteDanceInit(
          ByteDanceInitResult metric, FaceCertTokenRequest request, FaceCertTokenResponse response) {
    try {
      FaceEventPublisher.publishEvent(
              new ThirdpartApiWatchEvent(
                      metric.getBizId(),
                      new ThirdpartApiWatchMetric<>(metric, response, x -> metric.completed(response))));
    } catch (Exception e) {
      //
    }
  }

  public void logByteDanceQuery(
          ByteDanceQueryResult metric, FaceQueryRequest request,  FaceQueryResponse response) {
    try {
      FaceEventPublisher.publishEvent(
              new ThirdpartApiWatchEvent(
                      metric.getBizId(),
                      new ThirdpartApiWatchMetric<>(metric, response, x -> metric.completed(response))));
    } catch (Exception e) {
      //
    }
  }

  public void logAntBlockChainInitWithException(
      AntBlockChainInitResult metric, CreateFacevrfServerRequest request, Exception ex) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, null, x -> metric.exception(ex))));
    } catch (Exception e) {
      //
    }
  }

  public void logAntBlockChainQueryWithException(
      AntBlockChainQueryResult metric, QueryFacevrfServerRequest request, Exception ex) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, null, x -> metric.exception(ex))));
    } catch (Exception e) {
      //
    }
  }

  public void logLivenessInitWithException(
      LivenessInitResult metric, LivenessAuthorizationApplyRequest request, Exception ex) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, null, x -> metric.exception(ex))));
    } catch (Exception e) {
      //
    }
  }

  public void logTencentCloudInitWithException(
      TencentCloudInitResult metric, RequestEntity<?> request, Exception ex) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, null, x -> metric.exception(ex))));
    } catch (Exception e) {
      //
    }
  }

  public void logTencentCloudQueryWithException(
      TencentCloudQueryResult metric, RequestEntity<?> request, Exception ex) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, null, x -> metric.exception(ex))));
    } catch (Exception e) {
      //
    }
  }

  public void logDingTalkQueryWithException(
      DingTalkQueryResult metric, RequestEntity<?> request, Exception ex) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, null, x -> metric.exception(ex))));
    } catch (Exception e) {
      //
    }
  }

  public void logAliMiniProgInitCertifyIdWithException(
      AliMiniProgInitCertifyIdResult metric,
      AlipayUserCertifyOpenInitializeRequest request,
      Exception ex) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, null, x -> metric.exception(ex))));
    } catch (Exception e) {
      //
    }
  }

  public void logAliMiniProgInitCertifyUrlWithException(
      AliMiniProgInitCertifyUrlResult metric,
      AlipayUserCertifyOpenCertifyRequest request,
      Exception ex) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, null, x -> metric.exception(ex))));
    } catch (Exception e) {
      //
    }
  }

  public void logAliMiniProgQueryWithException(
      AliMiniProgQueryResult metric, AlipayUserCertifyOpenQueryRequest request, Exception ex) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, null, x -> metric.exception(ex))));
    } catch (Exception e) {
      //
    }
  }

  public void logWeChatFaceInitWithException(
      WeChatFaceInitResult metric, AbstractModel request, Exception ex) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, null, x -> metric.exception(ex))));
    } catch (Exception e) {
      //
    }
  }

  public void logWeChatFaceQueryWithException(
      WeChatFaceQueryResult metric, AbstractModel request, Exception ex) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, null, x -> metric.exception(ex))));
    } catch (Exception e) {
      //
    }
  }

  public void logAudioVideoDualInitWithException(
      AudioVideoDualInitResult metric, AudioVideoDualInitializeRequest request, Exception ex) {

    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, null, x -> metric.exception(ex))));
    } catch (Exception e) {
      //
    }
  }

  public void logWeChatVideoDualInitWithException(
      WeChatVideoDualInitResult metric, AudioVideoDualInitializeRequest request, Exception ex) {
    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, null, x -> metric.exception(ex))));
    } catch (Exception e) {
      //
    }
  }

  public void logAudioVideoDualQueryWithException(
      AudioVideoDualQueryResult metric, AudioVideoDualQueryRequest request, Exception ex) {

    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, null, x -> metric.exception(ex))));
    } catch (Exception e) {
      //
    }
  }

  public void logWeChatVideoDualQueryWithException(
      WeChatVideoDualQueryResult metric, AudioVideoDualQueryRequest request, Exception ex) {

    try {
      FaceEventPublisher.publishEvent(
          new ThirdpartApiWatchEvent(
              metric.getBizId(),
              new ThirdpartApiWatchMetric<>(metric, null, x -> metric.exception(ex))));
    } catch (Exception e) {
      //
    }
  }

  public void logWeChatFaceInitOrQuery(
      ApiResponseLogMetric metric, AbstractModel request, String response, String routeTag) {
    try {
      JSONObject responseJSONObject = JSON.parseObject(response);
      if (WECHATFACE_ROUTETAG_INITIALIZATION.equalsIgnoreCase(routeTag)) {
        logWeChatFaceInit(
            new WeChatFaceInitResult(metric.getBizId(), metric.getResponseTime()),
            request,
            responseJSONObject);
      } else {
        logWeChatFaceQuery(
            new WeChatFaceQueryResult(metric.getBizId(), metric.getResponseTime()),
            request,
            responseJSONObject);
      }
    } catch (Exception e) {
      //
    }
  }

  public void logWeChatFaceInitOrQueryWithException(
      ApiResponseLogMetric metric, AbstractModel request, Exception ex, String routeTag) {
    try {
      if (WECHATFACE_ROUTETAG_INITIALIZATION.equalsIgnoreCase(routeTag)) {
        logWeChatFaceInitWithException(
            new WeChatFaceInitResult(metric.getBizId(), metric.getResponseTime()), request, ex);
      } else {
        logWeChatFaceQueryWithException(
            new WeChatFaceQueryResult(metric.getBizId(), metric.getResponseTime()), request, ex);
      }
    } catch (Exception e) {
      //
    }
  }

  public void logVideoDualInit(
      ApiResponseLogMetric metric,
      AudioVideoDualInitializeRequest request,
      AudioVideoDualInitializeResponse response,
      VideoClientType clientTypeEnum) {
    try {
      if (VideoClientType.ALIPAY_MINI == clientTypeEnum) {
        logAudioVideoDualInit(
            new AudioVideoDualInitResult(metric.getBizId(), metric.getResponseTime()),
            request,
            response);
      } else if(VideoClientType.WECHAT_MINI == clientTypeEnum) {
        logWeChatVideoDualInit(
            new WeChatVideoDualInitResult(metric.getBizId(), metric.getResponseTime()),
            request,
            response);
      } else {
        //TODO 视频认证
      }
    } catch (Exception e) {
      //
    }
  }

  public void logVideoDualInitWithException(
      ApiResponseLogMetric metric,
      AudioVideoDualInitializeRequest request,
      Exception ex,
      VideoClientType clientTypeEnum) {
    try {
      if (VideoClientType.ALIPAY_MINI == clientTypeEnum) {
        logAudioVideoDualInitWithException(
            new AudioVideoDualInitResult(metric.getBizId(), metric.getResponseTime()), request, ex);
      } else if(VideoClientType.WECHAT_MINI == clientTypeEnum){
        logWeChatVideoDualInitWithException(
            new WeChatVideoDualInitResult(metric.getBizId(), metric.getResponseTime()),
            request,
            ex);
      } else  {
        //TODO 视频认证
      }
    } catch (Exception e) {
      //
    }
  }

  public void logVideoDualQuery(
      ApiResponseLogMetric metric,
      AudioVideoDualQueryRequest request,
      AudioVideoDualQueryResponse response,
      VideoClientType clientTypeEnum) {
    try {
      if (VideoClientType.ALIPAY_MINI == clientTypeEnum) {
        logAudioVideoDualQuery(
            new AudioVideoDualQueryResult(metric.getBizId(), metric.getResponseTime()),
            request,
            response);
      } else if (VideoClientType.WECHAT_MINI == clientTypeEnum) {
        logWeChatVideoDualQuery(
            new WeChatVideoDualQueryResult(metric.getBizId(), metric.getResponseTime()),
            request,
            response);
      } else {
        //TODO 视频认证
      }
    } catch (Exception e) {
      //
    }
  }

  public void logVideoDualQueryWithException(
      ApiResponseLogMetric metric,
      AudioVideoDualQueryRequest request,
      Exception ex,
      VideoClientType clientTypeEnum) {
    try {
      if (VideoClientType.ALIPAY_MINI == clientTypeEnum) {
        logAudioVideoDualQueryWithException(
            new AudioVideoDualQueryResult(metric.getBizId(), metric.getResponseTime()),
            request,
            ex);
      }else if(VideoClientType.WECHAT_MINI == clientTypeEnum)  {
        logWeChatVideoDualQueryWithException(
            new WeChatVideoDualQueryResult(metric.getBizId(), metric.getResponseTime()),
            request,
            ex);
      } else {
        //TODO 视频认证
      }
    } catch (Exception e) {
      //
    }
  }

  public void logByteDanceInitWithException(
          ByteDanceInitResult metric, FaceCertTokenRequest request, Exception ex) {
    try {
      FaceEventPublisher.publishEvent(
              new ThirdpartApiWatchEvent(
                      metric.getBizId(),
                      new ThirdpartApiWatchMetric<>(metric, null, x -> metric.exception(ex))));
    } catch (Exception e) {
      //
    }
  }

  public void logByteDanceQueryWithException(
          ByteDanceQueryResult metric, FaceQueryRequest request, Exception ex) {
    try {
      FaceEventPublisher.publishEvent(
              new ThirdpartApiWatchEvent(
                      metric.getBizId(),
                      new ThirdpartApiWatchMetric<>(metric, null, x -> metric.exception(ex))));
    } catch (Exception e) {
      //
    }
  }

  @Async
  @EventListener
  public void doHandle(ThirdpartApiWatchEvent event) {
    try {
      doHandleEvent(event);
    } catch (Exception ex) {
      LogSupoort.providerLogInfo(
          log, "ThirdpartApiWatchEvent exception : {}", ExceptionUtils.getStackTrace(ex));
    }
  }

  private void doHandleEvent(ThirdpartApiWatchEvent event) {
    ThirdpartApiWatchMetric metric = event.getSource();
    metric.getDoneConsumer().accept(metric.getResponse());
  }
}
