package com.timevale.faceauth.service.domain.provider.tencent;

import com.timevale.faceauth.service.core.support.ArgumentUtil;
import org.springframework.util.StringUtils;

/**
 * 腾讯云刷脸认证初始化上送请求
 *
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/18 17
 */
class TencentCloudCertificationInitializationUploadRequest
    extends TencentCloudCertificationRequest {

  /** 水纹照片类型 */
  static final byte PHOTO_TYPE_WATER = (byte) 0x01;
  /** 高清照片类型 */
  static final byte PHOTO_TYPE_HIGH_DEFINITION = (byte) 0x02;

  /** 用户 ID ，用户的唯一标识（不能带有特殊字符） */
  private final String userId;
  /** 证件号码 */
  private final String idNo;
  /**
   * 枚举值：不传该字段、传空、01为国内二代身份证（原有的证件服务）；
   * 02：港澳居民来往内地通行证、03：台湾居民来往内地通行证定、04：居国外的中国公
   * 民护照、05：外国人永久居留身份证
   * 腾讯云建议：国内二代身份证不用做修改，idType 不传；新增的出入境证件类型增加传
   * idType字段。
   * */
  private final String idType;
  /** 姓名 */
  private final String name;
  /** 比对源照片类型 参数值为1：水纹正脸照 参数值为2：高清正脸照 */
  private final Byte sourcePhotoType;
  /**
   * 比对源照片， 注意： 原始图片不能超过500KB，且必须为 JPG 或 PNG 格式 参数有值： 使用合作伙伴提供的比对源照片进行比对，必须注照片是正脸可信照片，照片质量由合作方保证
   * 参数为空： 根据身份证号+姓名使用权威数据源比对
   */
  private String sourcePhotoStr;

  /**
   * 活体交互模式参数值为1时，表示仅使用实时检测模式，不兼容的情况下回调错误码3005参数值非1或不入参，表示优先使用实时检测模式，如遇不兼容情况，自动降级为视频录制模式
   */
  private String liveInterType;

  /**
   * 是否显示结果页面
   * 参数值为“1”时直接跳转到 url 回调地址
   * null 或 其他值跳转提供的结果页
   */
  private String resultType;

  /**
   * H5 人脸核身完成后回调的第三方URL，需要第三方提供完整 URL 且做 URL Encode
   * 完整 URL Encode 示例:原 URL(https://cloud.tencent.com) Encode 后：(https%3a%2f%2fcloud.tencent.com
   */
  private String url;

  /**
   * 随机数
   */
  private String nonce;

  /**
   * appId
   */
  private String appId;

  private TencentCloudCertificationInitializationUploadRequest(
      TencentCloudCertificationInitializationUploadRequestBuilder builder) {
    super(builder);
    this.userId = builder.userId;
    this.idNo = builder.idNo;
    this.idType = builder.idType;
    this.name = builder.name;
    this.url = builder.url;
    this.resultType = "1";
    this.nonce = builder.nonce;
    this.appId = builder.appId;
    this.sourcePhotoStr = builder.sourcePhotoStr;
    this.sourcePhotoType = (StringUtils.isEmpty(builder.sourcePhotoStr) ? null : builder.sourcePhotoType);
    this.liveInterType = builder.liveInterType;
  }

  public String getUserId() {
    return userId;
  }

  public String getIdNo() {
    return idNo;
  }

  public String getIdType() {
    return idType;
  }

  public String getUrl() {
    return url;
  }

  public String getResultType() {
    return resultType;
  }

  public String getNonce() {
    return nonce;
  }

  public String getAppId() {
    return appId;
  }

  public String getName() {
    return name;
  }

  public String getSourcePhotoStr() {
    return sourcePhotoStr;
  }

  public Byte getSourcePhotoType() {
    return sourcePhotoType;
  }

  public String getLiveInterType() {
    return liveInterType;
  }

  void setSourcePhotoStr(String sourcePhotoStr) {
    this.sourcePhotoStr = sourcePhotoStr;
  }

  static TencentCloudCertificationInitializationUploadRequestBuilder createBuilder() {
    return (new TencentCloudCertificationInitializationUploadRequestBuilder());
  }

  static class TencentCloudCertificationInitializationUploadRequestBuilder
      extends TencentCloudCertificationRequestBuilder<
          TencentCloudCertificationInitializationUploadRequest,
          TencentCloudCertificationInitializationUploadRequestBuilder> {

    private String userId;
    private String idNo;
    private String idType;
    private String name;
    private String sourcePhotoStr;
    private String url;
    private Byte sourcePhotoType = null;
    private String liveInterType;
    private String nonce;
    private String appId;

    @Override
    TencentCloudCertificationInitializationUploadRequest build() {
      return (new TencentCloudCertificationInitializationUploadRequest(this));
    }

    @Override
    protected TencentCloudCertificationInitializationUploadRequestBuilder self() {
      return this;
    }

    TencentCloudCertificationInitializationUploadRequestBuilder setUserId(String userId) {
      ArgumentUtil.throwIfEmptyArgument(userId, "userId");
      this.userId = userId;
      return this;
    }

    TencentCloudCertificationInitializationUploadRequestBuilder setIdNo(String idNo) {
      ArgumentUtil.throwIfEmptyArgument(idNo, "idNo");
      this.idNo = idNo;
      return this;
    }

    TencentCloudCertificationInitializationUploadRequestBuilder setIdType(String idType) {
      this.idType = idType;
      return this;
    }

    TencentCloudCertificationInitializationUploadRequestBuilder setName(String name) {
      ArgumentUtil.throwIfEmptyArgument(name, "name");
      this.name = name;
      return this;
    }

    TencentCloudCertificationInitializationUploadRequestBuilder setSourcePhotoStr(
        String sourcePhotoStr) {
      this.sourcePhotoStr = sourcePhotoStr;
      return this;
    }

    TencentCloudCertificationInitializationUploadRequestBuilder setUrl(
        String url) {
      this.url = url;
      return this;
    }

    TencentCloudCertificationInitializationUploadRequestBuilder setNonce(
        String nonce) {
      this.nonce = nonce;
      return this;
    }

    TencentCloudCertificationInitializationUploadRequestBuilder setAppId(
        String appId) {
      this.appId = appId;
      return this;
    }

    TencentCloudCertificationInitializationUploadRequestBuilder setSourcePhotoType(
        byte sourcePhotoType) {
      ArgumentUtil.throwIfNotTrue(sourcePhotoType > (byte) 0x00, "sourcePhotoType");
      this.sourcePhotoType = sourcePhotoType;
      return this;
    }

    TencentCloudCertificationInitializationUploadRequestBuilder setLiveInterType(String liveInterType) {
      this.liveInterType = liveInterType;
      return this;
    }
  }
}
