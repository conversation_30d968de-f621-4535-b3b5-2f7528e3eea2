package com.timevale.faceauth.service.domain.provider.support;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.event.FaceEventPublisher;
import com.timevale.faceauth.service.domain.event.ProviderApiFailEvent;
import com.timevale.faceauth.service.domain.support.RestTemplateUtil;
import com.timevale.faceauth.service.utils.providerLog.AbstractProviderLogResultResolver;
import com.timevale.faceauth.service.utils.providerLog.LogConsumer;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogService;
import com.timevale.mandarin.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.net.URI;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/21 18
 */
@Slf4j
@Component
public class RestTemplateRequestResolver implements HttpRequestResolver{

  private static final String EMPTY = "null";
  private final Map<Class<?>, List<Field>> fieldsMapping;


  public RestTemplateRequestResolver() {
    this.fieldsMapping = (new ConcurrentHashMap<>(16));
  }

  public URI resolveURI(String uriString) throws FaceException {
    try {
      return URI.create(uriString.trim());
    } catch (Exception cause) {
      throw FaceException.valueOf(
          FaceStatusCode.RESOURCE_ERROR_HTTP_INITIALIZATION,
          "Error uri[" + uriString + "] .",
          cause);
    }
  }

  public String resolveQueryString(Object request) throws FaceException {
    List<Field> fields = fieldsMapping.get(request.getClass());
    if (null == fields) {
      fields = getDefinitionFields(request.getClass());
      fieldsMapping.put(request.getClass(), fields);
    }
    List<String> params = new ArrayList<>();
    for(Field field : fields){
      String argName = field.getName();
      try {
        Object obj = field.get(request);
        if(obj == null){
          continue;
        }
        String value = String.valueOf(obj);
        if(Objects.equals(EMPTY, value) || StringUtils.isBlank(value)){
            continue;
        }
        params.add(argName + "=" + value);
      } catch (Exception cause) {
        throw FaceException.valueOf(
                FaceStatusCode.PROVIDER_INVALID_PARAMETER,
                "Invalid parameter[" + argName + "] .",
                cause);
      }
    }
    return  String.join("&", params);
  }



  private List<Field> getDefinitionFields(Class<?> clazz) {
    List<Field> list = (new LinkedList<>());
    Class<?> source = clazz;
    Set<String> methods;
    do {
      methods = getDefinitionMethods(source);
      for (Field field : source.getDeclaredFields()) {
        if (canResolved(field, methods)) {
          if (!field.isAccessible()) {
            field.setAccessible(true);
          }
          list.add(field);
        }
      }
    } while (!Object.class.equals(source = source.getSuperclass()));
    return list;
  }

  private Set<String> getDefinitionMethods(Class<?> clazz) {
    Method[] methods = clazz.getDeclaredMethods();
    return Arrays.stream(methods).map(Method::getName).collect(Collectors.toSet());
  }

  private boolean canResolved(Field field, Set<String> methods) {
    char[] buffer = field.getName().toCharArray();
    buffer[0] = (char) (buffer[0] - 32);
    final String suffixName = String.valueOf(buffer);
    return (methods.contains("get" + suffixName) || methods.contains("is" + suffixName));
  }

  public <T> RequestEntity<T> resolveRequestEntity(String uriString, HttpMethod httpMethod)
      throws FaceException {
    return resolveRequestEntity((T) null, uriString, httpMethod, MediaType.APPLICATION_JSON_UTF8);
  }

  public <T> RequestEntity<T> resolveRequestEntity(T input, String uriString, HttpMethod httpMethod)
      throws FaceException {
    return resolveRequestEntity(input, uriString, httpMethod, MediaType.APPLICATION_JSON_UTF8);
  }

  public <T> RequestEntity<T> resolveRequestEntity(
      T input, String uriString, HttpMethod httpMethod, MediaType mediaType) throws FaceException {
    HttpHeaders headers = (new HttpHeaders());
    if (null != mediaType) {
      headers.setContentType(mediaType);
    }
    URI uri = resolveURI(uriString);
    return (new RequestEntity<>(input, headers, httpMethod, uri));
  }

  public <T> ResponseEntity<T> resolveResponse(
         String provider, RequestEntity requestEntity, Class<T> responseType) throws FaceException {
     return resolveResponse(null, provider, requestEntity, responseType, null, null, null);
  }

  public <T> ResponseEntity<T> resolveResponse(
      String faceId, String provider, RequestEntity requestEntity, Class<T> responseType)
      throws FaceException {
    return resolveResponse(faceId, provider, requestEntity, responseType, null, null, null);
  }

  public <T> ResponseEntity<T> resolveResponse(
      String faceId, String provider, RequestEntity requestEntity, Class<T> responseType, AbstractProviderLogResultResolver providerLogResultResolver, LogConsumer<AbstractProviderLogResultResolver, RequestEntity<?>, ResponseEntity<String>> logConsumer, LogConsumer<AbstractProviderLogResultResolver, RequestEntity<?>, Exception> logExceptionConsumer) throws FaceException {
    RestTemplate restTemplate = RestTemplateUtil.getRestTemplate();
    ResponseEntity<T> responseEntity;
    long startTime = System.currentTimeMillis();
    try {
      responseEntity = restTemplate.exchange(requestEntity, responseType);
      if (null != providerLogResultResolver && null != logConsumer) {
        providerLogResultResolver.setResponseTime(System.currentTimeMillis() - startTime);
        logConsumer.accept(providerLogResultResolver, requestEntity,
            (ResponseEntity<String>) responseEntity);
      }
    } catch (Exception cause) {
      if (null != providerLogResultResolver && null != logExceptionConsumer) {
        providerLogResultResolver.setResponseTime(System.currentTimeMillis() - startTime);
        logExceptionConsumer.accept(providerLogResultResolver, requestEntity, cause);
      }
      ProviderApiFailEvent event =
          (new ProviderApiFailEvent(faceId, requestEntity.getUrl().getPath(), provider, cause));
      FaceEventPublisher.publishEvent(event);
      throw FaceException.valueOf(
          FaceStatusCode.PROVIDER_FAILED_API,
          "Error invoke api[" + requestEntity.getUrl().toString() + "] .",
          cause);
    }

    if (200 == responseEntity.getStatusCodeValue()) {
      return responseEntity;
    }

    throw FaceException.valueOf(
        FaceStatusCode.PROVIDER_FAILED_API,
        "Error response["
            + responseEntity.getStatusCodeValue()
            + ","
            + responseEntity.getBody()
            + "] on api["
            + requestEntity.getUrl().toString()
            + "] .");
  }
}
