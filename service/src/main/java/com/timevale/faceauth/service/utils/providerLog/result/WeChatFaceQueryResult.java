package com.timevale.faceauth.service.utils.providerLog.result;

import com.alibaba.fastjson.JSONObject;
import com.timevale.faceauth.service.apispace.facade.com.timevale.apispace.enums.MarkStatusEnum;
import com.timevale.faceauth.service.utils.providerLog.AbstractProviderLogResultResolver;
import com.timevale.faceauth.service.utils.providerLog.ProviderActionEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderKeyIdEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogConfiguration;
import com.timevale.mandarin.base.util.StringUtils;

import java.util.List;
import java.util.Optional;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
public class WeChatFaceQueryResult extends AbstractProviderLogResultResolver<JSONObject> {

  public WeChatFaceQueryResult(String bizId, long responseTime) {
    super(bizId, responseTime);
  }

  @Override
  public ProviderKeyIdEnum getProviderKeyId() {
    return ProviderKeyIdEnum.WeChatFace;
  }

  @Override
  public ProviderActionEnum getAction() {
    return ProviderActionEnum.QUERY;
  }

  @Override
  public String getCode(JSONObject response) {
    JSONObject root =
        Optional.ofNullable(response)
            .map(x -> x.getJSONObject(RESPONSE_CONTENT_FIELD_RESPONSE))
            .map(x -> x.getJSONObject(RESPONSE_CONTENT_FIELD_TEXT))
            .orElse(null);
    String errCode = null;
    if (null != root) {
      errCode = root.getString(RESPONSE_CONTENT_FIELD_ERRCODE);
    }
    return errCode;
  }

  @Override
  public String getMsg(JSONObject response) {
    JSONObject root =
        Optional.ofNullable(response)
            .map(x -> x.getJSONObject(RESPONSE_CONTENT_FIELD_RESPONSE))
            .map(x -> x.getJSONObject(RESPONSE_CONTENT_FIELD_TEXT))
            .orElse(null);
    String errMsg = null;
    if (null != root) {
      errMsg = root.getString(RESPONSE_CONTENT_FIELD_ERRMSG);
    }
    return errMsg;
  }

  @Override
  public String getResult(JSONObject response) {
    String code = getCode(response);
    String msg = getMsg(response);
    if(StringUtils.isAllBlank(code,msg)){
        return MarkStatusEnum.FAILURE.name();
    }
    return null;
  }

  @Override
  public List<String> getFieldEmptyMappingStautsUnknown() {
    return ProviderLogConfiguration.weChatFaceActionQueryFieldEmptyMappingStautsUnknown;
  }

  @Override
  public List<String> getCodeMsgMappingStautsFailure() {
    return ProviderLogConfiguration.weChatFaceActionQueryCodeMsgMappingStautsFailure;
  }

  @Override
  public List<String> getCodeMsgMappingStautsSuccess() {
    return ProviderLogConfiguration.weChatFaceActionQueryCodeMsgMappingStautsSuccess;
  }
}
