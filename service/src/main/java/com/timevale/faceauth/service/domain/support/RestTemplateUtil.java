package com.timevale.faceauth.service.domain.support;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.utils.SpringUtils;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import org.apache.http.conn.HttpClientConnectionManager;
import org.apache.http.impl.client.DefaultHttpRequestRetryHandler;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.LaxRedirectStrategy;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.MediaType;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;

/**
 * spring 自带httpclient
 *
 * <AUTHOR>
 * @since 2022/8/3 23:45
 */
public class RestTemplateUtil {

    public   static final  MappingJackson2HttpMessageConverter
            mappingJackson2HttpMessageConverter =
            new MappingJackson2HttpMessageConverter();

    static {
        mappingJackson2HttpMessageConverter.setSupportedMediaTypes(Arrays.asList(
                MediaType.APPLICATION_JSON, MediaType.APPLICATION_OCTET_STREAM,MediaType.APPLICATION_FORM_URLENCODED));
    }




    public static RestTemplate getRestTemplate() {
        return InnerHolder.CLIENT_RECOGNITION;
    }

    private  RestTemplate createSupportHttpClient() throws FaceException {
        RestTemplateUtil templateUtil =  new RestTemplateUtil();
        ConfigurableProperties configProp = SpringUtils.getBean(ConfigurableProperties.class);
        RestTemplateConfig templateConfig = new RestTemplateConfig(configProp);
        return templateUtil.restTemplate(templateConfig);
    }


    public RestTemplate restTemplate(HttpTemplateConfig templateConfig) {
        RestTemplateBuilder builder = new RestTemplateBuilder();
        RestTemplate restTemplate = builder.build();

        restTemplate.setRequestFactory(clientHttpRequestFactory(templateConfig));
        return restTemplate;
    }



    public HttpClientConnectionManager poolingConnectionManager(HttpTemplateConfig configProp) {
        PoolingHttpClientConnectionManager poolingConnectionManager = new PoolingHttpClientConnectionManager();
        // 连接池最大连接数
        poolingConnectionManager.setMaxTotal(
                configProp.connectionsTotal());
        // 每个主机的并发数
        poolingConnectionManager.setDefaultMaxPerRoute(
                configProp.connectionsPerHost());
        return poolingConnectionManager;
    }


    public HttpClientBuilder httpClientBuilder(HttpTemplateConfig configProp) {
        HttpClientBuilder httpClientBuilder = HttpClientBuilder.create();
        //设置HTTP连接管理器
        httpClientBuilder.setConnectionManager(poolingConnectionManager(configProp));
        httpClientBuilder.setRedirectStrategy(new LaxRedirectStrategy());
        httpClientBuilder.setRetryHandler(new DefaultHttpRequestRetryHandler(configProp.retryCount(), configProp.retryEnabled()));

        return httpClientBuilder;
    }


    public ClientHttpRequestFactory clientHttpRequestFactory(HttpTemplateConfig configProp) {
        HttpComponentsClientHttpRequestFactory clientHttpRequestFactory =
                new HttpComponentsClientHttpRequestFactory();

        clientHttpRequestFactory.setHttpClient(httpClientBuilder(configProp).build());
        // 毫秒，指客户端和服务器建立连接超时时间
        clientHttpRequestFactory.setConnectTimeout(
                configProp.connectionTimeout());
        // 毫秒，指客户端从服务器读取数据的timeout超出预期设定时间，超出后会抛出SocketTimeOutException.
        clientHttpRequestFactory.setReadTimeout(
                configProp.soTimeout());
        // 毫秒，从连接池获取连接的超时时间，如果连接池里连接都被用了，且超过设定时间,就会抛出超时异常
        clientHttpRequestFactory.setConnectionRequestTimeout(configProp.connectionManagerTimeout());
        return clientHttpRequestFactory;
    }


    private static class InnerHolder {

        static final RestTemplate CLIENT_RECOGNITION =
                new RestTemplateUtil().createSupportHttpClient();
        static {
            CLIENT_RECOGNITION.getMessageConverters().add(RestTemplateUtil.mappingJackson2HttpMessageConverter);
        }
    }
}
