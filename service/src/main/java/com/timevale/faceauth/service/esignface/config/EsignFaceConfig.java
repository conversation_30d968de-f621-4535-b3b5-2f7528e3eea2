package com.timevale.faceauth.service.esignface.config;

import com.timevale.framework.puppeteer.model.ConfigChangeEvent;
import com.timevale.framework.puppeteer.spring.annotation.PuppeteerConfigChangeListener;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Getter
@Slf4j
public class EsignFaceConfig {

  public static final String SPLIT = ",";

  /** 当前环境 */
  @Value("${esign.face.h5.url:http://realname-v3-stable-esignface.projectk8s.tsign.cn/face/action}")
  private String faceH5Url;

  @Value("${esign.mapping.bytedance.face.h5.url}")
  private String mappingBytedanceFaceH5Url;


  @Value("${esign.face.h5.url.expire:10}")
  private long faceH5UrlTimeout;

  // 动态域名配置 appId#dnsAppId,appId#dnsAppId
  @Value("${esign.face.h5.url.timeout:appId#dnsAppId}")
  private String faceH5DnsAppId;

  // 10M
  @Value("${esign.face.file.upload.max:10485760}")
  private long faceFileUploadMax;

  // 50k
  @Value("${esign.face.file.upload.min.v1:50000}")
  private long faceFileUploadMin;

  // 5M /压缩视频要求大于5M
  @Value("${esign.face.file.upload.compress.min:5242880}")
  private long faceFileUploadCompressMin;

  // 核对文件时间戳
  @Value("${esign.face.file.timestamp.check:false}")
  private boolean faceFileTimestampCheck;

  // 核对文件时间戳范围(分钟)
  @Value("${esign.face.file.timestamp.scope:30}")
  private int faceFileTimestampScope;


  // 核对文件签名
  @Value("${esign.face.file.sign.check:true}")
  private boolean faceFileSignCheck;

  @Value("${esign.face.video.compress.codec:mpeg4}")
  private   String faceVideoCompressCodec;

  @Value("${esign.face.video.compress.bit.rate:256k}")
  private   String faceVideoCompressBitRate;

  @Value("${esign.face.video.compress.frame.rate:20}")
  private   int faceVideoCompressFrameRate;



  private static Map<String, String> FACE_H5_DNSAPPID_MAP = null;


  @Value("${esign.mapping.bytedance.face.h5.v2.url:https://idverify.esign.cn}")
  private String mappingBytedanceFaceH5V2Url;


  @PuppeteerConfigChangeListener
  private void configChangeListener(ConfigChangeEvent changeEvent) {

    if (changeEvent.isChanged("esign.mapping.bytedance.face.h5.v2.url")) {
      mappingBytedanceFaceH5V2Url = changeEvent.getChange("esign.mapping.bytedance.face.h5.v2.url").getNewValue();
      log.info("PuppeteerListener update success  key ={} ,value = ", "esign.mapping.bytedance.face.h5.v2.url", mappingBytedanceFaceH5V2Url);
    }

    if (changeEvent.isChanged("esign.face.h5.url")) {
      faceH5Url = changeEvent.getChange("esign.face.h5.url").getNewValue();
    }

    if (changeEvent.isChanged("esign.mapping.bytedance.face.h5.url")) {
      mappingBytedanceFaceH5Url = changeEvent.getChange("esign.mapping.bytedance.face.h5.url").getNewValue();
    }

    if (changeEvent.isChanged("esign.face.h5.dnsAppId")) {
      faceH5DnsAppId = changeEvent.getChange("esign.face.h5.dnsAppId").getNewValue();
      FACE_H5_DNSAPPID_MAP = null;
    }

    if (changeEvent.isChanged("esign.face.h5.url.expire")) {
      String timeout = changeEvent.getChange("esign.face.h5.url.expire").getNewValue();
      faceH5UrlTimeout = Long.parseLong(timeout);
    }

    if (changeEvent.isChanged("esign.face.file.upload.max")) {
      String fileSize = changeEvent.getChange("esign.face.file.upload.max").getNewValue();
      faceFileUploadMax = Long.parseLong(fileSize);
    }

    if (changeEvent.isChanged("esign.face.file.upload.min.v1")) {
      String fileSize = changeEvent.getChange("esign.face.file.upload.min.v1").getNewValue();
      faceFileUploadMin = Long.parseLong(fileSize);
    }

    if (changeEvent.isChanged("esign.face.file.upload.compress.min")) {
      String fileSize = changeEvent.getChange("esign.face.file.upload.compress.min").getNewValue();
      faceFileUploadCompressMin = Long.parseLong(fileSize);
    }

    if (changeEvent.isChanged("esign.face.file.sign.check")) {
      String signCheck = changeEvent.getChange("esign.face.file.sign.check").getNewValue();
      faceFileSignCheck = Boolean.parseBoolean(signCheck);
    }

    if (changeEvent.isChanged("esign.face.file.timestamp.check")) {
      String timestampCheck = changeEvent.getChange("esign.face.file.timestamp.check").getNewValue();
      faceFileTimestampCheck = Boolean.parseBoolean(timestampCheck);
    }

    if (changeEvent.isChanged("esign.face.file.timestamp.scope")) {
      String timestampScope = changeEvent.getChange("esign.face.file.timestamp.scope").getNewValue();
      faceFileTimestampScope = Integer.parseInt(timestampScope);
    }
  }
}
