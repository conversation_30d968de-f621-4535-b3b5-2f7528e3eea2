package com.timevale.faceauth.service.utils.providerLog.result;

import com.timevale.faceauth.service.utils.providerLog.AbstractProviderLogResultResolver;
import com.timevale.faceauth.service.utils.providerLog.ProviderActionEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderKeyIdEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogConfiguration;
import com.timevale.mediaauth.facade.dto.response.AudioVideoDualInitializeResponse;
import java.util.List;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
public class WeChatVideoDualInitResult
    extends AbstractProviderLogResultResolver<AudioVideoDualInitializeResponse> {

  public WeChatVideoDualInitResult(String bizId, long responseTime) {
    super(bizId, responseTime);
  }

  @Override
  public ProviderKeyIdEnum getProviderKeyId() {
    return ProviderKeyIdEnum.WeChatVideoDual;
  }

  @Override
  public ProviderActionEnum getAction() {
    return ProviderActionEnum.INITIALIZATION;
  }

  @Override
  public String getCode(AudioVideoDualInitializeResponse response) {
    return null;
  }

  @Override
  public String getMsg(AudioVideoDualInitializeResponse response) {
    return null;
  }

  @Override
  public String getResult(AudioVideoDualInitializeResponse response) {
    return null;
  }

  @Override
  public List<String> getFieldEmptyMappingStautsUnknown() {
    return ProviderLogConfiguration
        .weChatVideoDualActionInitializationFieldEmptyMappingStautsUnknown;
  }

  @Override
  public List<String> getCodeMsgMappingStautsFailure() {
    return null;
  }

  @Override
  public List<String> getCodeMsgMappingStautsSuccess() {
    return null;
  }
}
