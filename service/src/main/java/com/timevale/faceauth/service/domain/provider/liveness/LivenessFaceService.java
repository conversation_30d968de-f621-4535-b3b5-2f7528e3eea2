package com.timevale.faceauth.service.domain.provider.liveness;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceResult;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.core.support.FaceQueryableExtend;
import com.timevale.faceauth.service.domain.ConfigurableFaceAuthorizationCompletedInvocationHandler;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.FaceAuthorizationPhotoResolver;
import com.timevale.faceauth.service.domain.processor.FaceRequestContext;
import com.timevale.faceauth.service.domain.processor.FaceReturnProcessor;
import com.timevale.faceauth.service.domain.processor.support.SpecifyDingTalkFaceStartProcessor;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationData;
import com.timevale.faceauth.service.domain.provider.ProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.support.AbstractProviderFaceAuthorizationResult;
import com.timevale.faceauth.service.domain.provider.support.AbstractProviderService;
import com.timevale.faceauth.service.domain.provider.support.FaceAuthorizationInitializingContext;
import com.timevale.faceauth.service.domain.repository.*;
import com.timevale.faceauth.service.domain.support.ConfigurableFaceCallbackAssert;
import com.timevale.faceauth.service.domain.support.DnsResolver;
import com.timevale.faceauth.service.domain.support.PlaintextResourceStorageService;
import com.timevale.faceauth.service.exception.ProviderException;
import com.timevale.faceauth.service.liveness.ConfigurableLivenessAuthorizationProperty;
import com.timevale.faceauth.service.liveness.event.LivenessAuthorizeCompletionEvent;
import com.timevale.faceauth.service.liveness.facade.LivenessRecognition;
import com.timevale.faceauth.service.liveness.facade.LivenessRecognitionResult;
import com.timevale.faceauth.service.liveness.support.LivenessAuthorizationBizType;
import com.timevale.faceauth.service.liveness.support.LivenessAuthorizationQueryService;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogService;
import com.timevale.faceauth.service.utils.providerLog.result.LivenessQueryResult;
import com.timevale.mandarin.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 基于活体的刷脸认证服务实现
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/3/10
 */
@Slf4j
@Component
public class LivenessFaceService extends AbstractProviderService
        implements ConfigurableFaceCallbackAssert,
        ApplicationListener<LivenessAuthorizeCompletionEvent> {

  // 默认活体认证内容获取最大递归深度
  private static final int DEFAULT_RECOGNITION_RECURSION_MAX = 1;
  @Autowired
  private  ConfigurableLivenessAuthorizationProperty configurableProperties;

  private final LivenessFaceApplyInvocationHandler applyInvocationHandler;
  private final FaceRepository faceRepository;
  private final PlaintextResourceStorageService plaintextResourceStorageService;
  private final LivenessAuthorizationQueryService livenessAuthorizationQueryService;
  private final FaceAuthorizationPhotoResolver faceAuthorizationPhotoResolver;
  @Autowired private ProviderLogService providerLogService;

  @Autowired
  public LivenessFaceService(
          FaceRepository faceRepository,
          ProviderFaceRepository providerFaceRepository,
          DnsResolver dnsResolver,
          ConfigurableProperties configurableProperties,
          FaceSwitchRepository faceSwitchRepository,
          LivenessFaceApplyInvocationHandler applyInvocationHandler,
          PlaintextResourceStorageService plaintextResourceStorageService,
          LivenessAuthorizationQueryService livenessAuthorizationQueryService,
          FaceAuthorizationPhotoResolver faceAuthorizationPhotoResolver) {
    super(
            faceRepository,
            providerFaceRepository,
            dnsResolver,
            configurableProperties,
            faceSwitchRepository);
    this.applyInvocationHandler = applyInvocationHandler;
    this.faceRepository = faceRepository;
    this.plaintextResourceStorageService = plaintextResourceStorageService;
    this.livenessAuthorizationQueryService = livenessAuthorizationQueryService;
    this.faceAuthorizationPhotoResolver = faceAuthorizationPhotoResolver;
  }

  @Override
  public String getProviderName() {
    return PROVIDER_LIVENESS;
  }

  @Override
  public String getFullName() {
    return FULL_NAME_PROVIDER_LIVENESS;
  }

  @Override
  public String resolveSimilarity(FaceResult result) {
    return "" + result.getSimilarity();
  }

  @Override
  public String resolveLiveRate(FaceResult result) {
    return "" + result.getLiveRate();
  }

  @Override
  public FaceRequestContext refreshRequest(FaceRequestContext context) {
    if (context instanceof LivenessFaceAuthRequestContext) {
      return context;
    }
    // 尝试推断用户的认证照片数据，并构建新的刷脸认证请求上下文
    UserPhoto userPhoto = faceAuthorizationPhotoResolver.resolvePhoto(context);
    return (new LivenessFaceAuthRequestContext(context, userPhoto));
  }

  @Override
  protected ProviderFaceAuthorizationData doInitialize(
          FaceAuthorizationInitializingContext initializingContext) throws FaceException {
    return applyInvocationHandler.invoke(initializingContext, this);
  }

  @Override
  protected ProviderFaceAuthorizationResult doQuery(
          String completedType,
          FaceQueryableExtend extend,
          FaceInfo faceInfo,
          ProviderFaceInfo providerFaceInfo)
          throws FaceException {
    return super.doQuery(
            ConfigurableFaceAuthorizationCompletedInvocationHandler.TYPE_COMPLETED_CALLBACK,
            extend,
            faceInfo,
            providerFaceInfo);
  }

  @Override
  protected ProviderFaceAuthorizationResult detectFaceAuthorizationResultOnReturn(
          String faceId, HttpServletRequest request) throws FaceException {
    return resolveResult(faceId);
  }

  private ProviderFaceAuthorizationResult resolveResult(String faceId) {
    LivenessRecognition recognition =
            livenessAuthorizationQueryService.getLivenessResult(
                    LivenessAuthorizationBizType.TYPE_FACE_AUTHORIZATION, faceId);
    return buildFaceResult(recognition, null);
  }

  @Override
  protected byte[] extractRequestData(HttpServletRequest request) throws FaceException {
    // obtain data with query, and do nothing .
    return null;
  }

  @Override
  protected ProviderFaceAuthorizationResult detectFaceAuthorizationResultOnCallback(
          String faceId, byte[] dataBuffer, HttpServletRequest request) throws FaceException {
    return resolveResult(faceId);
  }

  @Override
  protected AbstractProviderFaceAuthorizationResult doQueryAuthorizeResult(
          FaceQueryableExtend extend, FaceInfo faceInfo, ProviderFaceInfo providerFaceInfo)
          throws FaceException {
    if(this.checkTasKExpired(faceInfo)){
      throw FaceException.valueOf(FaceStatusCode.FACE_TASK_EXPIRED);
    }
    return this.getLivenessResult(providerFaceInfo.getOrderNo());
  }


  public LivenessFaceAuthorizationResult getLivenessResult(String orderNo){
    LivenessRecognition recognition =
            livenessAuthorizationQueryService.getLivenessResult(orderNo);
    return buildFaceResult(recognition, null);
  }

  @Override
  protected String deduceReturnUrl(
          String returnServiceUrl,
          FaceRequestContext requestContext,
          FaceReturnProcessor faceReturnProcessor) {

    if (faceReturnProcessor instanceof SpecifyDingTalkFaceStartProcessor) {
      return super.deduceReturnUrl(returnServiceUrl, requestContext, faceReturnProcessor);
    }

    return returnServiceUrl;
  }

  @Override
  protected AbstractProviderFaceAuthorizationResult resolveDoneAuthorizationResult(
          ProviderReturnInfo providerReturn,
          FaceQueryableExtend extend,
          FaceInfo faceInfo,
          ProviderFaceInfo providerFaceInfo)
          throws FaceException {
    String content = providerReturn.getData();
    if (StringUtils.isEmpty(content)) {
      return doQueryAuthorizeResult(extend, faceInfo, providerFaceInfo);
    }

    LivenessRecognition recognition = obtainResolvedRecognition(content, 0);
    if (null == recognition) {
      return doQueryAuthorizeResult(extend, faceInfo, providerFaceInfo);
    }

    return buildFaceResult(recognition, content);
  }

  private LivenessRecognition obtainResolvedRecognition(
          final String content, int currentRecursionDeep) {
    if (StringUtils.isEmpty(content)) {
      return null;
    }

    String plaintextContent;
    try {
      plaintextContent = plaintextResourceStorageService.get(content);
    } catch (Exception cause) {
      log.warn("Fail to obtain plaintext content at '" + content + "' .", cause);
      return null;
    }

    try {
      return JsonUtils.json2pojo(plaintextContent, LivenessRecognition.class);
    } catch (Exception cause) {
      log.warn(
              "Fail to instantiation at '"
                      + LivenessRecognition.class
                      + "' on '"
                      + plaintextContent
                      + "' .",
              cause);
      // 此处针对数据存在多次存储的情况，做递归处理
      if (DEFAULT_RECOGNITION_RECURSION_MAX > currentRecursionDeep) {
        log.info("Retried to obtain recognition at '" + plaintextContent + "' .");
        return obtainResolvedRecognition(plaintextContent, ++currentRecursionDeep);
      }

      log.warn("Fail to obtain recognition content at '" + content + "' .");
      return null;
    }
  }

  @Override
  public void onApplicationEvent(LivenessAuthorizeCompletionEvent event) {
    LivenessRecognition recognition = event.getData();
    LivenessFaceAuthorizationResult result = buildFaceResult(recognition, null);
    providerLogService.logLivenessQuery(
            new LivenessQueryResult(recognition.getBizId(), 0, recognition),
            recognition.getLivenessId(),
            recognition);
    super.onCompletedFaceAuthorization(
            ConfigurableFaceAuthorizationCompletedInvocationHandler.TYPE_COMPLETED_CALLBACK,
            result,
            null,
            null);
  }

  private LivenessFaceAuthorizationResult buildFaceResult(
          LivenessRecognition recognition, String recognitionContent) {
    String faceId = recognition.getBizId();
    LivenessRecognitionResult recognitionResult = recognition.getResult();
    LivenessFaceAuthMessage faceAuthMessage = this.revokerFaceResult(recognition);
    return LivenessFaceAuthorizationResult.createBuilder(getProviderName())
            .setFaceId(faceId)
            .setContent(
                    StringUtils.isEmpty(recognitionContent)
                            ? resolveResultContent(recognition)
                            : recognitionContent)
            .setCompleted(recognition.isCompleted())
            .setSuccess(recognitionResult.isSuccess())
            .setError(
                    recognitionResult.isSuccess()
                            ? null
                            : (ProviderException.buildFaceError(
                            faceAuthMessage.errCode,
                            faceAuthMessage.errMsg,
                            faceAuthMessage.errCode,
                            faceAuthMessage.errMsg)))
            .setTimestamp(
                    recognition.getCompletedTimeMillis() < 0 ? 0 : recognition.getCompletedTimeMillis())
            .setLiveRate(recognitionResult.getLiveRate())
            .setSimilarity(recognitionResult.getSimilarity())
            .setPhoto(recognitionResult.getPhoto())
            .setPhotoType(recognitionResult.getPhotoType())
            .setVideo(recognition.getProvider().getVideoAndAudio())
            .build();
  }

  private LivenessFaceAuthMessage revokerFaceResult(LivenessRecognition recognition){
    if(isRateSafeLow(recognition)){
      FaceStatusCode statusCode = FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE;
      return new LivenessFaceAuthMessage(String.valueOf(statusCode.getCode()), statusCode.getMsg());
    }
    String errCode = revokerResultCode(recognition);
    String errMsg = revokerResultMsg(recognition);
    return  new LivenessFaceAuthMessage(errCode, errMsg);

  }

  private String revokerResultCode(LivenessRecognition recognition) {

    FaceStatusCode defaultResult = FaceStatusCode.UNCOMPLETED_AUTHORIZATION;
    boolean isNotCompleted = !recognition.isCompleted();
    String resultCode =
            isNotCompleted
                    && com.timevale.mandarin.base.util.StringUtils.isBlank(recognition.getResultCode())
                    ? String.valueOf(defaultResult.getCode())
                    : recognition.getResultCode();
    return resultCode;
  }

  private String revokerResultMsg(LivenessRecognition recognition) {

    FaceStatusCode defaultResult = FaceStatusCode.UNCOMPLETED_AUTHORIZATION;
    boolean isNotCompleted = !recognition.isCompleted();
    String resultMsg =
            isNotCompleted
                    && com.timevale.mandarin.base.util.StringUtils.isBlank(recognition.getResultMsg())
                    ? defaultResult.getMsg()
                    : recognition.getResultMsg();


    return resultMsg;
  }



  //活体率过低
  public boolean isRateSafeLow(LivenessRecognition context) {
    if(context == null || context.getProvider() == null || context.getResult() == null){
      return false;
    }
    //没有完成或是成功忽略
    if(!context.getProvider().isCompleted() || context.getResult().isSuccess() ){
      return false;
    }
    //-1忽略默认值
    if(context.getResult().getLiveRate() < 0){
      return false;
    }
    boolean isRateLow = configurableProperties.getLivenessRateThreshold() > context.getResult().getLiveRate();
    return isRateLow;
  }


  private String resolveResultContent(LivenessRecognition recognition) {
    String content;
    try {
      content = JsonUtils.obj2json(recognition);
    } catch (Exception cause) {
      try {
        log.warn("Fail to serialize result at '" + recognition.getClass() + "' .", cause);
      } catch (Exception ignore) {
        // do nothing ...
      }
      return "";
    }

    String resourceKey;
    try {
      resourceKey = plaintextResourceStorageService.save(recognition.getLivenessId(), content);
    } catch (Exception cause) {
      try {
        log.warn("Fail to store plaintext at '" + content + "' .", cause);
      } catch (Exception ignore) {
        // do nothing ...
      }
      return "";
    }
    return resourceKey;
  }

  private static final class LivenessFaceAuthMessage{
    private String errCode;
    private String errMsg;

    public LivenessFaceAuthMessage(String errCode, String errMsg) {
      this.errCode = errCode;
      this.errMsg = errMsg;
    }

    public String getErrCode() {
      return errCode;
    }

    public String getErrMsg() {
      return errMsg;
    }
  }

  private static final class LivenessFaceAuthRequestContext implements FaceRequestContext {

    private final String photo;
    private final String photoType;
    private final FaceRequestContext request;

    private LivenessFaceAuthRequestContext(FaceRequestContext request, UserPhoto userPhoto) {
      this.request = request;
      this.photo = userPhoto.getPhoto();
      this.photoType = userPhoto.getPhotoType();
    }

    @Override
    public Map<String, String> bizContext() {
      return request.bizContext();
    }

    @Override
    public String getAppId() {
      return request.getAppId();
    }

    @Override
    public String getOid() {
      return request.getOid();
    }

    @Override
    public String getBizId() {
      return request.getBizId();
    }

    @Override
    public String getBizScene() {
      return request.getBizScene();
    }

    @Override
    public String getBizCode() {
      return request.getBizCode();
    }

    @Override
    public String getClientType() {
      return request.getClientType();
    }

    @Override
    public String getName() {
      return request.getName();
    }

    @Override
    public String getIdNo() {
      return request.getIdNo();
    }

    @Override
    public String getIdType() {
      return request.getIdType();
    }

    @Override
    public String getProvider() {
      return request.getProvider();
    }

    @Override
    public String getPhoto() {
      return photo;
    }

    @Override
    public String getPhotoType() {
      return photoType;
    }

    @Override
    public String getReturnUrl() {
      return request.getReturnUrl();
    }

    @Override
    public String getCallbackUrl() {
      return request.getCallbackUrl();
    }

    @Override
    public long getTimestamp() {
      return request.getTimestamp();
    }

    @Override
    public String getInput() {
      return request.getInput();
    }
  }

  @Override
  public String getProvider() {
    return getProviderName();
  }

  //  @Override
  //  public boolean isAllowed(FaceAuthorizationResult result) {
  //    return result.ifSuccess();
  //  }
}
