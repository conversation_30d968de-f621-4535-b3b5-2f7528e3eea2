package com.timevale.faceauth.service.liveness.provider.tencent;

import com.google.gson.reflect.TypeToken;
import com.tencentcloudapi.common.AbstractModel;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.JsonResponseModel;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.faceid.v20180301.FaceidClient;
import com.timevale.faceauth.service.bean.ApiResponseLogMetric;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.provider.tencent.TencentCloudClientConf;
import com.timevale.faceauth.service.enums.FaceBizContextConstants;
import com.timevale.faceauth.service.enums.FaceInnerConstants;
import com.timevale.faceauth.service.liveness.ConfigurableLivenessAuthorizationProperty;
import com.timevale.faceauth.service.liveness.LivenessAuthorizationBizException;
import com.timevale.faceauth.service.liveness.LivenessAuthorizationBizStatusCode;
import com.timevale.faceauth.service.liveness.provider.ConfigurableProviderLivenessService;
import com.timevale.faceauth.service.liveness.provider.ProviderLivenessApiInvocationContext;
import com.timevale.faceauth.service.liveness.provider.support.AbstractProviderApiInvocationHandler;
import com.timevale.faceauth.service.liveness.provider.support.ProviderApiResponse;
import com.timevale.faceauth.service.liveness.provider.support.ProviderResponseDataResolver;
import com.timevale.faceauth.service.liveness.repository.ProviderInvocationRepository;
import com.timevale.faceauth.service.liveness.resource.LivenessResourceResolver;
import com.timevale.faceauth.service.thirdparty.ThirdpartyException;
import com.timevale.faceauth.service.utils.SpringUtil;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogService;
import com.timevale.mandarin.weaver.utils.RequestContext;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Type;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;

/**
 * 腾讯云 API 调用基本处理
 *
 * <AUTHOR>
 * @copyright 2020
 * @date 2020/3/5
 */
@Slf4j
public abstract class QCloudApiInvocationHandler<
        Response extends QCloudResponse, Context extends ProviderLivenessApiInvocationContext>
    extends AbstractProviderApiInvocationHandler<QClouldApiRequest, Response, Context> {

  private final ConfigurableLivenessAuthorizationProperty livenessAuthorizationProperty;

  public QCloudApiInvocationHandler(
      ConfigurableLivenessAuthorizationProperty livenessAuthorizationProperty,
      ProviderInvocationRepository providerInvocationRepository,
      LivenessResourceResolver livenessResourceResolver) {
    super(providerInvocationRepository, livenessResourceResolver);
    this.livenessAuthorizationProperty = livenessAuthorizationProperty;
  }

  @Override
  protected Response resolveResult(
      Class<Response> responseType,
      ProviderApiResponse response,
      QClouldApiRequest request,
      Context context) {
    Object data;
    try {
      data = response.resolveData(resolveResponseDataType().getType());
    } catch (Exception cause) {
      log.warn("Failure deserialize operation .", cause);
      throw LivenessAuthorizationBizStatusCode.FAIL_INVOCATION_AT_PROVIDER.createException(
          "Error response .");
    }
    @SuppressWarnings("unchecked")
    Response result = ((JsonResponseModel<Response>) data).response;
    request.setRequestId(result.getRequestId());
    return result;
  }

  protected abstract TypeToken<JsonResponseModel<Response>> resolveResponseDataType();

  @Override
  protected QClouldApiRequest prepareRequest(Context context) {
    String api = resolveRequestAction();
    return (new QClouldApiRequest(api));
  }

  @Override
  public ProviderApiResponse call(QClouldApiRequest request, Context context) {
    if (log.isDebugEnabled()) {
      log.info(
          "Begin to be call provider on '"
              + context.getProvider()
              + "' at '"
              + request.getApi()
              + "' .");
    }
    try {
      return call0(request, context);
    } catch (Exception cause) {
      ThirdpartyException thirdpartyEx =
          new ThirdpartyException(
              ConfigurableProviderLivenessService.PROVIDER_TENCENT_PAAS,
              String.valueOf(FaceStatusCode.PROVIDER_FAILED_API.getCode()),
              FaceStatusCode.PROVIDER_FAILED_API.getMsg(),
              cause);

      return ProviderApiResponse.createBuilder().setOk(false).setError(thirdpartyEx).build();
    }
  }

  private ProviderApiResponse call0(QClouldApiRequest request, Context context) {
    Credential cred = new Credential(resolvePaaSSecretId(), resolvePaaSSecretKey());

    InnerQCloudClient client = (new InnerQCloudClient(cred, resolvePaaSRegion()));
    client.setClientProfile(SpringUtil.getBean(TencentCloudClientConf.class).getClientProfile());
    AbstractModel requestModel = resolveRequestModel(request, context);
    configApiRequest(requestModel, request, context);
    String response = client.execute(requestModel, request.getApi());
    return ProviderApiResponse.createBuilder()
        .setDataResolver(client)
        .setOk(true)
        .setContent(response)
        .build();
  }

  protected void configApiRequest(
      AbstractModel requestModel, QClouldApiRequest request, Context context) {
    Map<String, ?> parameterMap = resolveRequestParameters(requestModel);
    request.putAll(parameterMap);
  }

  protected Map<String, ?> resolveRequestParameters(AbstractModel requestModel) {
    return Collections.emptyMap();
  }

  protected String resolvePaaSSecretId() {
    return livenessAuthorizationProperty.getQCloudPaaSSecretId();
  }

  protected String resolvePaaSSecretKey() {
    return livenessAuthorizationProperty.getQCloudPaaSSecretKey();
  }

  protected String resolvePaaSRegion() {
    return livenessAuthorizationProperty.getQCloudPaaSRegion();
  }

  protected abstract AbstractModel resolveRequestModel(QClouldApiRequest request, Context context);

  protected abstract String resolveRequestAction();

  public static class InnerQCloudClient extends FaceidClient
      implements QCloudClient, ProviderResponseDataResolver {

    public InnerQCloudClient(Credential credential, String region) {
      super(credential, region);
    }

    @Override
    public String execute(AbstractModel request, String action) {
      long startTime = System.currentTimeMillis();
      try {
        String response = super.internalRequest(request, action);
        SpringUtil.getBean(ProviderLogService.class)
            .logWeChatFaceInitOrQuery(
                new ApiResponseLogMetric(
                    (String)
                        Optional.ofNullable(
                                RequestContext.get(FaceInnerConstants.FLOW_FACE_ID))
                            .orElse(""),
                    System.currentTimeMillis() - startTime),
                request,
                response,
                action);
        return response;
      } catch (Exception cause) {
        SpringUtil.getBean(ProviderLogService.class)
            .logWeChatFaceInitOrQueryWithException(
                new ApiResponseLogMetric(
                    (String)
                        Optional.ofNullable(
                                RequestContext.get(FaceInnerConstants.FLOW_FACE_ID))
                            .orElse(""),
                    System.currentTimeMillis() - startTime),
                request,
                cause,
                action);
        log.warn("Fail to execute request .", cause);
        throw LivenessAuthorizationBizStatusCode.FAIL_INVOCATION_AT_PROVIDER.createException(
            "Fail to execute request .");
      }
    }

    @Override
    public <T> T resolveData(String content, Class<T> dataType)
        throws LivenessAuthorizationBizException {
      try {
        return gson.fromJson(content, dataType);
      } catch (Exception cause) {
        log.warn("Failure deserialize type[" + dataType + "] on data[" + content + "] .", cause);
        throw LivenessAuthorizationBizStatusCode.FAIL_INNER.createException("Error response .");
      }
    }

    @Override
    public Object resolveData(String content, Type dataType)
        throws LivenessAuthorizationBizException {
      try {
        return gson.fromJson(content, dataType);
      } catch (Exception cause) {
        log.warn("Failure deserialize type[" + dataType + "] on data[" + content + "] .", cause);
        throw LivenessAuthorizationBizStatusCode.FAIL_INNER.createException("Error response .");
      }
    }
  }
}
