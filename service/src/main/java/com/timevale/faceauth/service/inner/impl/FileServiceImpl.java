package com.timevale.faceauth.service.inner.impl;

import com.timevale.faceauth.service.bean.FilePostTransferUrlResult;
import com.timevale.faceauth.service.bean.FileTransferUrlModel;
import com.timevale.faceauth.service.bean.FileTransferUrlResult;
import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.inner.FileService;
import com.timevale.faceauth.service.utils.HttpUtilPlus;
import com.timevale.filesystem.common.service.api.FileSystemService;
import com.timevale.filesystem.common.service.query.BaseInput;
import com.timevale.filesystem.common.service.query.GetDownloadUrlInput;
import com.timevale.filesystem.common.service.query.GetSignUrlInput;
import com.timevale.filesystem.common.service.query.GetTokenInput;
import com.timevale.filesystem.common.service.result.GetFileInfoResult;
import com.timevale.filesystem.common.service.result.GetSignUrlResult;
import com.timevale.filesystem.common.service.result.GetTokenResult;
import com.timevale.mandarin.base.exception.BaseRuntimeException;
import com.timevale.mandarin.base.util.StringUtils;
import esign.utils.exception.ErrorsDiscriptor;
import esign.utils.exception.SuperException;
import esign.utils.httpclient.HttpHeaderMgmt;
import esign.utils.response.BaseResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

@Service
public class FileServiceImpl implements FileService {

  private static final Logger logger = LoggerFactory.getLogger(FileServiceImpl.class);
  /** 上传文件指定文件类型为 octet-stream */
  private static final String CONTENT_TYPE_OCTET_STREAM = "application/octet-stream";

  private static final String HTTP_URL_SPLIT = "://";



  @Value("${file.system.is.oss.internal}")
  private boolean isInternal;

  @Value("${file.system.transfer.url.http:https}")
  private String transferUrlHttp;

  @Value("${file.system.is.oss.downloadurl.expire:3600000}")
  private int downloadUrlExpire ;

  @Autowired private FileSystemService fileSystemService;

  /**
   * md5字节码
   *
   * @param src
   * @return
   */
  public static byte[] md5Encrypt(byte[] src) {
    try {
      MessageDigest md = MessageDigest.getInstance("MD5");
      return md.digest(src);
    } catch (NoSuchAlgorithmException ex) {
      ex.printStackTrace();
    }
    return null;
  }

  @Override
  public String getPhotoBase64(String fileKey) {
    if (logger.isDebugEnabled()) {
      logger.info(" get photo base64, fileKey={}", fileKey);
    }

    try {
      return downloadText(fileKey);
    }  catch (BaseRuntimeException e) {
      logger.warn("get photo base64 data fail : {}", e);
      throw new FaceException(FaceStatusCode.ERR_INNER, e.getMessage());
    }
  }

  @Override
  public String uploadPhotoAndVideo(String fileName, String base64Content) {
    long startTime = System.currentTimeMillis();
    GetSignUrlInput getSignUrlInput = new GetSignUrlInput();
    // 服务商0-阿里云，1-百度云，可选，
    // 默认值：0 --2019年10月15日戍电增加 20-普通文件加密 21-分块文件加密 22-分片文件加密 23-分片混淆文件加密
    getSignUrlInput.setAgentType(ConfigurableProperties.getInstance().getOssUploadAgentType());

    String contentMd5 = Base64Utils.encodeToString(md5Encrypt(base64Content.getBytes()));

    getSignUrlInput.setMd5(contentMd5);
    getSignUrlInput.setContentType(CONTENT_TYPE_OCTET_STREAM);
    getSignUrlInput.setFileName(fileName);
    getSignUrlInput.setInternal(isInternal);
    getSignUrlInput.setProjectId(ConfigurableProperties.getInstance().getProjectId());

    try {
      // 获取文件上传信息
      GetSignUrlResult uploadInfo = fileSystemService.getSignUrl(getSignUrlInput);

      HttpHeaderMgmt mgmt = new HttpHeaderMgmt();
      mgmt.addHeader("Content-MD5", contentMd5);
      mgmt.addHeader("Content-Type", CONTENT_TYPE_OCTET_STREAM);
      BaseResponse response =
              HttpUtilPlus.upload(uploadInfo.getUrl(), base64Content.getBytes(), mgmt, BaseResponse.class);
      if (response.getErrCode() != ErrorsDiscriptor.Success.code()) {
        throw new FaceException(FaceStatusCode.ERR_INNER,response.getMsg());
      }

      return uploadInfo.getFileKey();
    } catch (SuperException e) {
      logger.error("upload uploadPhotoAndVideo fail : {}", e.getMessage());
      throw new FaceException(FaceStatusCode.ERR_INNER, e.getMessage());
    } catch (BaseRuntimeException e) {
      logger.error("get file upload url fail : {}", e);
      throw new FaceException(FaceStatusCode.ERR_INNER, e.getMessage());
    } finally {
      logger.info("upload uploadPhotoAndVideo cost : {}  ms", (System.currentTimeMillis()-startTime));
    }
  }

  @Override
  public FileTransferUrlResult transferUrl(FileTransferUrlModel model) {


    GetSignUrlInput urlParam = new GetSignUrlInput();
    urlParam.setContentType(model.getContentType());
    urlParam.setMd5(model.getContentMd5());
    urlParam.setContentLength(model.getFileSize());
    urlParam.setFileName(model.getFileName());
    urlParam.setProjectId(ConfigurableProperties.getInstance().getProjectId());
    urlParam.setNeedHttps(true);
    GetSignUrlResult result = fileSystemService.getSignUrl(urlParam);

    String fileKey = result.getFileKey();
    String url = convertTransUrl(result.getUrl());

    FileTransferUrlResult response = new FileTransferUrlResult();
    response.setFileKey(fileKey);
    response.setUploadUrl(url);
    return response;
  }

  @Override
  public FilePostTransferUrlResult transferPostUrl(FileTransferUrlModel model) {

    GetTokenInput tokenInput = new GetTokenInput();
    // 服务商0-阿里云，1-百度云，可选，
    // 默认值：0 --2019年10月15日戍电增加 20-普通文件加密 21-分块文件加密 22-分片文件加密 23-分片混淆文件加密
    tokenInput.setAgentType(model.isEncryption() ?
            ConfigurableProperties.getInstance().getOssUploadAgentType() : 0);
    tokenInput.setContentType(model.getContentType());
    tokenInput.setMd5(model.getContentMd5());
    tokenInput.setFileName(model.getFileName());
    tokenInput.setProjectId(ConfigurableProperties.getInstance().getProjectId());
    tokenInput.setInternal(false);

    GetTokenResult tokenResult = fileSystemService.getToken(tokenInput);

    String host = convertTransUrl(tokenResult.getHost());
    return FilePostTransferUrlResult
            .builder()
            .accessId(tokenResult.getAccessId())
            .dir(tokenResult.getDir())
            .fileKey(tokenResult.getFileKey())
            .expire(tokenResult.getExpire())
            .host(host)
            .policy(tokenResult.getPolicy())
            .signature(tokenResult.getSignature())
            .build();
  }


  private String convertTransUrl(String url){
    return transferUrlHttp + url.substring(url.indexOf(HTTP_URL_SPLIT));
  }

  @Override
  public GetFileInfoResult getFileInfo(String fileKey) {
    BaseInput input = new BaseInput();
    input.setFileKey(fileKey);
    return fileSystemService.getFileInfo(input);
  }

  @Override
  public String getDownloadUrl(String fileKey){
    return getDownloadUrl(fileKey, false);
  }

  @Override
  public String getDownloadInternalUrl(String fileKey) {
    return getDownloadUrl(fileKey, isInternal);
  }

  private String getDownloadUrl(String fileKey, boolean isInternal){
    GetDownloadUrlInput input = new GetDownloadUrlInput();
    input.setFileKey(fileKey);
    input.setExpire(downloadUrlExpire);
    input.setInternal(isInternal);
    return fileSystemService.getDownloadUrl(input).getUrl();
  }

  @Override
  public byte[] download(String fileKey) {
    if (StringUtils.isNotBlank(fileKey)) {
      String downloadUrl = getDownloadInternalUrl(fileKey);
      return HttpUtilPlus.getAsResponseBytes(downloadUrl);
    }
    return null;
  }

  @Override
  public String downloadText(String fileKey) {
    byte[] data = download(fileKey);
    return new String(data);
  }


}
