package com.timevale.faceauth.service.domain.provider.tencent;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/21 12
 */
class TencentCloudCertificationQueryFileResultType {

  private final byte value;

  private TencentCloudCertificationQueryFileResultType(byte value) {
    this.value = value;
  }

  byte getValue() {
    return value;
  }

  /** 不查询资源 */
  static final TencentCloudCertificationQueryFileResultType GET_NONE =
          (new TencentCloudCertificationQueryFileResultType((byte) 0x00));

  /** 查询视频和照片 */
  static final TencentCloudCertificationQueryFileResultType GET_VIDEO_AND_PHOTO =
      (new TencentCloudCertificationQueryFileResultType((byte) 0x01));


  /** 仅查询照片 */
  static final TencentCloudCertificationQueryFileResultType GET_PHOTO_ONLY =
          (new TencentCloudCertificationQueryFileResultType((byte) 0x02));

  /** 仅查询视频 */
  static final TencentCloudCertificationQueryFileResultType GET_VIDEO_ONLY =
      (new TencentCloudCertificationQueryFileResultType((byte) 0x03));

}
