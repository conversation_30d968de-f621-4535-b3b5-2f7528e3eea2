package com.timevale.faceauth.service.domain.provider.tencent;

import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.mandarin.base.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2021-11-09 20:14
 */
public interface TencentWebAccessHolder {

    String getWebAppId();

    String getWebAppSecret();

    boolean isDoRefresh();

    default String  getTencentCloudCertificationIdAscApi(){
        return ConfigurableProperties.getInstance().getTencentCloudCertificationIdAscApi();
    }

    default String getTencentCloudCertificationIdAscIframeApi(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            return ConfigurableProperties.getInstance().getTencentCloudCertificationIframeApi();
        } else {
            return ConfigurableProperties.getInstance().getTencentCloudCertificationIframeApi() + "?orderNo=" + orderNo;
        }
    }
}
