package com.timevale.faceauth.service.domain.provider.tencent;

import com.timevale.faceauth.service.core.FaceException;
import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.domain.ConfigurableProperties;
import com.timevale.faceauth.service.domain.provider.support.ELock;
import com.timevale.faceauth.service.domain.provider.support.ELockExe;
import com.timevale.faceauth.service.domain.provider.support.RestTemplateRequestResolver;
import com.timevale.framework.tedis.util.TedisUtil;
import com.timevale.mandarin.base.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @copyright 2019
 * @date 2019/10/21 18
 */
@Slf4j
@Component
public class TencentCloudTicketResolver {

  private static final String TICKET_KEY_PRE = "face06:tencent:cloud:ticket:";

  private static final String TICKET_LOCK = TICKET_KEY_PRE + "lock";
  //  timeout a hour
  private static final int TICKET_KEY_PUT_TIMEOUT_SECONDS = 50 * 60;

  private final ConfigurableProperties properties;
  private final RestTemplateRequestResolver requestResolver;

  @Autowired
  public TencentCloudTicketResolver(
          ConfigurableProperties properties, RestTemplateRequestResolver requestResolver) {
    this.properties = properties;
    this.requestResolver = requestResolver;
  }

  public TencentCloudTicket resolveDefaultTicket(String faceId,TencentWebAppIdVersion appIdVersion)
          throws FaceException {

    return getAndRefreshIfExpired(faceId,appIdVersion);
  }

  private TencentCloudTicket obtainTicketFromCache(TencentWebAppIdVersion appIdVersion) {
    Object data;
    String ticketKey = getTicketKey(appIdVersion);
    try {
      data = TedisUtil.tedis().string().get(ticketKey);
    } catch (Exception cause) {
      log.warn("Fail get key[" + ticketKey + "] from cache .", cause);
      return null;
    }
    if (null == data) {
      log.warn("Not found key[" + ticketKey + "] from cache .");
      return null;
    }

    String content = String.valueOf(data);
    try {
      return JsonUtils.json2pojo(content, TencentCloudTicket.class);
    } catch (Exception cause) {
      log.warn(
              "Data[" + content + "] could not instantiate to type[" + TencentCloudTicket.class + "] .",
              cause);
      return null;
    }
  }

  private TencentCloudTicket getAndRefreshIfExpired(String faceId, TencentWebAppIdVersion appIdVersion)
          throws FaceException {
    TencentCloudAccessToken accessToken = TencentCloudUtil.getAccessToken(appIdVersion);
    TencentCloudTicket ticket = obtainTicketFromCache(appIdVersion);
    if (null == ticket || isExpired(accessToken, ticket)) {
      return refreshTicked( faceId, accessToken, appIdVersion);
    }

    return ticket;
  }

  public TencentCloudTicket refreshTicked(
          String faceId, TencentCloudAccessToken accessToken, TencentWebAppIdVersion appIdVersion)
          throws FaceException {
    return new ELock<TencentCloudTicket>()
            .lock(
                    TICKET_LOCK,
                    new ELockExe() {
                      @Override
                      public TencentCloudTicket process() {
                        TencentCloudTicket ticket =
                                resolveTicket(
                                        faceId, accessToken, TencentCloudTicketType.TYPE_SIGN, appIdVersion);
                        putTicketToCache(ticket, appIdVersion);
                        return ticket;
                      }

                      @Override
                      public TencentCloudTicket get() {
                        return obtainTicketFromCache(appIdVersion);
                      }
                    });
  }

  private boolean isExpired(TencentCloudAccessToken accessToken, TencentCloudTicket ticket) {
    if (accessToken == null || ticket == null) {
      return true;
    }
    return ticket.isExpiredToken(accessToken)
            || (ticket.expired(properties.getTencentCloudTokenExpiredLeadTime()));
  }

  /**
   * 获取腾讯云刷脸访问授权的 ticket令牌
   *
   * @param faceId 刷脸任务Id
   * @param accessToken 刷脸访问授权token
   * @param ticketType ticket令牌类型
   * @param appIdVersion web appId版本
   * @return
   */
  TencentCloudTicket resolveTicket(
          String faceId,
          TencentCloudAccessToken accessToken,
          TencentCloudTicketType ticketType,
          TencentWebAppIdVersion appIdVersion) {
    accessToken =
            (null == accessToken ? TencentCloudUtil.getAccessToken(appIdVersion) : accessToken);
    long startTime = System.currentTimeMillis();
    TencentCloudTicketRequest request =
            TencentCloudTicketRequest.createBuilder()
                    .setAccess_token(accessToken.getAccess_token())
                    .setApp_id(appIdVersion.getAccessHolder().getWebAppId())
                    .setType(ticketType)
                    .setUserId(faceId)
                    .build();
    String queryString = requestResolver.resolveQueryString(request);
    String uriStr = properties.getTencentCloudTicketApi() + "?" + queryString;
    RequestEntity<String> requestEntity =
            requestResolver.resolveRequestEntity(
                    (String) null, uriStr, HttpMethod.GET, (MediaType) null);
    ResponseEntity<String> responseEntity =
            requestResolver.resolveResponse(
                    faceId, appIdVersion.getProviderName(), requestEntity, String.class);
    TencentCloudTicketResponse response;
    try {
      response = JsonUtils.json2pojo(responseEntity.getBody(), TencentCloudTicketResponse.class);
    } catch (Exception cause) {
      throw FaceException.valueOf(
              FaceStatusCode.PROVIDER_ERROR_API_RESULT_SYNTAX,
              "Data["
                      + responseEntity.getBody()
                      + "] could not instantiate type ["
                      + TencentCloudTicket.class
                      + "] .");
    }
    TencentCloudTicket[] tickets = response.getTicketsIfAbsent();
    if (null == tickets) {
      throw FaceException.valueOf(FaceStatusCode.PROVIDER_ERROR_API_RESPONSE, "Not found ticket .");
    }
    TencentCloudTicket ticket =
            Arrays.stream(tickets).filter(Objects::nonNull).findFirst().orElse(null);
    if (null == ticket) {
      throw FaceException.valueOf(FaceStatusCode.PROVIDER_ERROR_API_RESPONSE, "Not found ticket .");
    }
    ticket.setAccess_token(accessToken.getAccess_token());
    long endTime = System.currentTimeMillis();
    log.info(
            "tencent cloud get {} ticket={},cost={} ms",
            ticketType.getValue(),
            ticket,
            (endTime - startTime));
    return ticket;
  }

  @SuppressWarnings("unchecked")
  private void putTicketToCache(TencentCloudTicket ticket, TencentWebAppIdVersion appIdVersion)
          throws FaceException {
    String data;
    try {
      data = JsonUtils.obj2json(ticket);
    } catch (Exception cause) {
      throw FaceException.valueOf(
              FaceStatusCode.PROVIDER_FAILED_CACHED_TICKET, "Error serialize ticket .", cause);
    }
    try {
      TedisUtil.tedis()
              .string()
              .set(
                      getTicketKey(appIdVersion), data, deduceTicketCacheTimeout(ticket), TimeUnit.SECONDS);
    } catch (Exception cause) {
      throw FaceException.valueOf(
              FaceStatusCode.PROVIDER_FAILED_CACHED_TICKET,
              "Error cached ticket[" + ticket + "] .",
              cause);
    }
  }

  private long deduceTicketCacheTimeout(TencentCloudTicket ticket) {
    return (0 >= ticket.getExpire_in() ? TICKET_KEY_PUT_TIMEOUT_SECONDS : ticket.getExpire_in());
  }

  public String getTicketKey(TencentWebAppIdVersion appIdVersion) {
    return TICKET_KEY_PRE + appIdVersion.getAccessHolder().getWebAppId();
  }
}
