package com.timevale.faceauth.service.utils.providerLog.result;

import com.timevale.faceauth.service.utils.providerLog.AbstractProviderLogResultResolver;
import com.timevale.faceauth.service.utils.providerLog.ProviderActionEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderKeyIdEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogConfiguration;
import com.timevale.mediaauth.facade.dto.response.AudioVideoDualQueryResponse;
import java.util.List;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
public class WeChatVideoDualQueryResult
    extends AbstractProviderLogResultResolver<AudioVideoDualQueryResponse> {

  public WeChatVideoDualQueryResult(String bizId, long responseTime) {
    super(bizId, responseTime);
  }

  @Override
  public ProviderKeyIdEnum getProviderKeyId() {
    return ProviderKeyIdEnum.WeChatVideoDual;
  }

  @Override
  public ProviderActionEnum getAction() {
    return ProviderActionEnum.QUERY;
  }

  @Override
  public String getCode(AudioVideoDualQueryResponse response) {
    return null;
  }

  @Override
  public String getMsg(AudioVideoDualQueryResponse response) {
    return null;
  }

  @Override
  public String getResult(AudioVideoDualQueryResponse response) {
    return null;
  }

  @Override
  public List<String> getFieldEmptyMappingStautsUnknown() {
    return ProviderLogConfiguration.weChatVideoDualActionQueryFieldEmptyMappingStautsUnknown;
  }

  @Override
  public List<String> getCodeMsgMappingStautsFailure() {
    return ProviderLogConfiguration.weChatVideoDualActionQueryCodeMsgMappingStautsFailure;
  }

  @Override
  public List<String> getCodeMsgMappingStautsSuccess() {
    return ProviderLogConfiguration.weChatVideoDualActionQueryCodeMsgMappingStautsSuccess;
  }
}
