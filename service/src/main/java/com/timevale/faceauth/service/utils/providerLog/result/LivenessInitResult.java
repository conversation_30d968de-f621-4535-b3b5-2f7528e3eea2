package com.timevale.faceauth.service.utils.providerLog.result;

import com.timevale.faceauth.service.liveness.facade.LivenessApplyResult;
import com.timevale.faceauth.service.utils.providerLog.AbstractProviderLogResultResolver;
import com.timevale.faceauth.service.utils.providerLog.ProviderActionEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderKeyIdEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogConfiguration;
import java.util.List;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
public class LivenessInitResult extends AbstractProviderLogResultResolver<LivenessApplyResult> {

  public LivenessInitResult(String bizId, long responseTime) {
    super(bizId, responseTime);
  }

  @Override
  public ProviderKeyIdEnum getProviderKeyId() {
    return ProviderKeyIdEnum.Liveness;
  }

  @Override
  public ProviderActionEnum getAction() {
    return ProviderActionEnum.INITIALIZATION;
  }

  @Override
  public String getCode(LivenessApplyResult response) {
    return null;
  }

  @Override
  public String getMsg(LivenessApplyResult response) {
    return null;
  }

  @Override
  public String getResult(LivenessApplyResult response) {
    return null;
  }

  @Override
  public List<String> getFieldEmptyMappingStautsUnknown() {
    return ProviderLogConfiguration.livenessActionInitializationFieldEmptyMappingStautsUnknown;
  }

  @Override
  public List<String> getCodeMsgMappingStautsFailure() {
    return null;
  }

  @Override
  public List<String> getCodeMsgMappingStautsSuccess() {
    return null;
  }
}
