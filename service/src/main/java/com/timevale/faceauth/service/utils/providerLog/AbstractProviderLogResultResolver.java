package com.timevale.faceauth.service.utils.providerLog;

import com.alibaba.fastjson.JSONObject;
import com.timevale.faceauth.service.apispace.facade.com.timevale.apispace.api.ThirdpartyApiService;
import com.timevale.faceauth.service.apispace.facade.com.timevale.apispace.enums.MarkStatusEnum;
import com.timevale.faceauth.service.apispace.facade.com.timevale.apispace.request.ApiResponseBaseMetric;
import com.timevale.faceauth.service.apispace.facade.com.timevale.apispace.response.CodeMsgMappingResponse;
import com.timevale.faceauth.service.utils.SpringUtil;
import com.timevale.mandarin.base.util.CollectionUtils;
import com.timevale.mandarin.base.util.JsonUtils;
import com.timevale.mandarin.base.util.StringUtils;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>">jiuchen</a>
 */
public abstract class AbstractProviderLogResultResolver<R> implements ProviderLogConstants {

  private String bizId;

  private long responseTime;

  public AbstractProviderLogResultResolver(String bizId, long responseTime) {
    this.bizId = bizId;
    this.responseTime = responseTime;
  }

  public void completed(R response) {
    SpringUtil.getBean(ThirdpartyApiService.class).pushResponse(resolver(response));
  }

  public void exception(Exception ex) {
    SpringUtil.getBean(ThirdpartyApiService.class).pushResponse(resolverException(ex));
  }

  public ApiResponseBaseMetric resolver(R response) {
    return ApiResponseBaseMetric.builder()
        .bizId(getBizId())
        .firstSpaceId(getProviderKeyId().getFirstSpaceId())
        .secondSpaceId(getProviderKeyId().getSecondSpaceId())
        .providerId(getProviderKeyId().getProviderId())
        .providerKey(getProviderKeyId().getProviderKey())
        .action(getAction().name())
        .responseTime(getResponseTime())
        .providerCode(getCode(response))
        .providerMsg(getMsg(response))
        .markStatus(deduceMarkStatusByResponse(response))
        .build();
  }

  public ApiResponseBaseMetric resolverException(Exception ex) {
    return ApiResponseBaseMetric.builder()
        .bizId(getBizId())
        .firstSpaceId(getProviderKeyId().getFirstSpaceId())
        .secondSpaceId(getProviderKeyId().getSecondSpaceId())
        .providerId(getProviderKeyId().getProviderId())
        .providerKey(getProviderKeyId().getProviderKey())
        .action(getAction().name())
        .responseTime(getResponseTime())
        .markStatus(
            ex instanceof IOException
                ? MarkStatusEnum.TIMEOUT.name()
                : MarkStatusEnum.UNKNOWN.name())
        .build();
  }

  public String getBizId() {
    return bizId;
  }

  public long getResponseTime() {
    return responseTime;
  }

  public void setResponseTime(long responseTime) {
    this.responseTime = responseTime;
  }

  protected abstract ProviderKeyIdEnum getProviderKeyId();

  protected abstract ProviderActionEnum getAction();

  protected abstract String getCode(R r);

  protected abstract String getMsg(R r);

  protected abstract String getResult(R r);

  /**
   * 供应商响应信息中缺少必要字段，标记为未知状态
   *
   * @return
   */
  protected abstract List<String> getFieldEmptyMappingStautsUnknown();

  /**
   * 供应商响应信息中code+msg 标记为失败状态（如果msg过长可以截取一部分）
   *
   * @return
   */
  protected abstract List<String> getCodeMsgMappingStautsFailure();

  /**
   * 供应商响应信息中code+msg 标记为成功状态（如果msg过长可以截取一部分）
   *
   * @return
   */
  protected abstract List<String> getCodeMsgMappingStautsSuccess();

  /**
   * 根据供应商响应信息推测 标记状态 的通用方法，特殊情况支持覆写
   *
   * @param response
   * @return
   */
  protected String deduceMarkStatusByResponse(R response) {
    return deduceMarkStatusByResponseBase(
        JsonUtils.obj2json(response),
        getFieldEmptyMappingStautsUnknown(),
        getCodeMsgMappingStautsFailure(),
        getCodeMsgMappingStautsSuccess(),
        getCode(response),
        getMsg(response),
        getResult(response));
  }

  private String deduceMarkStatusByResponseBase(
      String responseContent,
      List<String> unknownFields,
      List<String> failureCodeMsgs,
      List<String> successCodeMsgs,
      String currentCode,
      String currentMsg,
      String currentResult) {
    // 供应商非 通用显示，取内部实现结果 直接返回
    if(StringUtils.isNotBlank(currentResult)){
      return currentResult;
    }
    // 首先检查是否缺少必备字段，顺序不能调整
    if (CollectionUtils.isNotEmpty(unknownFields)
        && fieldEmptyMappingStautsUnknown(responseContent, unknownFields)) {
      return MarkStatusEnum.UNKNOWN.name();
    }
    // 部分接口并不通过code、msg表达成功，而是返回了必备字段就算成功
    if (StringUtils.isBlank(currentCode) && StringUtils.isBlank(currentMsg)) {
      return MarkStatusEnum.SUCCESS.name();
    }
    // 当出现未适配过的code、msg时，可以视情况判断是否是正常结果，如果是正常结果，可将code+msg配置到名单中
    if (CollectionUtils.isNotEmpty(failureCodeMsgs)
        && codeMsgHit(currentCode, currentMsg, failureCodeMsgs)) {
      return MarkStatusEnum.FAILURE.name();
    }
    // 成功的情况往往code+msg是不会变化的，至少是可枚举的
    if (CollectionUtils.isNotEmpty(successCodeMsgs)
        && codeMsgHit(currentCode, currentMsg, successCodeMsgs)) {
      return MarkStatusEnum.SUCCESS.name();
    }
    // 以上的名单，尤其是标记失败的列表配置存在枚举过多的可能性，当首次写入采集表之后，就可以从以上的列表中删除了，读取表中的标记状态
    CodeMsgMappingResponse codeMsgMappingResponse =
        SpringUtil.getBean(ThirdpartyApiService.class)
            .getCodeMsgMappingByProviderId(
                getProviderKeyId().getProviderId(), currentCode, currentMsg);
    if (null != codeMsgMappingResponse
        && StringUtils.isNotBlank(codeMsgMappingResponse.getMappingMarkStatus())) {
      return codeMsgMappingResponse.getMappingMarkStatus();
    }
    return MarkStatusEnum.UNKNOWN.name();
  }

  private boolean fieldEmptyMappingStautsUnknown(String responseContent, List<String> fields) {
    if (StringUtils.isBlank(responseContent) || CollectionUtils.isEmpty(fields)) {
      return false;
    }
    for (String field : fields) {
      String[] jsonField = field.split("\\.");
      String nextJSONObject = responseContent;
      for (int i = 0; i < jsonField.length; i++) {
        nextJSONObject = JSONObject.parseObject(nextJSONObject).getString(jsonField[i]);
        if (StringUtils.isBlank(nextJSONObject)) {
          return true;
        }
      }
    }
    return false;
  }

  private boolean codeMsgHit(String code, String msg, List<String> codeMsgs) {
    if (StringUtils.isBlank(code) && StringUtils.isBlank(msg)) {
      return false;
    }
    if (CollectionUtils.isEmpty(codeMsgs)) {
      return false;
    }
    String currentCodeMsg = "";
    if (StringUtils.isNotBlank(code)) {
      currentCodeMsg += code;
    }
    if (StringUtils.isNotBlank(msg)) {
      currentCodeMsg += msg;
    }
    for (String codeMsg : codeMsgs) {
      if (currentCodeMsg.contains(codeMsg)) {
        return true;
      }
    }
    return false;
  }
}
