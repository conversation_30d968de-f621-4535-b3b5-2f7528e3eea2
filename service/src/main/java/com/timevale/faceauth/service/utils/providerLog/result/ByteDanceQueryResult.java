package com.timevale.faceauth.service.utils.providerLog.result;

import com.timevale.faceauth.service.domain.provider.bytedance.domain.FaceQueryResponse;
import com.timevale.faceauth.service.utils.providerLog.AbstractProviderLogResultResolver;
import com.timevale.faceauth.service.utils.providerLog.ProviderActionEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderKeyIdEnum;
import com.timevale.faceauth.service.utils.providerLog.ProviderLogConfiguration;

import java.util.List;

/** <AUTHOR> href="mailto:<EMAIL>">jiuchen</a> */
public class ByteDanceQueryResult extends AbstractProviderLogResultResolver<FaceQueryResponse> {

  public ByteDanceQueryResult(String bizId, long responseTime) {
    super(bizId, responseTime);
  }

  @Override
  public ProviderKeyIdEnum getProviderKeyId() {
    return ProviderKeyIdEnum.ByteDance;
  }

  @Override
  public ProviderActionEnum getAction() {
    return ProviderActionEnum.QUERY;
  }

  @Override
  public String getCode(FaceQueryResponse response) {
    return String.valueOf(null == response.getData().getVerifyAlgorithmBaseResp() ? "" : response.getData().getVerifyAlgorithmBaseResp().getStatusCode());
  }

  @Override
  public String getMsg(FaceQueryResponse response) {
    return String.valueOf(response.getData().getResult());
  }

  @Override
  public String getResult(FaceQueryResponse response) {
    return null;
  }

  @Override
  public List<String> getFieldEmptyMappingStautsUnknown() {
    return ProviderLogConfiguration.byteDanceActionQueryFieldEmptyMappingStautsUnknown;
  }

  @Override
  public List<String> getCodeMsgMappingStautsFailure() {
    return ProviderLogConfiguration.byteDanceActionQueryCodeMsgMappingStautsFailure;
  }

  @Override
  public List<String> getCodeMsgMappingStautsSuccess() {
    return ProviderLogConfiguration.byteDanceActionQueryCodeMsgMappingStautsSuccess;
  }
}
