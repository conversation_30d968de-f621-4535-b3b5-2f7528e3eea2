package com.timevale.faceauth.service.liveness.provider.esign.support;

import com.timevale.faceauth.service.core.FaceStatusCode;
import com.timevale.faceauth.service.exception.ProviderErrorEnum;
import com.timevale.facedetectalgo.service.enums.DetectResultCodeEnum;
import com.timevale.infoauth.service.enums.InfoAuthErrorCodeEnum;

/**
 * e签宝库照片比对供应商错误码适配
 *
 * <AUTHOR>
 * @since 2020-11-09 23:16
 */
public enum FaceActionDetectResultCodeAdapter implements ProviderErrorEnum {

  // 活体通用返回码
  OK("成功", DetectResultCodeEnum.SUCCESS, FaceStatusCode.OK),
  ERROR("系统错误", DetectResultCodeEnum.SYSTEM_ERROR, FaceStatusCode.PROVIDER_FAILED_API),

  // 动作活体返回码
  ATTACK_CHECK_IS_NOT_PASS(
      "防攻击检测不通过",
      DetectResultCodeEnum.ATTACK_CHECK_IS_NOT_PASS,
      FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),

  BRIGHTNESS_CHECK_IS_NOT_PASS(
      "光照太强或太弱",
      DetectResultCodeEnum.BRIGHTNESS_CHECK_IS_NOT_PASS,
      FaceStatusCode.LIVENESS_BRIGHTNESS_CHECK_NOT_PASS),

  RESOLUTION_CHECK_IS_NOT_PASS(
      "分辨率太低",
      DetectResultCodeEnum.RESOLUTION_CHECK_IS_NOT_PASS,
      FaceStatusCode.LIVENESS_RESOLUTION_CHECK_NOT_PASS),

  CLARITY_CHECK_IS_NOT_PASS(
      "人脸不清晰",
      DetectResultCodeEnum.CLARITY_CHECK_IS_NOT_PASS,
      FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),

  INTEGRITY_CHECK_IS_NOT_PASS(
      "人脸不完整，距离屏幕太远或太近",
      DetectResultCodeEnum.INTEGRITY_CHECK_IS_NOT_PASS,
      FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),

  MASK_CHECK_IS_NOT_PASS(
      "面部被遮挡",
      DetectResultCodeEnum.MASK_CHECK_IS_NOT_PASS,
      FaceStatusCode.LIVENESS_INTEGRITY_CHECK_NOT_PASS),

  MOUTH_OPEN_IS_NOT_FOUND(
      "未检测到张嘴动作，请保证清晰完整人脸完成动作",
      DetectResultCodeEnum.MOUTH_OPEN_IS_NOT_FOUND,
      FaceStatusCode.LIVENESS_MOUTH_OPEN_NOT_FOUND),

  EYE_BLINK_IS_NOT_FOUND(
      "未检测到眨眼动作，请保证清晰完整人脸完成动作",
      DetectResultCodeEnum.EYE_BLINK_IS_NOT_FOUND,
      FaceStatusCode.LIVENESS_EYE_BLINK_NOT_FOUND),

  HEAD_SHAKE_IS_NOT_FOUND(
      "未检测到摇头动作，请保证清晰完整人脸完成动作",
      DetectResultCodeEnum.HEAD_SHAKE_IS_NOT_FOUND,
      FaceStatusCode.LIVENESS_FIRST_ACTION_NOT_FOUND),

  EYE_IS_MASKED(
      "眼睛被遮挡", DetectResultCodeEnum.EYE_IS_MASKED, FaceStatusCode.LIVENESS_EYE_IS_MASKED),

  NOSE_IS_MASKED(
      "鼻子被遮挡", DetectResultCodeEnum.NOSE_IS_MASKED, FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),

  MOUTH_IS_MASKED(
      "嘴巴被遮挡", DetectResultCodeEnum.MOUTH_IS_MASKED, FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),

  REQUEST_IS_NOT_EXIST(
      "活体请求不存在",
      DetectResultCodeEnum.REQUEST_IS_NOT_EXIST,
      FaceStatusCode.PROVIDER_REQUEST_IS_NOT_EXIST),

  VIDEO_TOO_LONG(
          "视频录制时间过长",
          DetectResultCodeEnum.VIDEO_TOO_LONG,
          FaceStatusCode.LIVENESS_VIDEO_TOO_LONG),

  CAN_NOT_DETECT_FACE(
          "检测不到人脸",
          DetectResultCodeEnum.CAN_NOT_DETECT_FACE,
          FaceStatusCode.LIVENESS_CAN_NOT_DETECT_FACE),

  EYE_BLINK_IS_NOT_FOUND_MULTI_FACE(
          "检测到多张人脸",
          DetectResultCodeEnum.EYE_BLINK_IS_NOT_FOUND_MULTI_FACE,
          FaceStatusCode.LIVENESS_FOUND_MULTI_FACE),

  MOUTH_OPEN_IS_NOT_FOUND_MULTI_FACE(
          "检测到多张人脸",
          DetectResultCodeEnum.MOUTH_OPEN_IS_NOT_FOUND_MULTI_FACE,
          FaceStatusCode.LIVENESS_FOUND_MULTI_FACE),

  HEAD_SHAKE_IS_NOT_FOUND_MULTI_FACE(
          "检测到多张人脸",
          DetectResultCodeEnum.HEAD_SHAKE_IS_NOT_FOUND_MULTI_FACE,
          FaceStatusCode.LIVENESS_FOUND_MULTI_FACE),
  ;

  private String desc;
  private DetectResultCodeEnum detectResultCode;
  private FaceStatusCode faceCode;

  FaceActionDetectResultCodeAdapter(
      String desc, DetectResultCodeEnum detectResultCode, FaceStatusCode faceCode) {
    this.desc = desc;
    this.detectResultCode = detectResultCode;
    this.faceCode = faceCode;
  }

  public static FaceActionDetectResultCodeAdapter getStatusByCode(Integer adapterCode) {
    for (FaceActionDetectResultCodeAdapter status : FaceActionDetectResultCodeAdapter.values()) {
      DetectResultCodeEnum detectResultCode = status.detectResultCode;
      if (detectResultCode == null) {
        continue;
      }
      if (detectResultCode.getNCode().equals(adapterCode)) {
        return status;
      }
    }

    return FaceActionDetectResultCodeAdapter.INTEGRITY_CHECK_IS_NOT_PASS;
  }

  public boolean ifSuccess() {
    return this == OK;
  }

  public boolean ifFail() {
    return !ifSuccess();
  }

  public String getDesc() {
    return desc;
  }

  public DetectResultCodeEnum getDetectResultCode() {
    return detectResultCode;
  }

  public FaceStatusCode getFaceCode() {
    return faceCode;
  }

  @Override
  public String errCode() {
    return name();
  }

  @Override
  public String errMsg() {
    return desc;
  }
}
