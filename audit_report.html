<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>Java代码审计详细报告 - FaceUserResourceControlServiceImpl#modifyControlRecord</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #d32f2f;
            text-align: center;
            border-bottom: 3px solid #d32f2f;
            padding-bottom: 10px;
        }
        h2 {
            color: #1976d2;
            margin-top: 30px;
        }
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            min-width: 1200px;
        }
        th, td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: left;
            vertical-align: top;
            word-wrap: break-word;
        }
        th {
            background-color: #f0f0f0;
            font-weight: bold;
            color: #333;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        .high-risk { 
            background-color: #ffcdd2; 
            border-left: 5px solid #d32f2f;
        }
        .medium-risk { 
            background-color: #fff3cd; 
            border-left: 5px solid #f57f17;
        }
        .low-risk { 
            background-color: #d1ecf1; 
            border-left: 5px solid #17a2b8;
        }
        .risk-level {
            font-weight: bold;
            font-size: 18px;
            text-align: center;
        }
        .risk-10 { color: #d32f2f; }
        .risk-9 { color: #e53935; }
        .risk-8 { color: #f57c00; }
        .risk-7 { color: #ff9800; }
        .risk-6 { color: #ffc107; }
        .risk-5 { color: #ffeb3b; }
        .risk-4 { color: #8bc34a; }
        .risk-3 { color: #4caf50; }
        .risk-2 { color: #009688; }
        .risk-1 { color: #00bcd4; }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            border-left: 4px solid #007bff;
        }
        .summary {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 5px solid #1976d2;
        }
        .call-chain {
            background-color: #f3e5f5;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 5px solid #7b1fa2;
        }
        .mermaid-container {
            background-color: #fafafa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e0e0e0;
            text-align: center;
        }
        .code-snippet {
            font-size: 11px;
            line-height: 1.3;
            max-width: 300px;
            overflow: hidden;
        }
        .module-name {
            font-weight: bold;
            max-width: 120px;
        }
        .file-path {
            font-size: 11px;
            max-width: 200px;
            word-break: break-all;
        }
        .risk-desc {
            max-width: 250px;
            font-size: 12px;
        }
        .fix-suggestion {
            max-width: 200px;
            font-size: 11px;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</head>
<body>
    <div class="container">
        <h1>Java代码审计详细报告</h1>
        <h2>FaceUserResourceControlServiceImpl#modifyControlRecord 调用链安全分析</h2>
        
        <div class="summary">
            <h3>审计概要</h3>
            <p><strong>审计目标：</strong>com.timevale.faceauth.service.impl.api.FaceUserResourceControlServiceImpl#modifyControlRecord</p>
            <p><strong>审计时间：</strong>2025-08-04</p>
            <p><strong>风险等级分布：</strong></p>
            <ul>
                <li><span style="color: #d32f2f;">高风险（9-10级）：</span>3个</li>
                <li><span style="color: #f57f17;">中风险（5-8级）：</span>7个</li>
                <li><span style="color: #17a2b8;">低风险（1-4级）：</span>1个</li>
            </ul>
            <p><strong>关键发现：</strong>该调用链存在严重的权限控制缺失问题，任何人都可以修改控制记录，存在重大安全风险。</p>
        </div>

        <div class="call-chain">
            <h3>调用链路径</h3>
            <p>客户端请求 → FaceUserResourceControlServiceImpl#modifyControlRecord → FaceUserResourceControlInnerServiceImpl#modifyControlRecord → FaceUserResourceControlRecordDAO#modifyControlRecord → 数据库</p>
        </div>

        <div class="mermaid-container">
            <h3>调用链流程图</h3>
            <div class="mermaid">
graph TD
    A[客户端请求] --> B[FaceUserResourceControlServiceImpl#modifyControlRecord]
    B --> C[FaceAuthValidationUtils.notNull]
    B --> D[FaceAuthValidationUtils.validateBean]
    B --> E[FaceAuthValidationUtils.notEmpty]
    B --> F[FaceAuthValidationUtils.assertTrue]
    B --> G[FaceAuthValidationUtils.notBlank recordId]
    B --> H[FaceAuthValidationUtils.notBlank appId]
    B --> I[JsonUtils.obj2pojo]
    B --> J[FaceUserResourceControlInnerServiceImpl.parseProofDocuments]
    B --> K[FaceUserResourceControlInnerService.modifyControlRecord]

    K --> L[FaceUserResourceControlInnerServiceImpl#selectOneByRecordId]
    L --> M[FaceUserResourceControlRecordDAO.selectOneByRecordId]
    K --> N[FaceAuthValidationUtils.notNull]
    K --> O[FaceUserResourceControlRecordDAO.modifyControlRecord]

    O --> P[(数据库表: tb_face_user_resource_control_record)]

    style A fill:#e1f5fe
    style B fill:#fff3e0
    style K fill:#fff3e0
    style O fill:#ffebee
    style P fill:#f3e5f5

    classDef riskHigh fill:#ffcdd2,stroke:#d32f2f,stroke-width:3px
    classDef riskMedium fill:#fff9c4,stroke:#f57f17,stroke-width:2px
    classDef riskLow fill:#e8f5e8,stroke:#388e3c,stroke-width:1px

    class B,K riskHigh
    class O,J riskMedium
    class C,D,E,F,G,H,I,L,M,N riskLow
            </div>
        </div>

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th style="width: 100px;">模块名</th>
                        <th style="width: 200px;">文件路径</th>
                        <th style="width: 80px;">代码行号</th>
                        <th style="width: 300px;">风险代码片段</th>
                        <th style="width: 120px;">风险类别</th>
                        <th style="width: 250px;">风险描述及后果</th>
                        <th style="width: 60px;">风险等级</th>
                        <th style="width: 200px;">修复建议</th>
                    </tr>
                </thead>
            <tbody>
                <!-- 高风险问题 -->
                <tr class="high-risk">
                    <td class="module-name">API入口层</td>
                    <td class="file-path">FaceUserResourceControlServiceImpl.java</td>
                    <td>88-107</td>
                    <td class="code-snippet"><pre>@Override
public SupportResult&lt;BaseFaceAuthResult&gt;
modifyControlRecord(ControlRecordInput input) {
    // 仅有参数验证，无权限控制
    FaceAuthValidationUtils.notNull(input, ...);
    faceUserResourceControlInnerService
        .modifyControlRecord(recordDO);
    return SupportResult.success(null);
}</pre></td>
                    <td>越权访问风险</td>
                    <td class="risk-desc">该方法使用@RestService注解暴露为REST服务，但完全缺乏权限控制、身份验证和访问控制机制。任何人都可以调用此接口修改控制记录，可能导致未授权的数据修改和敏感信息泄露。</td>
                    <td><span class="risk-level risk-10">10</span></td>
                    <td class="fix-suggestion">1. 添加身份验证机制（如JWT token验证）<br>2. 实现基于角色的访问控制（RBAC）<br>3. 添加操作权限验证<br>4. 记录操作审计日志</td>
                </tr>

                <tr class="high-risk">
                    <td class="module-name">业务逻辑层</td>
                    <td class="file-path">FaceUserResourceControlInnerServiceImpl.java</td>
                    <td>132-137</td>
                    <td class="code-snippet"><pre>@Override
public void modifyControlRecord(
    FaceUserResourceControlRecordDO input) {
    FaceUserResourceControlRecordDO recordDO =
        this.selectOneByRecordId(input.getRecordId());
    FaceAuthValidationUtils.notNull(recordDO, ...);
    controlRecordDAO.modifyControlRecord(input);
}</pre></td>
                    <td>越权访问风险</td>
                    <td class="risk-desc">方法只验证记录是否存在，但不验证调用者是否有权限修改该记录。用户可以修改任意存在的控制记录，包括其他用户的记录，严重违反数据安全原则。</td>
                    <td><span class="risk-level risk-9">9</span></td>
                    <td class="fix-suggestion">1. 添加记录所有权验证<br>2. 验证操作者与记录创建者的关系<br>3. 实现字段级权限控制<br>4. 添加操作前的业务规则验证</td>
                </tr>

                <tr class="high-risk">
                    <td class="module-name">业务逻辑层</td>
                    <td class="file-path">FaceUserResourceControlInnerServiceImpl.java</td>
                    <td>132-137</td>
                    <td class="code-snippet"><pre>// 缺乏状态变更控制
controlRecordDAO.modifyControlRecord(input);</pre></td>
                    <td>大事务问题</td>
                    <td class="risk-desc">可以任意修改记录状态，没有状态变更的业务规则验证。可能导致业务状态不一致，绕过正常的业务流程，影响系统的业务完整性。</td>
                    <td><span class="risk-level risk-9">9</span></td>
                    <td class="fix-suggestion">1. 实现状态机控制状态变更<br>2. 添加状态变更的前置条件验证<br>3. 记录状态变更历史<br>4. 实现状态回滚机制</td>
                </tr>

                <!-- 中风险问题 -->
                <tr class="medium-risk">
                    <td class="module-name">输入验证层</td>
                    <td class="file-path">ControlRecordInput.java</td>
                    <td>72-97</td>
                    <td class="code-snippet"><pre>private List&lt;ProofDocumentDTO&gt; proofDocumentList;

@Data
public static class ProofDocumentDTO {
    private String fileKey; // 无长度限制
    @Deprecated
    private String downloadUrl;
}</pre></td>
                    <td>输入过载未防范</td>
                    <td class="risk-desc">fileKey字段没有长度限制，可能接受超大文件键值。攻击者可能通过提交超长字符串导致内存溢出或存储空间耗尽。</td>
                    <td><span class="risk-level risk-7">7</span></td>
                    <td class="fix-suggestion">1. 为fileKey字段添加长度限制注解<br>2. 实现文件大小验证<br>3. 添加总体数据量限制<br>4. 实现请求频率限制</td>
                </tr>

                <tr class="medium-risk">
                    <td class="module-name">数据访问层</td>
                    <td class="file-path">FaceUserResourceControlRecordDAO.java</td>
                    <td>126-129</td>
                    <td class="code-snippet"><pre>&lt;if test='mobileNo != null'&gt;
  mobile_no = #{mobileNo},&lt;/if&gt;
&lt;if test='status != null'&gt;
  status = #{status},&lt;/if&gt;</pre></td>
                    <td>组件初始无限制</td>
                    <td class="risk-desc">可以修改敏感字段如status，没有字段级别的权限控制。可能修改不应该被修改的敏感字段，导致业务逻辑被绕过。</td>
                    <td><span class="risk-level risk-7">7</span></td>
                    <td class="fix-suggestion">1. 实现字段级权限控制<br>2. 分离敏感字段的修改接口<br>3. 添加字段修改的审批流程<br>4. 记录字段修改历史</td>
                </tr>

                <!-- 其他中风险问题 -->
                <tr class="medium-risk">
                    <td class="module-name">业务逻辑层</td>
                    <td class="file-path">FaceUserResourceControlInnerServiceImpl.java</td>
                    <td>132-137</td>
                    <td class="code-snippet"><pre>// 整个方法没有并发控制
public void modifyControlRecord(...) {
    // ... 业务逻辑
}</pre></td>
                    <td>死锁问题</td>
                    <td class="risk-desc">没有并发控制机制，可能出现数据竞争。在高并发场景下可能导致数据不一致。</td>
                    <td><span class="risk-level risk-6">6</span></td>
                    <td class="fix-suggestion">1. 添加分布式锁机制<br>2. 实现乐观锁控制<br>3. 使用数据库事务隔离</td>
                </tr>

                <tr class="medium-risk">
                    <td class="module-name">输入验证层</td>
                    <td class="file-path">ControlRecordInput.java</td>
                    <td>62</td>
                    <td class="code-snippet"><pre>@NotBlank(message = "手机号码不能为空")
private String mobileNo;</pre></td>
                    <td>输入过载未防范</td>
                    <td class="risk-desc">手机号字段有@NotBlank注解但没有格式验证。可能接受无效的手机号格式。</td>
                    <td><span class="risk-level risk-5">5</span></td>
                    <td class="fix-suggestion">1. 添加手机号格式验证注解<br>2. 实现手机号归属地验证</td>
                </tr>

                <tr class="medium-risk">
                    <td class="module-name">异常处理层</td>
                    <td class="file-path">FaceAuthValidationUtils.java</td>
                    <td>25-32</td>
                    <td class="code-snippet"><pre>if (var2.hasNext()) {
    ConstraintViolation&lt;T&gt; bean =
        (ConstraintViolation)var2.next();
    throw FaceException.valueOf(...,
        bean.getMessage());
}</pre></td>
                    <td>异常处理不当</td>
                    <td class="risk-desc">验证失败时直接抛出包含详细错误信息的异常。可能向攻击者泄露系统内部结构信息。</td>
                    <td><span class="risk-level risk-5">5</span></td>
                    <td class="fix-suggestion">1. 统一异常信息格式<br>2. 过滤敏感信息<br>3. 记录详细错误到日志</td>
                </tr>

                <!-- 低风险问题 -->
                <tr class="low-risk">
                    <td class="module-name">数据访问层</td>
                    <td class="file-path">FaceUserResourceControlRecordDAO.java</td>
                    <td>122-134</td>
                    <td class="code-snippet"><pre>@Update({
    "&lt;script&gt;",
    "UPDATE tb_face_user_resource_control_record",
    "&lt;set&gt;",
    "   &lt;if test='mobileNo != null'&gt;
       mobile_no = #{mobileNo},&lt;/if&gt;",
    "&lt;/set&gt;",
    "WHERE record_id = #{recordId}",
    "&lt;/script&gt;"
})</pre></td>
                    <td>SQL相关问题</td>
                    <td class="risk-desc">使用了MyBatis参数绑定，有效防范了SQL注入。当前实现相对安全，但仍需关注参数验证的完整性。</td>
                    <td><span class="risk-level risk-2">2</span></td>
                    <td class="fix-suggestion">1. 继续保持参数绑定使用<br>2. 定期进行SQL注入安全测试<br>3. 添加SQL执行监控</td>
                </tr>
            </tbody>
            </table>
        </div>

        <h2>风险汇总与建议</h2>

        <div class="summary">
            <h3>关键风险点</h3>
            <ol>
                <li><strong>权限控制缺失（风险等级：10）：</strong>这是最严重的安全问题，必须立即修复。</li>
                <li><strong>越权访问风险（风险等级：9）：</strong>用户可以修改任意记录，严重违反安全原则。</li>
                <li><strong>状态控制缺失（风险等级：9）：</strong>可能导致业务逻辑被绕过。</li>
            </ol>
        </div>

        <div class="summary">
            <h3>修复优先级建议</h3>
            <ol>
                <li><strong>紧急修复（风险等级9-10）：</strong>
                    <ul>
                        <li>立即添加身份验证和权限控制机制</li>
                        <li>实现记录所有权验证</li>
                        <li>添加状态变更控制</li>
                    </ul>
                </li>
                <li><strong>高优先级修复（风险等级7-8）：</strong>
                    <ul>
                        <li>添加输入长度限制和文件大小验证</li>
                        <li>实现字段级权限控制</li>
                        <li>添加并发控制机制</li>
                    </ul>
                </li>
                <li><strong>中优先级修复（风险等级5-6）：</strong>
                    <ul>
                        <li>完善异常处理机制</li>
                        <li>添加数据库级别权限控制</li>
                        <li>实现输入格式验证</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="summary">
            <h3>安全架构建议</h3>
            <p>建议实施以下安全架构改进：</p>
            <ul>
                <li><strong>认证授权体系：</strong>集成统一的身份认证和授权系统</li>
                <li><strong>API网关：</strong>在API网关层实现统一的安全控制</li>
                <li><strong>审计日志：</strong>记录所有敏感操作的审计日志</li>
                <li><strong>安全测试：</strong>定期进行安全渗透测试和代码审计</li>
                <li><strong>监控告警：</strong>实现异常操作的实时监控和告警</li>
            </ul>
        </div>

        <p style="text-align: center; margin-top: 30px; color: #666; font-size: 14px;">
            报告生成时间：2025-08-04 | 审计工具：专业代码审计系统
        </p>
    </div>
</body>
</html>
