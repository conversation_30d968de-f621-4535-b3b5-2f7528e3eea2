<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EsignFaceController#faceInfo 方法风险分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
            color: #1e293b;
            line-height: 1.6;
            min-height: 100vh;
        }
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 60px;
            position: relative;
        }
        .header::before {
            content: '';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            border-radius: 2px;
        }
        h1 {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            letter-spacing: -0.02em;
        }
        .subtitle {
            color: #64748b;
            font-size: 1.1rem;
            font-weight: 400;
        }
        .card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 16px;
            padding: 32px;
            margin-bottom: 32px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
        }
        .card h2 {
            color: #3b82f6;
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .icon {
            width: 24px;
            height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .summary-item {
            background: rgba(248, 250, 252, 0.8);
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #3b82f6;
            text-align: center;
            border: 1px solid rgba(148, 163, 184, 0.1);
        }
        .summary-item .number {
            font-size: 2rem;
            font-weight: 700;
            color: #3b82f6;
            display: block;
        }
        .summary-item .label {
            color: #64748b;
            font-size: 0.9rem;
            margin-top: 8px;
        }
        .call-chain-diagram {
            background: rgba(248, 250, 252, 0.8);
            border-radius: 12px;
            padding: 24px;
            margin: 24px 0;
            overflow-x: auto;
            border: 1px solid rgba(148, 163, 184, 0.2);
        }
        .mermaid {
            background: transparent !important;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 24px;
            font-size: 14px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            overflow: hidden;
            border: 1px solid rgba(148, 163, 184, 0.2);
        }
        th, td {
            border: 1px solid rgba(148, 163, 184, 0.2);
            padding: 16px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        .high-risk {
            background: rgba(239, 68, 68, 0.1);
            border-left: 4px solid #ef4444;
        }
        .medium-risk {
            background: rgba(245, 158, 11, 0.1);
            border-left: 4px solid #f59e0b;
        }
        .low-risk {
            background: rgba(34, 197, 94, 0.1);
            border-left: 4px solid #22c55e;
        }
        .risk-level {
            font-weight: 700;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            display: inline-block;
            min-width: 32px;
            text-align: center;
        }
        .risk-level.high {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }
        .risk-level.medium {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
        }
        .risk-level.low {
            background: linear-gradient(135deg, #22c55e, #16a34a);
            color: white;
        }
        pre {
            background: rgba(248, 250, 252, 0.8);
            color: #1e293b;
            padding: 16px;
            border-radius: 8px;
            overflow-x: auto;
            font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
            font-size: 12px;
            line-height: 1.5;
            border: 1px solid rgba(148, 163, 184, 0.3);
        }
        .module-name {
            font-weight: 600;
            color: #3b82f6;
        }
        .file-path {
            font-family: 'JetBrains Mono', monospace;
            font-size: 11px;
            color: #64748b;
            word-break: break-all;
        }
        .risk-category {
            font-weight: 600;
            color: #7c3aed;
        }
        .line-number {
            font-family: 'JetBrains Mono', monospace;
            background: rgba(59, 130, 246, 0.1);
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 11px;
            color: #3b82f6;
        }
        .priority-list {
            list-style: none;
            padding: 0;
        }
        .priority-list li {
            background: rgba(248, 250, 252, 0.8);
            margin: 12px 0;
            padding: 16px;
            border-radius: 8px;
            border-left: 4px solid;
            display: flex;
            align-items: center;
            gap: 12px;
            border: 1px solid rgba(148, 163, 184, 0.2);
        }
        .priority-list li:nth-child(1) { border-left-color: #ef4444; }
        .priority-list li:nth-child(2) { border-left-color: #f59e0b; }
        .priority-list li:nth-child(3) { border-left-color: #f59e0b; }
        .priority-list li:nth-child(4) { border-left-color: #f59e0b; }
        .priority-list li:nth-child(5) { border-left-color: #22c55e; }
        .footer {
            margin-top: 60px;
            padding: 32px;
            text-align: center;
            background: rgba(248, 250, 252, 0.8);
            border-radius: 12px;
            border-top: 1px solid rgba(148, 163, 184, 0.2);
            border: 1px solid rgba(148, 163, 184, 0.2);
        }
        .footer p {
            color: #64748b;
            font-size: 0.9rem;
        }
        @media (max-width: 768px) {
            .container { padding: 20px 16px; }
            h1 { font-size: 2rem; }
            .card { padding: 20px; }
            .summary-grid { grid-template-columns: 1fr; }
            table { font-size: 12px; }
            th, td { padding: 12px 8px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>EsignFaceController#faceInfo</h1>
            <p class="subtitle">方法风险分析报告</p>
        </div>

        <div class="card">
            <h2>
                <span class="icon">🚨</span>
                风险概览
            </h2>
            <div class="summary-grid">
                <div class="summary-item">
                    <span class="number">6</span>
                    <div class="label">发现风险总数</div>
                </div>
                <div class="summary-item">
                    <span class="number">1</span>
                    <div class="label">高风险</div>
                </div>
                <div class="summary-item">
                    <span class="number">4</span>
                    <div class="label">中风险</div>
                </div>
                <div class="summary-item">
                    <span class="number">1</span>
                    <div class="label">低风险</div>
                </div>
            </div>
            <div style="margin-top: 24px; padding: 20px; background: rgba(239, 68, 68, 0.1); border-radius: 8px; border-left: 4px solid #ef4444;">
                <strong style="color: #ef4444;">⚠️ 最严重风险：</strong>
                <span style="color: #1e293b;">越权访问风险（等级9） - 攻击者可通过枚举faceId访问其他用户敏感信息</span>
            </div>
        </div>

        <div class="card">
            <h2>
                <span class="icon">🔗</span>
                调用链流程图
            </h2>
            <div class="call-chain-diagram">
                <div class="mermaid">
                    graph TD
                        A[🌐 HTTP Request<br/>faceInfo API] --> B{🔍 invalidParam<br/>参数验证}
                        B -->|Valid| C[💾 faceCache.queryContextWithFaceId<br/>查询缓存上下文]
                        B -->|Invalid| Z1[❌ 返回参数错误]

                        C --> D[🔧 TedisUtil.get<br/>Redis缓存查询]
                        D -->|Found| E{📊 EsignFaceStatusEnum.isFinish<br/>状态检查}
                        D -->|Not Found| Z2[❌ 返回上下文不存在]

                        E -->|Finished| Z3[❌ 返回流程已结束]
                        E -->|In Progress| F[🎯 livenessAuthorizationService<br/>获取活体动作]

                        F --> G[🔐 authorizationResolver.resolveLiveness<br/>权限解析]
                        G --> H[🗃️ livenessRepository.get<br/>数据库查询]

                        H --> I[🎭 CardUtils.hideIdNo/hideName<br/>数据脱敏]
                        I --> J[🎨 platformClient.getPageColor<br/>获取页面配置]

                        J --> K[📦 构建响应对象<br/>EsignFaceUserInfoResponse]
                        K --> L[✅ 返回成功响应]

                        style A fill:#f8fafc,stroke:#3b82f6,stroke-width:2px,color:#1e293b
                        style B fill:#f8fafc,stroke:#f59e0b,stroke-width:2px,color:#1e293b
                        style C fill:#f8fafc,stroke:#3b82f6,stroke-width:2px,color:#1e293b
                        style D fill:#f8fafc,stroke:#8b5cf6,stroke-width:2px,color:#1e293b
                        style E fill:#f8fafc,stroke:#f59e0b,stroke-width:2px,color:#1e293b
                        style F fill:#fef2f2,stroke:#ef4444,stroke-width:3px,color:#1e293b
                        style G fill:#fef2f2,stroke:#ef4444,stroke-width:3px,color:#1e293b
                        style H fill:#f8fafc,stroke:#f59e0b,stroke-width:2px,color:#1e293b
                        style I fill:#f0fdf4,stroke:#22c55e,stroke-width:2px,color:#1e293b
                        style J fill:#f8fafc,stroke:#3b82f6,stroke-width:2px,color:#1e293b
                        style K fill:#f0fdf4,stroke:#22c55e,stroke-width:2px,color:#1e293b
                        style L fill:#22c55e,stroke:#22c55e,stroke-width:2px,color:#ffffff
                        style Z1 fill:#ef4444,stroke:#ef4444,stroke-width:2px,color:#ffffff
                        style Z2 fill:#ef4444,stroke:#ef4444,stroke-width:2px,color:#ffffff
                        style Z3 fill:#ef4444,stroke:#ef4444,stroke-width:2px,color:#ffffff
                </div>
            </div>
        </div>

        <div class="card">
            <h2>
                <span class="icon">⚠️</span>
                风险详情分析
            </h2>
            <table>
            <thead>
                <tr>
                    <th style="width: 10%;">模块名</th>
                    <th style="width: 15%;">文件路径</th>
                    <th style="width: 6%;">代码行号</th>
                    <th style="width: 20%;">风险代码片段</th>
                    <th style="width: 8%;">风险类别</th>
                    <th style="width: 18%;">风险描述及后果</th>
                    <th style="width: 4%;">风险等级</th>
                    <th style="width: 19%;">修复建议</th>
                </tr>
            </thead>
            <tbody>
                <tr class="high-risk">
                    <td class="module-name">刷脸控制器模块</td>
                    <td class="file-path">/deploy/src/main/java/com/timevale/faceauth/controller/esignface/EsignFaceController.java</td>
                    <td class="line-number">73-76</td>
                    <td><pre>public BaseResult&lt;EsignFaceUserInfoResponse&gt; faceInfo(@PathVariable("faceId") String faceId) {
    if (invalidParam(faceId)) {
      return BaseResult.fail(FaceResultEnum.PARAM_INVALID);
    }</pre></td>
                    <td class="risk-category">越权访问风险</td>
                    <td>方法仅验证faceId参数是否存在于缓存中，但未进行任何权限校验。攻击者可以通过枚举或猜测faceId来访问其他用户的敏感刷脸信息，包括脱敏后的身份证号、姓名等个人信息。这违反了最小权限原则，可能导致大规模个人信息泄露。</td>
                    <td><span class="risk-level high">9</span></td>
                    <td>增加用户身份验证和授权机制，确保只有合法用户才能访问自己的刷脸信息。建议添加JWT token验证或session验证，并校验当前用户是否有权限访问指定的faceId。</td>
                </tr>
                <tr class="medium-risk">
                    <td class="module-name">缓存模块</td>
                    <td class="file-path">/service/src/main/java/com/timevale/faceauth/service/esignface/cache/EsignFaceCache.java</td>
                    <td class="line-number">28-32</td>
                    <td><pre>String json = TedisUtil.get(createFaceCacheKey(faceId));
if(StringUtils.isBlank(json)){
   return null;
}
return JSON.parseObject(json,EsignFaceContext.class);</pre></td>
                    <td class="risk-category">异常处理不当</td>
                    <td>Redis缓存访问和JSON反序列化过程中可能出现异常（如网络超时、序列化错误等），但代码未进行异常捕获和处理。当Redis不可用或数据损坏时，会导致系统抛出未处理异常，影响用户体验并可能暴露系统内部信息。</td>
                    <td><span class="risk-level medium">6</span></td>
                    <td>添加try-catch块处理Redis访问异常和JSON解析异常，记录错误日志并返回适当的错误响应。考虑添加缓存降级机制。</td>
                </tr>
                <tr class="medium-risk">
                    <td class="module-name">刷脸控制器模块</td>
                    <td class="file-path">/deploy/src/main/java/com/timevale/faceauth/controller/esignface/EsignFaceController.java</td>
                    <td class="line-number">82-97</td>
                    <td><pre>LivenessActionInput livenessActionInput = new LivenessActionInput();
livenessActionInput.setLivenessId(context.getLivenessId());
livenessActionInput.setPhoto(context.getPhoto());
livenessActionInput.setPhotoType(
    LivenessAuthorizationPhotoType.TYPE_HIGH_DEFINITION.getValue());
SupportResult&lt;String&gt; actionsSupportResult =
    livenessAuthorizationService.getLivenessActions(livenessActionInput);</pre></td>
                    <td class="risk-category">第三方供应商调用无预案</td>
                    <td>调用活体认证服务获取动作时，未设置超时时间和重试机制，也缺乏熔断和降级策略。当第三方活体认证服务出现故障或响应缓慢时，可能导致当前请求长时间阻塞，进而引发级联故障，影响整个刷脸服务的可用性。</td>
                    <td><span class="risk-level medium">8</span></td>
                    <td>为第三方服务调用添加超时配置、重试机制和熔断器。实现降级策略，当服务不可用时返回默认的活体动作或友好的错误提示。</td>
                </tr>
                <tr class="low-risk">
                    <td class="module-name">工具类模块</td>
                    <td class="file-path">/service/src/main/java/com/timevale/faceauth/service/utils/CardUtils.java</td>
                    <td class="line-number">124-135</td>
                    <td><pre>public static String hideName(String text) {
    if (StringUtils.isBlank(text)) {
        return text;
    }
    if (text.length() < 1) {
        return text;
    }
    String newText = HIDE_NAME_MARK+text.substring(text.length()-1, text.length()) ;
    return newText;
}</pre></td>
                    <td class="risk-category">数据脱敏不充分</td>
                    <td>姓名脱敏逻辑过于简单，仅保留最后一个字符并用"**"替换前面部分。对于两字姓名，这种脱敏方式信息泄露风险较高，攻击者仍可能通过最后一个字符推测出完整姓名。</td>
                    <td><span class="risk-level low">5</span></td>
                    <td>改进脱敏算法，对于不同长度的姓名采用不同的脱敏策略。建议两字姓名只显示姓氏，三字及以上姓名显示首尾字符，中间用星号替换。</td>
                </tr>
                <tr class="medium-risk">
                    <td class="module-name">刷脸控制器模块</td>
                    <td class="file-path">/deploy/src/main/java/com/timevale/faceauth/controller/esignface/EsignFaceController.java</td>
                    <td class="line-number">100-104</td>
                    <td><pre>String[] actionsArray = StringUtils.split(actionResult, EsignFaceConfig.SPLIT);
List&lt;Integer&gt; livenessActions = new ArrayList&lt;&gt;();
for (String action : actionsArray) {
  livenessActions.add(Integer.parseInt(action));
}</pre></td>
                    <td class="risk-category">输入过载未防范问题</td>
                    <td>对actionResult字符串进行分割和解析时，未验证分割后数组的长度和每个元素的有效性。如果actionResult包含大量数据或非法字符，可能导致内存消耗过大或NumberFormatException异常，影响系统稳定性。</td>
                    <td><span class="risk-level medium">7</span></td>
                    <td>添加输入验证，限制分割后数组的最大长度，并对每个action字符串进行格式验证。使用try-catch处理Integer.parseInt可能的异常。</td>
                </tr>
                <tr class="medium-risk">
                    <td class="module-name">活体认证解析器模块</td>
                    <td class="file-path">/service/src/main/java/com/timevale/faceauth/service/liveness/repository/resolver/LivenessAuthorizationResolver.java</td>
                    <td class="line-number">40-45</td>
                    <td><pre>public LivenessAuthorization resolveLiveness(String livenessId) {
    LivenessAuthorizationArgumentAssert.throwIfEmpty(livenessId, "livenessId");
    LivenessAuthorization liveness = livenessRepository.get(livenessId);
    checkLiveness(liveness, livenessId);
    return liveness;
}</pre></td>
                    <td class="risk-category">SQL相关问题</td>
                    <td>通过livenessId查询数据库时，未检查是否使用了合适的索引。如果livenessId字段未建立索引或索引效率低下，在高并发场景下可能导致数据库查询性能问题，影响系统响应时间。</td>
                    <td><span class="risk-level medium">6</span></td>
                    <td>确保livenessId字段建立了高效的数据库索引。监控数据库查询性能，对慢查询进行优化。考虑添加查询缓存机制减少数据库压力。</td>
                </tr>
            </tbody>
        </table>
        </div>

        <div class="card">
            <h2>
                <span class="icon">📋</span>
                修复优先级建议
            </h2>
            <ol class="priority-list">
                <li>
                    <span style="color: #ef4444; font-size: 1.2rem;">🔴</span>
                    <div>
                        <strong style="color: #ef4444;">紧急修复（风险等级9）：</strong>
                        <span style="color: #1e293b;">越权访问风险 - 立即添加用户身份验证和授权机制</span>
                    </div>
                </li>
                <li>
                    <span style="color: #f59e0b; font-size: 1.2rem;">🟡</span>
                    <div>
                        <strong style="color: #f59e0b;">高优先级（风险等级8）：</strong>
                        <span style="color: #1e293b;">第三方供应商调用无预案 - 添加超时、重试和熔断机制</span>
                    </div>
                </li>
                <li>
                    <span style="color: #f59e0b; font-size: 1.2rem;">🟡</span>
                    <div>
                        <strong style="color: #f59e0b;">中优先级（风险等级7）：</strong>
                        <span style="color: #1e293b;">输入过载未防范问题 - 添加输入验证和异常处理</span>
                    </div>
                </li>
                <li>
                    <span style="color: #f59e0b; font-size: 1.2rem;">🟡</span>
                    <div>
                        <strong style="color: #f59e0b;">中优先级（风险等级6）：</strong>
                        <span style="color: #1e293b;">异常处理不当、SQL相关问题 - 完善异常处理和数据库优化</span>
                    </div>
                </li>
                <li>
                    <span style="color: #22c55e; font-size: 1.2rem;">🟢</span>
                    <div>
                        <strong style="color: #22c55e;">低优先级（风险等级5）：</strong>
                        <span style="color: #1e293b;">数据脱敏不充分 - 改进脱敏算法</span>
                    </div>
                </li>
            </ol>
            <div style="margin-top: 24px; padding: 20px; background: rgba(59, 130, 246, 0.1); border-radius: 8px; border-left: 4px solid #3b82f6;">
                <strong style="color: #3b82f6;">💡 建议：</strong>
                <span style="color: #1e293b;">优先修复越权访问风险，这是最严重的安全漏洞。然后按照风险等级依次处理其他问题。</span>
            </div>
        </div>

        <div class="footer">
            <p>报告生成时间: 2025-08-04 | 分析工具: Augment Agent | 基于 cr-prompt.md 规则</p>
        </div>
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'base',
            themeVariables: {
                primaryColor: '#3b82f6',
                primaryTextColor: '#1e293b',
                primaryBorderColor: '#3b82f6',
                lineColor: '#64748b',
                secondaryColor: '#f8fafc',
                tertiaryColor: '#ffffff',
                background: '#ffffff',
                mainBkg: '#f8fafc',
                secondBkg: '#e2e8f0',
                tertiaryBkg: '#cbd5e1'
            },
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });

        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为风险等级添加悬停效果
            const riskLevels = document.querySelectorAll('.risk-level');
            riskLevels.forEach(level => {
                level.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.1)';
                    this.style.transition = 'transform 0.2s ease';
                });
                level.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // 为卡片添加悬停效果
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.borderColor = 'rgba(59, 130, 246, 0.4)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.borderColor = 'rgba(148, 163, 184, 0.2)';
                });
            });
        });
    </script>
</body>
</html>
